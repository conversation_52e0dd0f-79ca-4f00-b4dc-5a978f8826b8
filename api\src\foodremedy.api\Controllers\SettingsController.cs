using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ConfigModel = foodremedy.database.Models.ConfigModel;


namespace foodremedy.api.Controllers;

[ApiController]
[Produces("application/json")]
[Route("api/[controller]")]
[ProducesResponseType(StatusCodes.Status200OK)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class SettingsController : ControllerBase
{
    private readonly IConfigurationRepository _configRepo;

    public SettingsController(IConfigurationRepository configRepo)
    {
        Console.WriteLine("SettingsController initialized");
        _configRepo = configRepo;
    }


    [HttpGet]
    public async Task<ActionResult<PaginatedResponse<ConfigModel>>> GetAll(
     [FromQuery] PaginationRequest paginationRequest,
     [FromQuery] string? sortBy,
     [FromQuery] string? key) // <-- new
    {
        try
        {
            var configs = await _configRepo.GetAllAsync();

            // 🔍 Filter by search keyword (case-insensitive)
            if (!string.IsNullOrEmpty(key))
            {
                configs = configs
                    .Where(c => c.Key.Contains(key, StringComparison.OrdinalIgnoreCase));
            }

            var total = configs.Count();

            // Optional sorting
            if (!string.IsNullOrEmpty(sortBy))
            {
                configs = sortBy.ToLower() switch
                {
                    "key" => configs.OrderBy(c => c.Key),
                    "value" => configs.OrderBy(c => c.Value),
                    _ => configs
                };
            }

            var pagedConfigs = configs
                .Skip(paginationRequest.Skip)
                .Take(paginationRequest.Take)
                .ToList();

            var response = new PaginatedResponse<ConfigModel>(
                total,
                pagedConfigs.Count,
                pagedConfigs
            );

            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Exception in GetAll(): {ex.Message}");
        }
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] ConfigModel newConfig)
    {
        if (string.IsNullOrWhiteSpace(newConfig.Key))
            return BadRequest("Key is required.");

        var existing = await _configRepo.GetByKeyAsync(newConfig.Key);
        if (existing != null)
            return Conflict($"Configuration with key already exists.");

        await _configRepo.AddAsync(newConfig);
        await _configRepo.SaveChangesAsync();

        return CreatedAtAction(nameof(GetAll), new { key = newConfig.Key }, newConfig);
    }

    [HttpDelete("{key}")]
    public async Task<IActionResult> Delete(string key)
    {
        try
        {
            var config = await _configRepo.GetByKeyAsync(key);
            if (config == null)
                return NotFound(new { message = $"No configuration found for key: {key}" });

            await _configRepo.DeleteAsync(config);
            await _configRepo.SaveChangesAsync();

            return Ok(new { message = $"Configuration '{key}' deleted successfully." });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = $"An error occurred while deleting configuration: {ex.Message}" });
        }
    }


    [HttpPut("{key}")]
    public async Task<IActionResult> Update(string key, [FromBody] ConfigModel updatedConfig)
    {
        try
        {
            var existing = await _configRepo.GetByKeyAsync(key);
            if (existing == null)
                return NotFound(new { message = $"No configuration found for key: {key}" });

            existing.Value = updatedConfig.Value;

            await _configRepo.UpdateAsync(existing);
            await _configRepo.SaveChangesAsync();

            return Ok(new { message = $"Configuration '{key}' updated successfully." });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = $"An error occurred while updating configuration: {ex.Message}" });
        }
    }
}
