﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace foodremedy.database.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static void AddDatabase(this IServiceCollection services, string connectionString)
        {
            services.AddDbContext<FoodRemedyDbContext>(options =>
                options.UseMySql(connectionString, new MySqlServerVersion(new Version(9, 0, 0)))); // Adjust version if needed
        }
    }
}
