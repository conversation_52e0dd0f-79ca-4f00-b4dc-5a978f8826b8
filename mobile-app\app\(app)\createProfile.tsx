// Create User Profile tsx

import { View, ScrollView, Pressable, Alert } from "react-native";
import { useCallback, useState } from "react";
import { useRouter } from "expo-router";
import Tt from "@/components/ui/UIText";
import Input from "@/components/ui/UIInput";
import IconGeneral from "@/components/icons/IconGeneral";
import Header from "@/components/layout/Header";

const ALLERGIES = [
  "Peanuts", "Tree Nuts", "Shellfish", "Fish", "Eggs",
  "Milk", "Soy", "Wheat", "Sesame", "Mustard"
];
const INTOLERANCES = ["Lactose", "Gluten", "FODMAPs", "Fructose", "Sulfites",
  "Histamine", "Salicylates", "MSG", "Caffeine", "Artificial Sweeteners"
];
const DIETARIES = [
  "Vegetarian", "Vegan", "Pescatarian", "Keto", "Paleo",
  "Low-Carb", "Low-FODMAP", "Dairy-Free", "Gluten-Free", "Halal", "Kosher", "Organic"
];

type MultiSelectSectionProps = {
  title: string;
  items: string[];
  selected: string[];
  onToggle: (item: string) => void;
};

const MultiSelectSection: React.FC<MultiSelectSectionProps> = ({ title, items, selected, onToggle, }) => (
  <View className="mb-6">
    <Tt className="text-lg font-interMedium text-hsl25 mb-1">{title}</Tt>
    <View className="flex-row flex-wrap justify-start">
      {items.map((item, idx) => {
        const isSelected = selected.includes(item);
        return (
          <Pressable key={idx}
            onPress={() => onToggle(item)}
            className={`px-4 py-1 m-1 border rounded-full ${isSelected ? 'bg-primary border-primary' : 'bg-white border-hsl30'}`}
          >
            <Tt className={`font-interMedium ${isSelected ? 'text-white' : 'text-hsl30'}`}>
              {item}
            </Tt>
          </Pressable>
        );
      })}
    </View>
  </View>
);

export default function ProfileCreation() {
  const router = useRouter();
  const [username, setUsername] = useState<string>("");
  const [age, setAge] = useState<string>("");
  const [selectedAllergies, setAllergies] = useState<string[]>([]);
  const [selectedIntolerances, setIntolerances] = useState<string[]>([]);
  const [selectedDietaries, setDietaries] = useState<string[]>([]);

  /**
   * Toggle Handler using Functional Updates
   */
  const toggle = useCallback((setter: React.Dispatch<React.SetStateAction<string[]>>, item: string) => {
    setter(prev => prev.includes(item) ? prev.filter(i => i !== item) : [...prev, item]);
  }, []);

  /**
   * Handle Image
   * TODO: Temporary Function - Delete later
   * Modals like Image and camera will be opened and then camera and gallery will be opened based on the user preference.
   */
  const handleImageModal = () => {
    console.warn("Image Modal Opened.");
  }

  /**
   * Handle Finish
   * TODO: Temporary Function - Delete later
   * Alerts will be replaced with notifications
   */
  const handleFinish = () => {
    if (!username.trim()) {
      Alert.alert("Missing Information", "Please enter your username.");
    } else if (!age.trim()) {
      Alert.alert("Missing Information", "Please enter your age.");
    } else if (isNaN(Number(age))) {
      Alert.alert("Invalid Age", "Age must be a number.");
    } else {
      router.navigate("/(app)/(tabs)");
    }
  };

  return (
    <View className="p-safe flex-1">
      <Header />

      <ScrollView>
        <View className="w-[90%] self-center">
          <Tt className="text-xl font-interBold text-center my-4">Create Your Nutritional Profile</Tt>

          {/* <Image /> */}
          <Pressable onPress={handleImageModal} className="justify-center items-center my-2">
            <IconGeneral type="account" fill="hsl(0 0% 40%)" size={100} />
            <Tt className="text-xl font-interMedium text-left">Upload Picture</Tt>
          </Pressable>

          {/* Username Input */}
          <Input
            value={username}
            onChangeText={setUsername}
            placeholder="User Name"
            className="my-2 py-3"
          />

          {/* Age Input */}
          <Input
            value={age}
            onChangeText={setAge}
            placeholder="Age"
            keyboardType="number-pad"
            className="my-2 py-3 mb-8"
          />

          {/* Allergies Section */}
          <MultiSelectSection
            title="Select Allergies"
            items={ALLERGIES}
            selected={selectedAllergies}
            onToggle={item => toggle(setAllergies, item)}
          />

          {/* Intolerances Section */}
          <MultiSelectSection
            title="Select Intolerance"
            items={INTOLERANCES}
            selected={selectedIntolerances}
            onToggle={item => toggle(setIntolerances, item)}
          />

          {/* Dietary Form */}
          <MultiSelectSection
            title="Dietary Form"
            items={DIETARIES}
            selected={selectedDietaries}
            onToggle={item => toggle(setDietaries, item)}
          />

        </View>
      </ScrollView>

      {/* Finish Button */}
      <Pressable
        onPress={handleFinish}
        hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
        className="w-[90%] self-center py-3 px-4 my-4 rounded-lg border bg-primary border-hsl90 active:bg-transparent active:border-primary"
      >
        {({ pressed }) => (
          <Tt className={`text-lg text-center font-interSemiBold ${pressed ? 'text-primary' : 'text-white'}`}>
            Save
          </Tt>
        )}
      </Pressable>
    </View>
  );
}