import IconGeneral from "@/components/icons/IconGeneral";
import Header from "@/components/layout/Header";
import Tt from "@/components/ui/UIText";
import { router } from "expo-router";
import { Pressable, ScrollView, View, Text } from "react-native";

/**
 * TODO
 * Delete mock data
 */
const members = [
  { id: 1, name: "<PERSON>", relationship: "<PERSON>" },
  { id: 2, name: "<PERSON>", relationship: "<PERSON>" },
  { id: 3, name: "<PERSON><PERSON>", relationship: "Friend" },
  { id: 4, name: "<PERSON><PERSON><PERSON>", relationship: "Father" },
];

export default function MembersPage() {
  /**
   * TODO
   * Add Member context api
   * Set member in provider
   */

  /**
   * Handle Edit Existing Member
   * @param id 
   */
  const handleEditMember = (id?: number) => {
    router.push("/(app)/membersEdit");
  };

  return (
    <View className="flex-1 p-safe">
      <Header />

      <View className="w-[95%] self-center">

        <View className="flex-row items-center justify-between mb-4">
          <Pressable onPress={() => router.back()}
            className="flex-row justify-center items-center self-end px-2 py-1">
            {({ pressed }) => (
              <IconGeneral type="arrow-backward-ios" fill={pressed ? "#FF3F3F" : "hsl(0 0%, 30%)"} />
            )}
          </Pressable>
          <Tt className="font-interBold text-xl">Household Members</Tt>
          <IconGeneral type="arrow-backward-ios" fill="hsl(0 0%, 95%)" />
        </View>

        <Pressable onPress={() => handleEditMember()}
          className="flex-row justify-center items-center self-end bg-white border border-hsl80 rounded px-2 py-1 gap-x-2
                  active:border-primary">
          {({ pressed }) => (
            <>
              <IconGeneral type="member-add" fill={pressed ? "#FF3F3F" : "hsl(0 0%, 30%)"} />
              <Tt className={`font-medium text-lg ${pressed ? 'text-primary' : 'text-hsl30'}`}>Add Member</Tt>
            </>
          )}
        </Pressable>

        <ScrollView>
          {members.map((member) => (
            <View key={member.id} className="flex-row items-center w-full py-4 my-1">

              {/* ✅ Updated: Red Avatar Circle + White Text */}
              <View className="w-[50px] h-[50px] rounded-full bg-red-500 flex justify-center items-center">
                <Tt className="font-interSemiBold text-xl text-white">{member.name[0]}</Tt>
              </View>

              <View className="px-2 flex-grow">
                <Tt className="font-interBold">{member.name}</Tt>
                <Tt>{member.relationship}</Tt>
              </View>

              <Pressable onPress={() => handleEditMember(member.id)}
                className="flex-row justify-center items-center bg-white border border-hsl80 rounded px-2 py-1 gap-x-2
                  active:border-primary">
                {({ pressed }) => (
                  <>
                    <IconGeneral type="member-edit" fill={pressed ? "#FF3F3F" : "hsl(0 0%, 30%)"} />
                    <Tt className={`font-medium text-lg ${pressed ? 'text-primary' : 'text-hsl30'}`}>Edit</Tt>
                  </>
                )}
              </Pressable>

            </View>
          ))}
        </ScrollView>

      </View>
    </View>
  );
}
