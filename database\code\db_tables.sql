DROP TABLE IF EXISTS `nutrient_list`, `nutrient`, `ingredient`, `recipy`, `allergy_list`, `allergy`, `medical_list`, `medical`, `class_list`, `classification`, `supplier_list`, `supplier`, `food`;

CREATE TABLE food(
	food_id	int NOT NULL PRIMARY KEY AUTO_INCREMENT,
    food_name varchar(50) NOT NULL,
    food_image varchar(255),
    food_details varchar(1000),
    food_season char(10),
    food_energyfiber int default 0,
    food_energynfiber int default 0,
    serving_size int default 0);

CREATE TABLE supplier(
	supplier_id int NOT NULL PRIMARY KEY AUTO_INCREMENT,
    supplier_name char(255) NOT NULL,
    supplier_staddress varchar(255),
    supplier_suburb char(20),
    supplier_postcode int,
    supplier_state char(20),
    supplier_phone_number varchar(10) NOT NULL,
    supplier_email varchar(255) NOT NULL);
    
CREATE TABLE supplier_list(
	food_id int NOT NULL,
    supplier_id int NOT NULL,
    date_update date,
    price decimal(5,2) default 0.00,
    availability bool,
    
    foreign key(food_id)
		references food(food_id)
        on update cascade 
        on delete cascade,
	
    foreign key(supplier_id)
		references supplier(supplier_id)
        on update cascade on delete cascade);
    
CREATE TABLE classification(
	class_id int NOT NULL PRIMARY KEY AUTO_INCREMENT,
    class_name char(10) NOT NULL,
    class_desc varchar(1000));
    
CREATE TABLE class_list(
	food_id int NOT NULL,
    class_id int NOT NULL,
    
    foreign key(food_id)
		references food(food_id)
        on update cascade
        on delete cascade,
	
    foreign key(class_id)
		references classification(class_id)
        on update cascade
        on delete cascade);
    
CREATE TABLE allergy(
	allergy_id integer NOT NULL PRIMARY KEY AUTO_INCREMENT,
    allergy_name char(50) NOT NULL,
    allergy_desc varchar(1000));
    
CREATE TABLE allergy_list(
	food_id int NOT NULL,
    allergy_id int NOT NULL,
    
    foreign key(food_id)
		references food(food_id)
        on update cascade
        on delete cascade,
	
    foreign key(allergy_id)
		references allergy(allergy_id)
        on update cascade
        on delete cascade);

CREATE TABLE nutrient(
	nutrient_id integer NOT NULL PRIMARY KEY AUTO_INCREMENT,
    nutrient_name char(20) NOT NULL,
    nutrient_desc varchar(1000));
    
CREATE TABLE nutrient_list(
	food_id int NOT NULL,
    nutrient_id int NOT NULL,
    nutrient_amount decimal(5,2) default 0.00,
    
    foreign key(food_id)
		references food(food_id)
        on update cascade
        on delete cascade,
	
    foreign key(nutrient_id)
		references nutrient(nutrient_id)
        on update cascade
        on delete cascade);

CREATE TABLE Profile (
    Id CHAR(36) CHARACTER SET ascii NOT NULL PRIMARY KEY,
    UserId CHAR(36) CHARACTER SET ascii NOT NULL,
    FirstName VARCHAR(50) NOT NULL,
    LastName VARCHAR(50) NOT NULL,
    Status BOOLEAN NOT NULL DEFAULT TRUE,
    Relationship VARCHAR(50),
    Age INT,
    AvatarUrl VARCHAR(255),
    Allergies JSON,
    Intolerances JSON,
    DietaryForm VARCHAR(100),
    FOREIGN KEY (UserId) REFERENCES User(Id)
        ON DELETE CASCADE
        ON UPDATE CASCADE
);

SELECT "** Created API Schema" as "";
