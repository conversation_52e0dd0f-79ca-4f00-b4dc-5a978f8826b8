<?xml version="1.0"?>
<doc>
    <assembly>
        <name>foodremedy.api</name>
    </assembly>
    <members>
        <member name="M:foodremedy.api.Controllers.AuthenticationController.AttemptLogin(foodremedy.api.Models.Requests.AttemptLogin)">
            <summary>
            Logs the user in
            </summary>
            <param name="attemptLogin">User login details</param>
            <returns> User login to API</returns>
            <response code="200">Logs the user in</response>
            <response code="400">Login is null</response>
            <response code="401">Login details are incorrect</response>
        </member>
        <member name="M:foodremedy.api.Controllers.AuthenticationController.RefreshAccessToken(foodremedy.api.Models.Requests.RefreshAccessToken)">
            <summary>
            Refreshes API access token
            </summary>
            <param name="refreshAccessToken">Refresh access token</param>
            <returns> New access token</returns>
            <response code="200">Refreshes access token</response>
            <response code="400">Access token is null</response>
            <response code="401">Access token is incorrect or if the user is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.AuthenticationController.CreateApiKey(System.Guid,foodremedy.api.Models.Requests.CreateApiKey)">
            <summary>
            Creates Api Key
            </summary>
            <param name="userId">Id of the user</param>
            <param name="createApiKey">Details for Api key creation</param>
            <returns> Creates new Api Key</returns>
            <response code="200">Creates a new Api Key</response>
            <response code="404">User does not exist</response>
            <response code="400">User is disabled or createApiKey is null</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.AuthenticationController.GetApiKeys(foodremedy.api.Models.Requests.PaginationRequest)">
            <summary>
            Gets all current API Keys
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <returns> List of current API Keys</returns>
            <response code="200">Returns the list of API Keys</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.AuthenticationController.DeleteApiKey(System.Guid)">
            <summary>
            Disables an API Key
            </summary>
            <param name="apiKeyId">ID of the API Key</param>
            <response code="204">Disables the Api key</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Api key does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.GetFoods(foodremedy.api.Models.Requests.PaginationRequest,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a list of all foods
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <param name="foodSeason">The season to filter by</param>
            <param name="nutrientName">The nutrient to filter by</param>
            <param name="tagName">The tag to filter by</param>
            <param name="sortBy">The parameter value to sort by in ascending order (name, season, description, energywithfibre, energywithoutfibre, servingsize)</param> 
            <response code="200">Returns the list of Foods</response>
            <response code="404">One of the filtering paramters does not exist</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.GetFood(System.Guid)">
            <summary>
            Gets a food by ID
            </summary>
            <param name="foodId">The ID of the food</param>
            <returns> Food associated with inputted ID</returns>
            <response code="200">Returns the food</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Food does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.GetFoodByName(System.String)">
            <summary>
            Gets foods by name
            </summary>
            <param name="name">The name of the food</param>
            <returns> The foods associated with the name</returns>
            <response code="200">Returns the foods</response>
            <response code="401">User is not authenticated</response>
            <response code="404">No food has that name</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.CreateFood(foodremedy.api.Models.Requests.CreateFood)">
            <summary>
            Creates a new food
            </summary>
            <param name="createFood">The food to be added</param>
            <returns> The successfully created food</returns>
            <response code="201">Returns the created food</response>
            <response code="401">User is not authenticated</response>
            <response code="400">The createFood is null or if one of its properties is invalid</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.UpdateFood(System.Guid,foodremedy.api.Models.Requests.CreateFood)">
            <summary>
            Updates a food
            </summary>
            <param name="foodId">Current ID of the food</param>
            <param name="updateFood">The updated food details</param>
            <returns>The successfully updated food</returns>
            <response code="201">Returns the updated food</response>
            <response code="401">User is not authenticated</response>
            <response code="400">The updateFood is null or if one of its properties is invalid</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.DeleteFood(System.Guid)">
            <summary>
            Deletes a food
            </summary>
            <param name="foodId">The ID of the food to be deleted</param>
            <response code="204">Deletes food</response>
            <response code="404">Food does not exist</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.FoodsController.GetFoodBySeason(System.String)">
            <summary>
            Gets all foods by season
            </summary>
            <param name="season">The season to filter by</param>
            <returns>The list of foods associated with the season</returns>
            <response code="200">Returns the list of foods</response>
            <response code="400">Season parameter cannot be empty</response>
            <response code="404">No foods found for the season</response>
        </member>
        <member name="M:foodremedy.api.Controllers.NutrientsController.GetNutrients(foodremedy.api.Models.Requests.PaginationRequest,System.String)">
            <summary>
            Gets the list of nutrients
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <param name="sortBy">The property to sort the result by</param>
            <returns> Gets a list of nutrients</returns>
            <response code="200">The list of Nutrients was found and returned</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.NutrientsController.GetNutrient(System.Guid)">
            <summary>
            Gets a nutrient using it's ID
            </summary>
            <param name="nutrientId">The ID of the nutrient</param>
            <returns> Success message</returns>
            <response code="200">Nutrient with that ID was found and returned</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Nutrient does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.NutrientsController.GetNutrientByName(System.String)">
            <summary>
            Gets a nutrient by name
            </summary>
            <param name="name">The name of the nutrient</param>
            <returns> The nutrient associated with the name</returns>
            <response code="200">Nutrient with that name was found and returned</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Nutrient does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.NutrientsController.CreateNutrient(foodremedy.api.Models.Requests.CreateNutrient)">
            <summary>
            Creates a new nutrient
            </summary>
            <param name="createNutrient">The nutrient to be added</param>
            <returns> Successfully created nutrient</returns>
            <response code="201">Returns created message</response>
            <response code="401">User is not authenticated</response>
            <response code="400">The createNutrient is null</response>
            <response code="409">Nutrient already has that name</response>
        </member>
        <member name="M:foodremedy.api.Controllers.NutrientsController.UpdateNutrient(System.Guid,foodremedy.api.Models.Requests.CreateNutrient)">
            <summary>
            Updates an existing nutrient
            </summary>
            <param name="nutrientId">The ID of the nutrient</param>
            <param name="updateNutrient">The updated nutrient details</param>
            <returns> The successfully updated nutrient</returns>
            <response code="201">Nutrient is updated</response>
            <response code="404">No nutrient by this name exists</response>
            <response code="401">User is not authenticated</response>
            <response code="400">A nutrient by that name already exists or no changes have been made</response>
        </member>
        <member name="M:foodremedy.api.Controllers.NutrientsController.DeleteNutrient(System.Guid)">
            <summary>
            Deletes an exisiting nutrient
            </summary>
            <param name="nutrientId">The ID of the nutrient</param>
            <response code="204">Nutrient successfully deleted</response>
            <response code="404">Nutrient does not exist</response>
            <response code="400">The nutrientId is null</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.ProfilesController.CreateProfile(foodremedy.api.Models.Requests.ProfileRequest)">
            <summary>
            Creates a new profile
            </summary>
            <param name="createProfileRequest">Profile details to be added</param>
            <returns>Successfully created profile</returns>
            <response code="201">Profile created</response>
            <response code="400">The request is invalid</response>
            <response code="401">User is not authenticated</response>
            <response code="404">User not found</response>
            <response code="409">A profile already exists for this user</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:foodremedy.api.Controllers.ProfilesController.GetProfile(System.Guid)">
            <summary>
            Gets a profile by ID
            </summary>
            <param name="profileId">ID of the profile to retrieve</param>
            <returns>Profile associated with the ID</returns>
            <response code="200">Returns the profile</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Profile not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:foodremedy.api.Controllers.ProfilesController.GetProfiles(foodremedy.api.Models.Requests.PaginationRequest,System.String)">
            <summary>
            Gets a paginated list of profiles
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <param name="sortby">The property to sort by</param>
            <returns>List of profiles</returns>
            <response code="200">Returns the list of profiles</response>
            <response code="401">User is not authenticated</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:foodremedy.api.Controllers.ProfilesController.UpdateProfile(System.Guid,foodremedy.api.Models.Requests.ProfileRequest)">
            <summary>
            Updates a profile
            </summary>
            <param name="profileId">ID of the profile to update</param>
            <param name="updateProfileRequest">Profile details to update</param>
            <returns>Updated profile</returns>
            <response code="200">Profile updated successfully</response>
            <response code="400">The request is invalid</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Profile not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:foodremedy.api.Controllers.ProfilesController.DeleteProfile(System.Guid)">
            <summary>
            Deletes a profile
            </summary>
            <param name="profileId">ID of the profile to delete</param>
            <returns>Success message</returns>
            <response code="200">Profile deleted successfully</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Profile not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagCategoriesController.GetCategories(foodremedy.api.Models.Requests.PaginationRequest)">
            <summary>
            Gets a list of tag categories
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <returns> List of tag categories</returns>
            <response code="200">Returns the list of Categories</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagCategoriesController.GetCategory(System.Guid)">
            <summary>
            Gets a category by ID
            </summary>
            <param name="categoryId">The ID of the category</param>
            <returns> Category associated with inputted ID</returns>
            <response code="200">Returns the category</response>
            <response code="401">User is not authenticated</response>
            <response code="404">category does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagCategoriesController.CreateTagCategory(foodremedy.api.Models.Requests.CreateTagCategory)">
            <summary>
            Creates a new tag category
            </summary>
            <param name="createTagCategory">Tag Category to be added</param>
            <returns> Successfullt created tag category</returns>
            <response code="201">Returns created message</response>
            <response code="401">User is not authenticated</response>
            <response code="400">The createTagCategory is null</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagCategoriesController.UpdateTagCategory(System.Guid,foodremedy.api.Models.Requests.UpdateTagCategory)">
            <summary>
            Updates an existing tag category
            </summary>
            <param name="id">The ID of the tag category</param>
            <param name="updateTagCategory">The updated tag category details</param>
            <returns> The successfully updated tag category</returns>
            <response code="201">Tag category is updated</response>
            <response code="404">No tag category by this ID exists</response>
            <response code="401">User is not authenticated</response>
            <response code="400">A tag category by that name already exists or no changes have been made</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagCategoriesController.DeleteTagCategory(System.Guid)">
            <summary>
            Deletes an existing tag category
            </summary>
            <param name="id">The ID of the tag category</param>
            <response code="204">Tag category successfully deleted</response>
            <response code="404">Tag category does not exist</response>
            <response code="400">The id is null</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagsController.Get(foodremedy.api.Models.Requests.PaginationRequest,System.String)">
            <summary>
            Gets a list af all tags
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <param name="sortBy">The property to sort the result by</param>
            <returns> List of tags</returns>
            <response code="200">Returns the list of Tags</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagsController.CreateTag(foodremedy.api.Models.Requests.CreateTag,System.Guid)">
            <summary>
            Creates a new tag
            </summary>
            <param name="createTag">Tag to be added</param>
            <param name="tagCategoryId">ID of the new tag</param>
            <returns> Successfully created tag</returns>
            <response code="201">Tag created</response>
            <response code="400">The createTag is null</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Tag category does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagsController.GetTagsByCategory(System.String,foodremedy.api.Models.Requests.PaginationRequest,System.String)">
            <summary>
            Gets a list of tags by category
            </summary>
            <param name="tagCategoryName">The name of the category to search for</param>
            <param name="paginationRequest">Used to divide results into pages</param>
            <param name="sortBy">The property to sort the result by</param>
            <returns> List of tags by category</returns>
            <response code="200">Returns the list of Tags associated with the category</response>
            <response code="401">User is not authenticated</response>
            <response code="404">Tag category does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagsController.GetById(System.Guid)">
            <summary>
            Gets a specific tag by its ID.
            </summary>
            <param name="tagId">The ID of the tag to retrieve.</param>
            <returns>The retrieved tag, or a 404 Not Found response if the tag is not found.</returns>
        </member>
        <member name="M:foodremedy.api.Controllers.TagsController.UpdateTag(System.Guid,foodremedy.api.Models.Requests.CreateTag)">
            <summary>
            Updates an existing tag
            </summary>
            <param name="tagId">ID of the tag</param>
            <param name="updateTag">The updated tag details</param>
            <returns> Successfully updated Tag</returns>
            <response code="201">Tag updated</response>
            <response code="400">The createTag is null, the name already exists or no changes have been made</response>
            <response code="404">Tag does not exist</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.TagsController.DeleteTag(System.Guid)">
            <summary>
            Deletes an existing tag
            </summary>
            <param name="tagId">ID of the tag to delete</param>
            <response code="201">Tag deleted</response>
            <response code="404">Tag does not exist</response>  
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.RegisterUser(foodremedy.api.Models.Requests.RegisterUser)">
            <summary>
            Creates a new user
            </summary>
            <param name="registerUser">User details to be added</param>
            <returns> Successfully created user</returns>
            <response code="200">User created</response>
            <response code="400">The createUser is null</response>
            <response code="401">User is not authenticated</response>
            <response code="409">A user already exists with this email or username</response>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.GetUsers(foodremedy.api.Models.Requests.PaginationRequest,System.String,System.String)">
            <summary>
            Gets a list of users
            </summary>
            <param name="paginationRequest">Used to divide results into pages</param>
            <param name="sortby">The property to sort by</param>
            <param name="status">Status to filter by</param>
            <returns> List of users</returns>
            <response code="201">Returns the list of Users</response>
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.GetUser(System.Guid)">
            <summary>
            Gets user by ID
            </summary>
            <param name="userId">ID of the user to search for</param>
            <returns> User associated with the ID</returns>
            <response code="200">Returns the User with the userId</response>
            <response code="404">User does not exist</response>   
            <response code="401">User is not authenticated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.UpdateUser(System.Guid,foodremedy.api.Models.Requests.RegisterUser)">
            <summary>
            Updates an exisiting user
            </summary>
            <param name="userId">ID of the user to update</param>
            <param name="updatedUser">The updated user details</param>
            <returns>Successfully updated user</returns>
            <response code="200">User updated</response>
            <response code="409">Password not changed</response>
            <response code="409">User already exists with this username</response>
            <response code="401">User is not authenticated as the user being updated</response>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.DeleteUser(System.Guid,System.String)">
            <summary>
            Enables/disables an existing user
            </summary>
            <param name="userId">ID of the user</param>
            <param name="status">Desired status of the user (Disable/Activate)</param>
            <response code="204">User enabled/disabled</response>
            <response code="401">If the user is not authenticated</response>
            <response code="404"> If the user does not exist</response>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.ActivateUser(System.Guid)">
            <summary>
            Activates a user account.
            </summary>
        </member>
        <member name="M:foodremedy.api.Controllers.UsersController.DeactivateUser(System.Guid)">
            <summary>
            Deactivates a user account.
            </summary>
        </member>
        <member name="M:foodremedy.api.Extensions.ServiceCollectionExtensions.AddDefaultClaimsPrincipalFactory(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the default claims principal factory.
            </summary>
            <remarks>
            Creates a principal with the single claim of the owner ID.
            </remarks>
            <param name="services">The container to add the services to.</param>
            <returns>The service container for further chaining.</returns>
        </member>
        <member name="T:foodremedy.api.Services.IApiKeysCacheService">
            <summary>
            Service used to resolve and invalidate API keys.
            </summary>
        </member>
        <member name="M:foodremedy.api.Services.IApiKeysCacheService.GetOwnerIdFromApiKey(System.String)">
            <summary>
            Gets a API Key Owner ID (owner of the API key) from its API Key.
            </summary>
            <param name="apiKey">The API Key received on the HTTP request.</param>
            <returns>The ID of the owner of the API Key if the key was found, null otherwise.</returns>
        </member>
        <member name="M:foodremedy.api.Services.IApiKeysCacheService.InvalidateApiKey(foodremedy.database.Models.ApiKey)">
            <summary>
            Invalidates (removes from cache and/or permanent storage) an API key.
            </summary>
            <param name="apiKey">The API Key to invalidate</param>
            <returns>A task representing the operation.</returns>
        </member>
        <member name="T:foodremedy.api.Utils.ApiKeyAuthenticationHandler">
            <summary>
            Authentication handler for API Keys. 
            Validates the API Key header format and that an API key can be found by <see cref="T:foodremedy.api.Services.IApiKeysCacheService"/>.
            </summary>
        </member>
        <member name="T:foodremedy.api.Utils.ApiKeyAuthenticationOptions">
            <summary>
            Contains the options needed by <see cref="T:foodremedy.api.Utils.ApiKeyAuthenticationHandler"/>
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.HeaderName">
            <summary>
            Gets or sets the name of the header used for the API Key. 
            <para></para>
            If not set, the default value is "x-api-key".
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.DefaultScheme">
            <summary>
            Gets or sets the name of the scheme used for authenticating API keys.
            <para></para>
            If not set, the default value is "ApiKey".
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.NoApiKeyHeaderLog">
            <summary>
            Gets or sets the log configuration for the event when a request is received without a valid API Key header. 
            <para></para>
            A single parameter is expected for the header name. If null, no log is written.
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.InvalidApiKeyLog">
            <summary>
            Gets or sets the log configuration for the event when a request is received without a valid API Key header. 
            <para></para>
            A single parameter is expected for the API Key received. If null, no log is written.
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.ApiKeyOwnerIdLogScopeName">
            <summary>
            Gets or sets the name of the log scope used for the owner ID of the API Key. 
            <para></para>
            If not set, the default value is "{ApiKeyOwnerId}". If null, no log scope is used.
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.ValidApiKeyLog">
            <summary>
            Gets or sets the log configuration for the event when a request is received with a valid API Key header. 
            <para></para>
            No parameter is expected. If null, no log is written.
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyAuthenticationOptions.FailureMessage">
            <summary>
            Gets or sets the message used to return an error for an invalid API key (or lack thereof).
            <para></para>
            If not set, the default value is "Invalid API Key". Must not be null.
            </summary>
        </member>
        <member name="T:foodremedy.api.Utils.ApiKeyGenerationOptions">
            <summary>
            Contains options used to generate API Keys by the default factory.
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyGenerationOptions.KeyPrefix">
            <summary>
            Gets or sets a prefix for the keys. For example: "EX-"
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyGenerationOptions.LengthOfKey">
            <summary>
            Gets or sets the desider final length for an API key.
            </summary>
        </member>
        <member name="P:foodremedy.api.Utils.ApiKeyGenerationOptions.GenerateUrlSafeKeys">
            <summary>
            Gets or sets whether to generate URL-safe keys. Base64 is used if false.
            </summary>
        </member>
        <member name="M:foodremedy.api.Utils.ApiKeyGenerationOptions.Validate">
            <summary>
            Validates the configuration, to allow settings to be mapped with Microsoft.Extensions.Configuration.
            </summary>
            <exception cref="T:System.InvalidOperationException">When <see cref="!:ByteCountToGenerate"/> or <see cref="P:foodremedy.api.Utils.ApiKeyGenerationOptions.LengthOfKey"/> are below 1.</exception>
        </member>
        <member name="M:foodremedy.api.Utils.IApiKeyFactory.GenerateApiKey">
            <summary>
            Generates an API Key.
            </summary>
            <returns>The API Key generated.</returns>
        </member>
        <member name="T:foodremedy.api.Utils.DefaultApiKeyFactory">
            <summary>
            Default implementation for the <see cref="T:foodremedy.api.Utils.IApiKeyFactory"/> service. 
            Uses <see cref="T:foodremedy.api.Utils.ApiKeyGenerationOptions"/> to generate keys with the secure <see cref="T:System.Security.Cryptography.RandomNumberGenerator"/>.
            </summary>
        </member>
        <member name="T:foodremedy.api.Utils.IClaimsPrincipalFactory">
            <summary>
            Service used to generate <see cref="T:System.Security.Claims.ClaimsPrincipal"/>s.
            </summary>
        </member>
        <member name="M:foodremedy.api.Utils.IClaimsPrincipalFactory.CreateClaimsPrincipal(System.String)">
            <summary>
            Creates a claims principal to use for authenticating a request.
            </summary>
            <param name="apiKeyOwnerId">The ID of the owner of the API Key.</param>
            <returns>A <see cref="T:System.Security.Claims.ClaimsPrincipal"/> that represents the entity that initiated the HTTP request.</returns>
        </member>
        <member name="T:foodremedy.api.Utils.DefaultClaimsPrincipalFactory">
            <summary>
            Default implementation for the <see cref="T:foodremedy.api.Utils.IClaimsPrincipalFactory"/> service.
            Creates a principal with the single claim of the owner ID.
            </summary>
        </member>
    </members>
</doc>
