﻿using foodremedy.api.Models.Requests;
using foodremedy.api.Utils;
using foodremedy.database.Models;

namespace foodremedy.api.Extensions;

public static class RequestModelExtensions
{
    public static Tag ToDbModel(this CreateTag createTag, TagCategory tagCategory)
    {
        return new Tag
        {
            Name = createTag.Name,
            Description = createTag.Description, // Add this line
            TagCategory = tagCategory
        };
    }

    public static ApiKey ToDbModel(this CreateApiKey createApiKey, string token, User user)
    {
        return new ApiKey{
            Token = token,
            Name = createApiKey.Name,
            Status = true,
            User = user
        };
    }

    public static Nutrient ToDbModel(this CreateNutrient createNutrient)
    {
        return new Nutrient(createNutrient.Name, createNutrient.Description);
    }

    public static User ToDbModel(this RegisterUser registerUser)
    {
        string salt = StringHasher.GenerateSalt();
        return new User(firstName: registerUser.FirstName,
        lastName: registerUser.LastName,
        email: registerUser.Email,
        username: registerUser.Username,
        passwordHash: StringHasher.Hash(registerUser.Password, salt),
        passwordSalt: salt,
        dateCreated: registerUser.DateCreated,
        age: registerUser.Age,
        registerUser.Role ?? "User");
    }

    public static Food ToDbModel(this CreateFood createFood, List<TagCategory>? tagCategories = null)
    {
        string Nutrients = "";
        if(!createFood.Nutrients.IsNullOrEmpty()){
            foreach(KeyValuePair <string, int> keyValues in createFood.Nutrients!) {  
                Nutrients += keyValues.Key + ":" + keyValues.Value.ToString() + ",";
            }
            Nutrients = Nutrients.TrimEnd(',');
        }

        if (createFood.Tags.IsNullOrEmpty() || tagCategories.IsNullOrEmpty())
            return new Food(createFood.Description, createFood.Name, createFood.FoodSeason, createFood.FoodEnergyWithFibre, createFood.FoodEnergyWithoutFibre,createFood.ServingSize, Nutrients)
            {
                Tags = Array.Empty<Tag>()
            };

        var tags = new List<Tag>();

        foreach (KeyValuePair<string, IEnumerable<string>> tagGroup in createFood.Tags!)
        {
            TagCategory? category = tagCategories!
                .SingleOrDefault(p => p.Name.Equals(tagGroup.Key, StringComparison.InvariantCultureIgnoreCase));

            ArgumentNullException.ThrowIfNull(category);

            tags.AddRange(tagGroup.Value.Select(p =>
            {
                Tag? dbTag = category
                    .Tags!
                    .SingleOrDefault(q => q.Name.Equals(p, StringComparison.InvariantCultureIgnoreCase));

                ArgumentNullException.ThrowIfNull(dbTag);

                return dbTag;
            }));
        }

        return new Food(createFood.Description, createFood.Name, createFood.FoodSeason, createFood.FoodEnergyWithFibre, createFood.FoodEnergyWithoutFibre,createFood.ServingSize, Nutrients)
        {
            Tags = tags
        };
    }

    public static TagCategory ToDbModel(this CreateTagCategory createTagCategory)
    {
        return new TagCategory
        {
            Name = createTagCategory.Name,
            Tags = Array.Empty<Tag>()
        };
    }

       public static Allergy ToDbModel(this CreateAllergy createAllergy)
        {
            return new Allergy(createAllergy.Name, createAllergy.Symptoms);
        }

    public static Profile ToDbModel(this ProfileRequest request)
    {
        return new Profile
        {
            Id = Guid.NewGuid(),
            UserId = request.UserId,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Status = request.Status,
            Relationship = request.Relationship,
            Age = request.Age,
            AvatarUrl = request.AvatarUrl,
            Allergies = request.Allergies,
            Intolerances = request.Intolerances,
            DietaryForm = request.DietaryForm
        };
    }

    public static void UpdateFromRequest(this Profile profile, ProfileRequest request)
    {
        profile.FirstName = request.FirstName;
        profile.LastName = request.LastName;
        profile.Status = request.Status;
        profile.Relationship = request.Relationship;
        profile.Age = request.Age;
        profile.AvatarUrl = request.AvatarUrl;
        profile.Allergies = request.Allergies;
        profile.Intolerances = request.Intolerances;
        profile.DietaryForm = request.DietaryForm;
        // Note: UserId and Id are not updated as they should remain constant
    }
}
