using foodremedy.api.Models.Requests;
using foodremedy.database;
using Mysqlx.Expr;
using ApiKey = foodremedy.database.Models.ApiKey;
using User = foodremedy.database.Models.User;
using RefreshToken = foodremedy.database.Models.RefreshToken;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.tests.Factories;

// This factory combines all authentication related objects (except users) into a single factory
public class AuthenticationFactory
{
    const string DefaultEmail = "DefaultEmail@default" ;
    const string DefaultPassword = "DefaultPassword";
    const string DefaultName = "DefaultName";
    const string DefaultUsername = "DefaultUsername";

    const string DefaultToken = "DefaultToken";
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public AuthenticationFactory(FoodRemedyDbContext db){
        Db = db;
    }

    public async Task<ApiKey> AddApiKey(string token=DefaultToken, string name=DefaultName, object? user=null){
        token = token==DefaultToken ? DefaultToken + num : token;
        name = name==DefaultName ? DefaultName + num : name;
        if(user ==  null) {
            user = await CreateUser();
        }
        else if(user is string v)
        {
            user = await GetUser(v);
        }
        ApiKey apiKey = Db.ApiKey.Add(new ApiKey{
            Token = token,
            Name = name,
            Status = true,
            User = (User)user!
        }).Entity;
        await Db.SaveChangesAsync();
        num++;
        return apiKey;
    }

    public CreateApiKey CreateApiKey(string name=DefaultName){
        name = name==DefaultName ? DefaultName + num : name;
        num++;
        return new CreateApiKey(name);
    }

    public AttemptLogin CreateLogin(string email=DefaultEmail,string username=DefaultUsername, string password=DefaultPassword){
        string identifier;
        if(email != DefaultEmail){
            identifier = email;
        }else if(username != DefaultUsername){
            identifier = username;
        }else{
            identifier = email==DefaultEmail ? DefaultEmail + num : email;
        }
        num++;
        return new AttemptLogin(identifier, password);
    }

    public RefreshAccessToken CreateRefreshAccessToken(string token=DefaultToken){
        token = token==DefaultToken ? DefaultToken + num : token;
        num++;
        return new RefreshAccessToken(token);
    }

    public RefreshToken AddRefreshToken(string token=DefaultToken){
        token = token==DefaultToken ? DefaultToken + num : token;
        RefreshToken createdToken = Db.RefreshToken.Add(new RefreshToken(token)).Entity;
        Db.SaveChangesAsync();
        num++;
        return createdToken;
    }

    private async Task<User> CreateUser(){
        IFactory userFactory = new UserFactory(Db);
        return (User) await userFactory.Add();
    }

    private Task<User?> GetUser(string name){
        return Db
            .User
            .SingleOrDefaultAsync(p => p.Name.Equals(name));
    }
}