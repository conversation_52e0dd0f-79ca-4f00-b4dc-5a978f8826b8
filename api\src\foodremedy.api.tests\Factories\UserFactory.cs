using foodremedy.api.Models.Requests;
using foodremedy.database;
using foodremedy.api.Utils;
using User = foodremedy.database.Models.User;

namespace foodremedy.api.tests.Factories;

public class UserFactory : IFactory
{
    const string DefaultEmail = "DefaultEmail@default" ;
    const string DefaultPassword = "DefaultPassword";

    const string DefaultUsername = "DefaultUsername";
    const string DefaultRole = "User";
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public UserFactory(FoodRemedyDbContext db){
        Db = db;
    }
    public async Task<IDatabaseModel> Add(dynamic? args = null)
    {
        num++;
        args = args ?? new { Email = DefaultEmail + num, Password = DefaultPassword, Username = DefaultUsername + num };

        string password = args.GetType().GetProperty("Password") != null ? args.Password : DefaultPassword;
        string salt = StringHasher.GenerateSalt();
        string hash = StringHasher.Hash(password, salt);
        
        
        string username = args.GetType().GetProperty("Username") != null ? args.Username : DefaultUsername + num;

        User user = Db.User.Add(new User(
            args.GetType().GetProperty("Email") != null ? args.Email : DefaultEmail + num, 
            username,
            hash, 
            salt,
            DefaultRole
            
        )).Entity;

        await Db.SaveChangesAsync(); 
        return user;
    }

    public IRequestModel Create(dynamic? args = null)
    {
        num++;
        args = args ?? new { Email = DefaultEmail + num, Username = DefaultUsername + num , Password = DefaultPassword };

        // Retrieve email and password or use defaults
        string email = args.GetType().GetProperty("Email") != null ? args.Email : DefaultEmail + num;
        string password = args.GetType().GetProperty("Password") != null ? args.Password : DefaultPassword;
        
        // Retrieve username or use default
        string username = args.GetType().GetProperty("Username") != null ? args.Username : DefaultUsername + num;

        return new RegisterUser(
            email, 
            username,
            password
        );
    }
}