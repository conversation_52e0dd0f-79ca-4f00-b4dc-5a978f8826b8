{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["kM20dwDcNoQeR0WK1t2GfTPTTvUuaZTZg4Ipzn1V2kc=", "PZu2AO2eGatbT3gjQBlSFv2WCWzbKwpvwvyXdqccnSk=", "r9RhZV5X0Lxctnf6Nhu6EmT62PNt+6KH1VTKZhbkWWk=", "T6br4gK4CV6sgQEmfmu5XLGDi5upY4uxLE1N1ake2u4="], "CachedAssets": {"kM20dwDcNoQeR0WK1t2GfTPTTvUuaZTZg4Ipzn1V2kc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\rtzwsgrvat-6rc0bqxwf5.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/custom-swagger#[.{fingerprint=6rc0bqxwf5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9q9n7bprz7", "Integrity": "AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "FileLength": 679, "LastWriteTime": "2025-08-08T09:26:48.3011317+00:00"}, "PZu2AO2eGatbT3gjQBlSFv2WCWzbKwpvwvyXdqccnSk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\fhkm1uvdc8-rptzmrj8w9.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/styles#[.{fingerprint=rptzmrj8w9}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "viuyvf6xzf", "Integrity": "+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "FileLength": 491, "LastWriteTime": "2025-08-08T09:26:48.3011317+00:00"}, "r9RhZV5X0Lxctnf6Nhu6EmT62PNt+6KH1VTKZhbkWWk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\plygpeghl8-3wvalnw33v.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "index#[.{fingerprint=3wvalnw33v}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cslqv4jcl", "Integrity": "tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "FileLength": 1661, "LastWriteTime": "2025-08-08T09:26:48.3011317+00:00"}, "T6br4gK4CV6sgQEmfmu5XLGDi5upY4uxLE1N1ake2u4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\davjr8vddz-8yjfewal8n.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "swagger-custom#[.{fingerprint=8yjfewal8n}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vj80dostp", "Integrity": "dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "FileLength": 950, "LastWriteTime": "2025-08-08T09:26:48.3011317+00:00"}}, "CachedCopyCandidates": {}}