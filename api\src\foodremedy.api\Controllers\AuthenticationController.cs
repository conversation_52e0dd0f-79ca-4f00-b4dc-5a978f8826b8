﻿using System.Net;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using foodremedy.api.Configuration;
using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Providers;
using foodremedy.api.Repositories;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using User = foodremedy.database.Models.User;
using foodremedy.api.Services;
using System.Text.Json;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("auth")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status200OK)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class AuthenticationController : ControllerBase
{
    private readonly IAuthenticationProvider _authenticationProvider;
    private readonly IUserRepository _userRepository;
    private readonly IApiKeyRepository _apiKeyRepository;
    private readonly IDistributedCache _distributedCache;
    private readonly IApiKeyService _apiKeyService;

    public AuthenticationController(IUserRepository userRepository, IApiKeyRepository apiKeyRepository,
        IAuthenticationProvider authenticationProvider, IDistributedCache distributedCache, IApiKeyService apiKeyService)
    {
        _userRepository = userRepository;
        _apiKeyRepository = apiKeyRepository;
        _authenticationProvider = authenticationProvider;
        _distributedCache = distributedCache;
        _apiKeyService = apiKeyService;
    }

    /// <summary>
    /// Logs the user in
    /// </summary>
    /// <param name="attemptLogin">User login details</param>
    /// <returns> User login to API</returns>
    /// <response code="200">Logs the user in</response>
    /// <response code="400">Login is null</response>
    /// <response code="401">Login details are incorrect</response>
    [AllowAnonymous]
    [HttpPost("login")]
    public async Task<ActionResult<AccessTokenCreated>> AttemptLogin([FromBody] AttemptLogin attemptLogin)
    {
        // Validate input
        if (attemptLogin == null)
        {
            return BadRequest("Login request cannot be null.");
        }

        if (string.IsNullOrWhiteSpace(attemptLogin.Identifier) || string.IsNullOrWhiteSpace(attemptLogin.Password))
        {
            return BadRequest("Username/email and password must be provided.");
        }

        // Fetch user by email or username
        User? user = await _userRepository.GetByUsernameOrEmailAsync(attemptLogin.Identifier);

        if (user == null)
        {
            // Add logging for failed login attempt (optional)
            return Unauthorized("Invalid username/email or password.");
        }

        // Check if the user can log in (e.g., password validation)
        if (!_authenticationProvider.UserCanLogin(user, attemptLogin.Password))
        {
            return Unauthorized("Invalid username/email or password.");
        }

        if (user == null)
        {
            return NotFound("User not found in the database.");
        }

        // Set the session in distributed cache
        await _distributedCache.SetStringAsync(UserSessionKeyEnum.SessionKeyUsername.ToString(), user.Username, new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60)
        });

        // Refresh the refresh token if necessary
        RefreshToken refreshToken = await _authenticationProvider.RefreshRefreshTokenAsync(user);

        // Ensure refresh token is valid
        ArgumentNullException.ThrowIfNull(user.RefreshTokenId);
        if (refreshToken == null)
        {
            return Unauthorized("Refresh token is invalid.");
        }

        await _userRepository.SaveChangesAsync();

        // Generate access token
        JwtSecurityToken accessToken = _authenticationProvider.CreateAccessToken(user);

        // Return response
        return Ok(accessToken.ToResponseModel(refreshToken));
    }


    /// <summary>
    /// Refreshes API access token
    /// </summary>
    /// <param name="refreshAccessToken">Refresh access token</param>
    /// <returns> New access token</returns>
    /// <response code="200">Refreshes access token</response>
    /// <response code="400">Access token is null</response>
    /// <response code="401">Access token is incorrect or if the user is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost("refresh")]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AccessTokenCreated>> RefreshAccessToken(
        [FromBody] RefreshAccessToken refreshAccessToken)
    {
        Claim? userId = User.Claims.SingleOrDefault(p => p.Type.Equals("subject"));

        if (userId == null)
            return Unauthorized();

        User? user = await _userRepository.GetByIdAsync(userId.Value);

        if (user == null ||
            !await _authenticationProvider.RefreshTokenIsValidAsync(user, refreshAccessToken.RefreshToken))
            return Unauthorized();

        RefreshToken refreshToken = await _authenticationProvider.RefreshRefreshTokenAsync(user);
        ArgumentNullException.ThrowIfNull(user.RefreshTokenId);
        await _userRepository.SaveChangesAsync();

        JwtSecurityToken accessToken = _authenticationProvider.CreateAccessToken(user);

        return Ok(accessToken.ToResponseModel(refreshToken));
    }

    /// <summary>
    /// Creates Api Key
    /// </summary>
    /// <param name="userId">Id of the user</param>
    /// <param name="createApiKey">Details for Api key creation</param>
    /// <returns> Creates new Api Key</returns>
    /// <response code="200">Creates a new Api Key</response>
    /// <response code="404">User does not exist</response>
    /// <response code="400">User is disabled or createApiKey is null</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost("{userId:guid}/api-key")]
    public async Task<ActionResult<Models.Responses.ApiKey>> CreateApiKey([FromRoute] Guid userId, [FromBody] CreateApiKey createApiKey)
    {
        var user = await _userRepository.GetByIdAsync(userId);

        if (user == null)
            return NotFound();

        if (user.Status == false)
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = $"Api Keys can only be created for active users"
            });

        database.Models.ApiKey apiKey = _authenticationProvider.CreateApiKey(createApiKey.Name, user);
        await _apiKeyService.AddApiKey(apiKey);
        _apiKeyRepository.Add(apiKey);
        await _apiKeyRepository.SaveChangesAsync();
        return Ok(apiKey.Token);
    }

    /// <summary>
    /// Gets all current API Keys
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <returns> List of current API Keys</returns>
    /// <response code="200">Returns the list of API Keys</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("api-key")]
    public async Task<ActionResult<PaginatedResponse<Models.Responses.ApiKey>>> GetApiKeys([FromQuery] PaginationRequest paginationRequest)
    {
        var results = await _apiKeyRepository.GetAsync(paginationRequest.Skip, paginationRequest.Take);

        return Ok(results.ToResponseModel(p => p.ToResponseModel()));
    }

    /// <summary>
    /// Disables an API Key
    /// </summary>
    /// <param name="apiKeyId">ID of the API Key</param>
    /// <response code="204">Disables the Api key</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Api key does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpDelete("api-key/{apiKeyId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteApiKey([FromRoute] Guid apiKeyId)
    {

        database.Models.ApiKey? dbApiKey = await _apiKeyRepository.GetByIdAsync(apiKeyId);

        if (dbApiKey != null)
        {
            await _apiKeyService.InvalidateApiKey(dbApiKey);
            dbApiKey.Status = false;
            await _apiKeyRepository.SaveChangesAsync();
            return NoContent();
        }

        return NotFound();
    }
}
