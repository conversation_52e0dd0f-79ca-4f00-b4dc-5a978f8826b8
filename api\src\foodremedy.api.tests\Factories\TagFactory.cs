using foodremedy.api.Models.Requests;
using foodremedy.database;
using TagCategory = foodremedy.database.Models.TagCategory;
using Tag = foodremedy.database.Models.Tag;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.tests.Factories;

public class TagFactory : IFactory
{
    const string DefaultName = "DefaultName";
    const string DefaultDescription = "DefaultDescription"; // Added default description
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public TagFactory(FoodRemedyDbContext db)
    {
        Db = db;
    }

    public async Task<IDatabaseModel> Add(dynamic? args = null){
        num++;
        TagCategory? category = null;
        if(args!.GetType().GetProperty("TagCategory") ==  null) {
            category = await CreateTagCategory();
        }
        else if(args.TagCategory is string){
            category = await GetTagCategory(args.TagCategory);
        }
        else{
            category = args.TagCategory;
        }
        Tag tag = Db.Tag.Add(new Tag{
            Name = args.GetType().GetProperty("Name") !=  null ? args.Name : DefaultName + num, 
            Description = args.GetType().GetProperty("Description") !=  null ? args.Description : DefaultDescription + num,
            TagCategory = category
            }).Entity;
        await Db.SaveChangesAsync();
        return tag;
    }

    public IRequestModel Create(dynamic? args = null){
        num++;
        args = args ?? new {Name = DefaultName};
        return new CreateTag(
            args.GetType().GetProperty("Name") !=  null ? args.Name : DefaultName + num,
            args.GetType().GetProperty("Description") !=  null ? args.Description : DefaultDescription + num
            );
    }

    private async Task<TagCategory> CreateTagCategory(){
        IFactory tagCategoryFactory = new TagCategoryFactory(Db);
        return (TagCategory) await tagCategoryFactory.Add();
    }

    private Task<TagCategory?> GetTagCategory(string name){
        return Db
            .TagCategory
            .SingleOrDefaultAsync(p => p.Name.Equals(name));
    }
}
