// _layout (app) tsx

import { Stack } from "expo-router";

export default function AppLayout() {

  /**
   * TODO: Auth Logic
   * 
   * "const { user, loading } = useAuth();"
   * 
   * - Redirect unauthenticated users to /login
   * - Allow authenticated users to access the (tabs) layout
   * - Use the AuthProvider context to manage and check auth state
   * 
   * Note: This component controls the main app navigation stack.
   */

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(tabs)" />
    </Stack>
  );
}
