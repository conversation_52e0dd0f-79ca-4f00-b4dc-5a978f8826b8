"""
Clean OpenFoodFacts Australia dataset for database ingestion.
"""

import json
import pandas as pd


# === Configuration constants ===
# Edit these paths as needed
INPUT_FILE = "data/demo data/productTestSample.jsonl"
OUTPUT_FILE = "data/demo data/cleanedProductTestSample.jsonl"

NUTRIENTS_TO_KEEP = {
    # Energy
    "energy_100g", "energy_serving", "energy_unit",
    "energy-kcal_100g", "energy-kcal_serving", "energy-kcal_unit",

    # Fats
    "fat_100g", "fat_serving", "fat_unit",
    "saturated-fat_100g", "saturated-fat_serving", "saturated-fat_unit",
    "trans-fat_100g", "trans-fat_serving", "trans-fat_unit",
    "monounsaturated-fat_100g", "monounsaturated-fat_serving", "monounsaturated-fat_unit",

    # Carbohydrates
    "carbohydrates_100g", "carbohydrates_serving", "carbohydrates_unit",
    "sugars_100g", "sugars_serving", "sugars_unit",
    "fiber_100g", "fiber_serving", "fiber_unit",

    # Proteins
    "proteins_100g", "proteins_serving", "proteins_unit",

    # Salt/Sodium
    "salt_100g", "salt_serving", "salt_unit",
    "sodium_100g", "sodium_serving", "sodium_unit",

    # Other
    "nova-group"
}


def load_data(file_path: str) -> pd.DataFrame:
    """Load a JSONL file into a pandas DataFrame."""
    try:
        df = pd.read_json(file_path, lines=True, dtype=False)
    except ValueError as e:
        raise RuntimeError(f"Failed to read JSONL file: {e}")
    return df


def drop_exact_duplicates(df: pd.DataFrame) -> pd.DataFrame:
    """
    Deduplicate by 'code' field, keeping the first occurrence.
    Reports how many duplicates were removed.
    """
    if 'code' not in df.columns:
        raise KeyError("Missing required 'code' column for deduplication.")
    initial_count = len(df)
    # Keep only the first row for each code
    df = df.drop_duplicates(subset=['code'], keep='first')
    dropped = initial_count - len(df)
    print(f"Dropped {dropped} duplicate rows based on 'code'.")
    return df


def ensure_code_field(df: pd.DataFrame) -> pd.DataFrame:
    """
    Ensure the 'code' column exists and contains only non-empty strings.
    Rows with missing or empty 'code' are removed.
    """
    if 'code' not in df.columns:
        raise KeyError("Missing required 'code' column in dataset.")
    # Strip whitespace and drop empty values
    df['code'] = df['code'].astype(str).str.strip()
    df = df[df['code'] != ""]
    return df


def clean_text_fields(df: pd.DataFrame) -> pd.DataFrame:
    """
    Standardize text fields:
      - 'product_name': trim whitespace, convert empty to NA
      - 'brands': ensure column exists, fill missing, title-case
    """
    # Clean product_name if present
    if 'product_name' in df.columns:
        df['product_name'] = (
            df['product_name']
            .astype(str)
            .str.strip()
                .replace({'': pd.NA})
        )
    else:
        # If missing entirely, add column with NA
        df['product_name'] = pd.NA

    # Clean or create brands column
    if 'brands' in df.columns:
        df['brands'] = (
            df['brands']
            .fillna("")           # replace nulls
            .astype(str)
            .str.strip()
                .str.title()
        )
    else:
        # Add a brands column of empty strings
        df['brands'] = ""

    return df


def to_numeric(series: pd.Series, default=0) -> pd.Series:
    """Convert a Series to numeric, coercing errors and filling NAs with a default value."""
    return pd.to_numeric(series, errors='coerce').fillna(default)


def clean_quantity_fields(df: pd.DataFrame) -> pd.DataFrame:
    """
    Parse numeric and unit fields for product and serving quantities.
    Missing unit columns are created with sensible defaults.
    """
    # Product quantity and unit
    df['product_quantity'] = to_numeric(
        df['product_quantity']) if 'product_quantity' in df.columns else 0
    df['product_quantity_unit'] = (
        df['product_quantity_unit']
        .fillna('g') if 'product_quantity_unit' in df.columns else 'g'
    )
    df['product_quantity_unit'] = df['product_quantity_unit'].astype(
        str).str.lower()

    # Serving quantity and unit
    df['serving_quantity'] = to_numeric(
        df['serving_quantity']) if 'serving_quantity' in df.columns else 0
    df['serving_quantity_unit'] = (
        df['serving_quantity_unit']
        .fillna('g') if 'serving_quantity_unit' in df.columns else 'g'
    )
    df['serving_quantity_unit'] = df['serving_quantity_unit'].astype(
        str).str.lower()

    # Serving size text
    df['serving_size'] = (
        df['serving_size']
        .fillna('Not Specified') if 'serving_size' in df.columns else 'Not Specified'
    )
    df['serving_size'] = df['serving_size'].astype(str)

    return df


def clean_nutriments(df: pd.DataFrame) -> pd.DataFrame:
    """
    Ensure the 'nutriments' column contains dicts; replace invalid entries with empty dicts.
    """
    df['nutriments'] = df['nutriments'].apply(
        lambda x: x if isinstance(x, dict) else {}
    )
    return df


def reduce_nutriments(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filter 'nutriments' field to keep only relevant macros
    and associated units/serving values. Removes all extra info.
    """
    def filter_nutriments(n: dict) -> dict:
        if not isinstance(n, dict):
            return {}
        return {k: v for k, v in n.items() if k in NUTRIENTS_TO_KEEP}

    df["nutriments"] = df["nutriments"].apply(filter_nutriments)
    return df


def strip_en_prefix(items):
    """
    Given either a list of strings or a comma-separated string,
    return a clean list with any 'en:' prefixes removed.
    """
    # Turn string → list
    if isinstance(items, str):
        parts = [p.strip() for p in items.split(',') if p.strip()]
    elif isinstance(items, list):
        parts = items
    else:
        return []

    # Drop any empty entries, strip 'en:' if present
    cleaned = []
    for tag in parts:
        if not tag:
            continue
        if tag.startswith('en:'):
            cleaned.append(tag.split(':', 1)[1])
        elif ':' in tag:
            # catch any other prefix:foo → foo
            cleaned.append(tag.split(':', 1)[1])
        else:
            cleaned.append(tag)
    return cleaned


def clean_all_tag_fields(df: pd.DataFrame) -> pd.DataFrame:
    """
    Apply strip_en_prefix to every one of these columns,
    defaulting missing/NaN → empty list.
    """
    tag_cols = [
        'traces', 'traces_from_ingredients', 'allergens',
        'additives_tags', 'allergens_tags', 'ingredients_tags',
        'labels_tags', 'categories_tags', 'ingredients_analysis_tags'
    ]
    for col in tag_cols:
        if col in df.columns:
            df[col] = df[col].apply(strip_en_prefix)
        else:
            df[col] = []
    return df


def reconvert_json_strings(df: pd.DataFrame) -> pd.DataFrame:
    """
    Parse any JSON-encoded strings back into Python objects (list or dict).
    """
    for col in df.columns:
        df[col] = df[col].apply(
            lambda x: json.loads(x)
            if isinstance(x, str) and (x.startswith('{') or x.startswith('['))
            else x
        )
    return df


def save_cleaned_data(df: pd.DataFrame, output_path: str):
    """
    Write the cleaned DataFrame to a JSONL file and log a summary.
    """
    df.to_json(output_path, orient='records', lines=True, force_ascii=False)
    print(f"Cleaned data saved to: {output_path}")
    print(f"Total valid products: {len(df)}")


def main(input_path: str, output_path: str = "cleaned_openfoodfacts.jsonl"):
    """
    Execute the full cleaning pipeline from raw JSONL to cleaned JSONL.
    """
    df = load_data(input_path)
    df = drop_exact_duplicates(df)
    df = ensure_code_field(df)
    df = clean_text_fields(df)
    df = clean_quantity_fields(df)
    df = clean_nutriments(df)
    df = reduce_nutriments(df)
    df = clean_all_tag_fields(df)
    df = reconvert_json_strings(df)
    save_cleaned_data(df, output_path)


if __name__ == "__main__":
    # Run the cleaning process
    main(INPUT_FILE, OUTPUT_FILE)
