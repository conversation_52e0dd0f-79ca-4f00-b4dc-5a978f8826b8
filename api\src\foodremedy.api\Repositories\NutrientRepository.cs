using foodremedy.database;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.Repositories;

public interface INutrientRepository
{
    Task<PaginatedResult<Nutrient>> GetAsync(int skip = 0, int take = 20);
    Nutrient Add(Nutrient nutrient);
    Nutrient Update(Nutrient nutrient);
    void Remove(Nutrient nutrient);
    Task SaveChangesAsync();
    Task<Nutrient?> GetByIdAsync(Guid id);
    Task<Nutrient?> GetByNameAsync(string name);
}

public class NutrientRepository : INutrientRepository
{
    private readonly FoodRemedyDbContext _dbContext;

    public NutrientRepository(FoodRemedyDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<PaginatedResult<Nutrient>> GetAsync(int skip = 0, int take = 20)
    {
        List<Nutrient> result = await _dbContext
            .Nutrient
            .Skip(skip)
            .Take(take)
            .ToListAsync();

        return new PaginatedResult<Nutrient>(result.Count, _dbContext.Nutrient.Count(), result);
    }

    public Nutrient Add(Nutrient Nutrient)
    {
        return _dbContext.Nutrient.Add(Nutrient).Entity;
    }

    public Nutrient Update(Nutrient Nutrient)
    {
        return _dbContext.Nutrient.Update(Nutrient).Entity;
    }

    public void Remove(Nutrient Nutrient)
    {
        _dbContext.Nutrient.Remove(Nutrient);
    }

    public async Task<Nutrient?> GetByIdAsync(Guid id)
    {
        return await _dbContext.Nutrient.SingleOrDefaultAsync(p => p.Id == id);
    }

    public async Task<Nutrient?> GetByNameAsync(string name)
    {
        return await _dbContext.Nutrient.SingleOrDefaultAsync(p => p.Name.Equals(name));
    }

    public async Task SaveChangesAsync()
    {
        await _dbContext.SaveChangesAsync();
    }
}
