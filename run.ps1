# Require admin permission to run scripts (if not already set)
# Set-ExecutionPolicy RemoteSigned -Scope CurrentUser # Run this once in PowerShell if needed

Write-Host "Activating virtual environment..."
# Source the activate script
. .venv\Scripts\Activate.ps1

if (-not $env:VIRTUAL_ENV) {
    Write-Host "Failed to activate virtual environment!"
    exit 1
}
Write-Host "Virtual environment activated."

Write-Host "Running run_script.py..."
# Run your python script
python run_script.py

# Optional: deactivate
# deactivate

Write-Host "Script finished."
