// Providers tsx

import { ReactNode } from "react";
import { ProductProvider } from "./ProductProvider";
import ModalLoader from "../modals/ModalALoader";
import { ModalManagerProvider } from "./ModalManagerProvider";
// import { NotificationProvider } from "./NotificationProvider";

interface ProvidersProps {
  children: ReactNode;
}

const Providers = ({ children }: ProvidersProps) => {

  return (
    <>
      <ModalManagerProvider>
        <ProductProvider>

          {children}
          <ModalLoader />

        </ProductProvider>
      </ModalManagerProvider>
    </>
  );
}

export default Providers;
