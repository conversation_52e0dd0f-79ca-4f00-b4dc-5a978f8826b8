using foodremedy.api.Extensions;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;

namespace foodremedy.api.Services;

public interface IApiKeyService
{
    Task<Dictionary<string, Guid>> GetActiveApiKeys();
    Task AddApiKey(database.Models.ApiKey apiKey);
    Task InvalidateApiKey(database.Models.ApiKey apiKey);
}

internal class ApiKeyService : IApiKeyService
{
    private static readonly Dictionary<string, Guid> _apiKeys = new(){};
    private readonly IApiKeyRepository _apiKeyRepository;
    public ApiKeyService(IApiKeyRepository apiKeyRepository)
    {
        _apiKeyRepository = apiKeyRepository;
    }

    public async Task<Dictionary<string, Guid>> GetActiveApiKeys()
    {
        if(_apiKeys.IsNullOrEmpty()) await GetKeysFromDb();
        return _apiKeys;
    }

    public async Task AddApiKey(database.Models.ApiKey apiKey)
    {
        if(_apiKeys.IsNullOrEmpty()) await GetKeysFromDb();
        _apiKeys.Add(apiKey.Token, apiKey.Id);
    }

    public async Task InvalidateApiKey(database.Models.ApiKey apiKey)
    {
        if(_apiKeys.IsNullOrEmpty()) await GetKeysFromDb();
        _apiKeys.Remove(apiKey.Token);
    }

    private async Task GetKeysFromDb(){
        var dbkeys = await _apiKeyRepository.GetActiveAsync();
            foreach(var key in dbkeys){
                _apiKeys.Add(key.Token, key.Id);
            }
    }
}