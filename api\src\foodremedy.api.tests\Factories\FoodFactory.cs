using System.Collections.Generic;
using foodremedy.api.Models.Requests;
using foodremedy.database;
using Food = foodremedy.database.Models.Food;
using Tag = foodremedy.database.Models.Tag;

namespace foodremedy.api.tests.Factories;

public class FoodFactory : IFactory
{
    const string DefaultName = "DefaultFood";
    const string DefaultDescription = "DefaultDescription";
    const string DefaultSeason = "Summer";
    const int DefaultEnergyWithFibre = 100;
    const int DefaultEnergyWithoutFibre = 50;
    const string DefaultNutrients = "";
    const int DefaultServingSize = 200;
    
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public FoodFactory(FoodRemedyDbContext db){
        Db = db;
    }

    public async Task<IDatabaseModel> Add(dynamic? args = null){
        num++;
        args = args ?? new {Name = new[] { DefaultName + num }, Description = DefaultDescription, Season = DefaultSeason, EnergyWithFibre = DefaultEnergyWithFibre,  EnergyWithoutFibre = DefaultEnergyWithoutFibre, ServingSize = DefaultServingSize, Nutrients = DefaultNutrients, Tags = new List<Tag>()};
        Food food = Db.Food.Add(new Food(
            args.GetType().GetProperty("Description") !=  null ? args.Description : DefaultDescription, 
            args.GetType().GetProperty("Name") !=  null ? args.Name : new[] { DefaultName + num }, 
            args.GetType().GetProperty("Season") !=  null ? args.Season : DefaultSeason, 
            args.GetType().GetProperty("EnergyWithFibre") !=  null ? args.EnergyWithFibre : DefaultEnergyWithFibre, 
            args.GetType().GetProperty("EnergyWithoutFibre") !=  null ? args.EnergyWithoutFibre : DefaultEnergyWithoutFibre,
            args.GetType().GetProperty("ServingSize") !=  null ? args.ServingSize : DefaultServingSize,
            args.GetType().GetProperty("Nutrients") !=  null ? args.Nutrients : DefaultNutrients
            ){
            Tags= args.GetType().GetProperty("Tags") !=  null ? args.Tags : new List<Tag>()
        }).Entity;
        await Db.SaveChangesAsync();    
        return food;
    }

    public IRequestModel Create(dynamic? args = null){
        num++;
        args = args ?? new {Name = new[] { DefaultName + num }, Description = DefaultDescription, Season = DefaultSeason, EnergyWithFibre = DefaultEnergyWithFibre,  EnergyWithoutFibre = DefaultEnergyWithoutFibre, ServingSize = DefaultServingSize, Nutrients = new Dictionary<string, int>(), Tags = new Dictionary<string, IEnumerable<string>>()};
        return new CreateFood(
            args.GetType().GetProperty("Description") !=  null ? args.Description : DefaultDescription, 
            args.GetType().GetProperty("Name") !=  null ? args.Name : new[] { DefaultName + num }, 
            args.GetType().GetProperty("Season") !=  null ? args.Season : DefaultSeason, 
            args.GetType().GetProperty("EnergyWithFibre") !=  null ? args.EnergyWithFibre : DefaultEnergyWithFibre, 
            args.GetType().GetProperty("EnergyWithoutFibre") !=  null ? args.EnergyWithoutFibre : DefaultEnergyWithoutFibre,
            args.GetType().GetProperty("ServingSize") !=  null ? args.ServingSize : DefaultServingSize,
            args.GetType().GetProperty("Nutrients") !=  null ? args.Nutrients : new Dictionary<string, int>(),
            args.GetType().GetProperty("Tags") !=  null ? args.Tags : new Dictionary<string, IEnumerable<string>>()
            );
    }
}