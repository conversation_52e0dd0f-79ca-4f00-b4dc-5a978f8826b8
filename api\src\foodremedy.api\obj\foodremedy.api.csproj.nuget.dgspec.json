{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\foodremedy.api.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\foodremedy.api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\foodremedy.api.csproj", "projectName": "foodremedy.api", "projectPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\foodremedy.api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\soft\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.database\\foodremedy.database.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.database\\foodremedy.database.csproj"}}}}, "warningProperties": {"noWarn": ["NU1603"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FastMember": {"target": "Package", "version": "[1.5.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Microsoft.Extensions.Caching.Hybrid": {"target": "Package", "version": "[9.4.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.2, )"}, "MySql.Data": {"target": "Package", "version": "[9.2.0, )"}, "OpenTelemetry": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Exporter.Jaeger": {"target": "Package", "version": "[1.5.1, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.11.1, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.11.1, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.2, )"}, "Serilog.Extensions.Logging.File": {"target": "Package", "version": "[3.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.database\\foodremedy.database.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.database\\foodremedy.database.csproj", "projectName": "foodremedy.database", "projectPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.database\\foodremedy.database.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.database\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\soft\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1603"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.4, )"}, "MySql.EntityFrameworkCore": {"target": "Package", "version": "[8.3.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.2, )"}, "Serilog.Extensions.Logging.File": {"target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}