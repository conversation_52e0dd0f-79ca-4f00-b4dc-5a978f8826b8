# docker/Dockerfile.api

FROM python:3.11-slim

WORKDIR /app

# Install Postgres client libraries for psycopg2
RUN apt-get update && apt-get install -y gcc libpq-dev

# Copy backend code only
COPY backend /app/backend

# Install requirements
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Start FastAPI
CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
