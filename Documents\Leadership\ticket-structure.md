# 🏷️ Ticket Structure

This document outlines the required structure for tickets used in the **Food Remedy API** project. Proper ticket formatting ensures tasks are trackable across Git branches, commits, and pull requests — and helps the team stay organized and efficient.


<br/>


## 🧩 Codes & Difficulty Levels

Every ticket in Planner must begin with a **unique code**. This acts as an identifier that links the task to related:
- Branch names (e.g., `git checkout -b FE080-create-clients-table`)
- Commit messages (e.g., `"Updated login validation. See FE080"`)
- Pull request titles (e.g., `"FE080 – Create Clients Table"`)


### Code Format

Each ticket code consists of:
- A **2-letter prefix** — indicating the section of the application
- A **numerical suffix** — a short ID to keep it unique
- Example: `FE080`, `DB015`, `UI001`

| Prefix | Area              |
|--------|-------------------|
| `FE`   | Frontend          |
| `BE`   | Backend           |
| `DB`   | Database          |
| `UI`   | UI/UX             |
| `RS`   | Research          |
| `DD`   | Documentation     |
| `SD`   | Solution Design   |
| `DV`   | DevOps            |

> 🔧 Additional prefixes can be introduced if new areas of the app emerge.


<br/>


### 🎯 Difficulty Levels

Each ticket should also have a **difficulty label** to guide contributors toward suitable tasks based on their learning goals:

- **Level 1** – *Beginner*  
  Basic tasks like styling, copying existing components, or writing documentation.  
  Recommended for pass-level contributors.

- **Level 2** – *Intermediate*  
  Implementation of small features or functional improvements.  
  Suitable for credit-level contributors.

- **Level 3** – *Advanced*  
  Complex features, research-based tasks, or architectural work.  
  Aimed at students pursuing distinction or high distinction.

<br/>

> 💡 Contributers are welcome to take on any level, but for higher grades, ensure they complete at least some Level 2 or 3 tickets.
