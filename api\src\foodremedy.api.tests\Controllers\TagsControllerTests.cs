﻿using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Responses;
using foodremedy.api.Models.Requests;
using Newtonsoft.Json;
using foodremedy.api.tests.Factories;

namespace foodremedy.api.tests.Controllers;

    /// <summary>
    /// Contains the test cases for the TagsController.
    /// Tests include unauthenticated, authenticated by API key, valid and invalid requests, and CRUD operations for tags.
    /// </summary>
internal sealed class TagsControllerTests : ControllerTestFixture
{
    /// <summary>
    /// Gets the factory used to create tag objects.
    /// </summary>
    public override IFactory Factory => tagFactory;

    /// <summary>
    /// Test cases for unauthenticated requests, expecting Unauthorized status.
    /// </summary>
    public new static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>
    {        
        new TestCaseData(new {
            Path= "tags", 
            Method= HttpMethod.Get
            }).SetName("GetTags_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "tags/name",
            Method= HttpMethod.Get
            }).SetName("GetTagsByCategory_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteTag_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateTag_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateTag_UnauthenticatedRequest_ReturnsUnauthorized")
    };

    /// <summary>
    /// Test cases for authenticated requests using an API key, expecting Unauthorized status.
    /// </summary>
    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>
    {        
        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteTag_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateTag_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateTag_AuthenticatedByApiKey_ReturnsUnauthorized")
    };

    /// <summary>
    /// Test cases for requests where the resource is not found, expecting NotFound status.
    /// </summary>
    public new static List<TestCaseData> NotFoundTests = new List<TestCaseData>
    {        
        new TestCaseData(new {
            Path=  "tags/name",
            Method= HttpMethod.Get
            }).SetName("GetTagsByCategory_CategoryDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteTag_TagDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateTag_CategoryDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"tags/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateTag_TagDoesNotExist_ReturnsNotFound")
    };

    /// <summary>
    /// Test cases for requests where no content is returned, expecting NoContent status.
     /// </summary>
    public new static List<TestCaseData> NoContentTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "tags", 
            Use_Real_Id_In_Path = true,
            Method= HttpMethod.Delete
            }).SetName("DeleteTag_ValidRequest_ReturnsNoContent"),
    };

    /// <summary>
    /// Test cases for invalid requests, expecting BadRequest status.
    /// </summary>
    public new static List<TestCaseData> BadRequestTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "tags",
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = "Existing Name"
                },
            Request_With_Properties = new{
                    Name = "Existing Name"
                },
            Method= HttpMethod.Put
            }).SetName("UpdateTag_NoChange_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "tags",
            Use_Real_Id_In_Path = true,
            Add_Tag_Before =  new{
                    Name = "name1"
                },
            Add_Before_With_Properties = new{
                    Name = "name2"
                },
            Request_With_Properties = new{
                    Name = "name1"
                },
            Method= HttpMethod.Put
            }).SetName("UpdateTag_ExistingName_ReturnsBadRequest"),
    };
            
    /// <summary>
    /// Test cases for successfully creating or updating a tag, expecting Created status.
    /// </summary>
    public new static List<TestCaseData> CreatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "tags",
            Method= HttpMethod.Post,
            Use_Real_Id_In_Path = true,
            Add_Tag_Category_Before = new{
                    Name = "Category"
                },
            Request_With_Properties = new{
                    Name = "Name"
                },
            Check_Id_Exists = true,
            Check_Result_Properties = new{
                    Name = "Name",
                    TagCategory = "Category"
                }
            }).SetName("CreateTag_ValidRequest_ReturnsCreated"),

        new TestCaseData(new {
            Path=  "tags",
            Method= HttpMethod.Put,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = "name1"
                },
            Request_With_Properties = new{
                    Name = "name2"
                },
            Check_Result_Properties = new{
                    Name = "name2"
                }
            }).SetName("UpdateTag_ValidRequest_ReturnsCreated"),
    };

    /// <summary>
    /// Test cases for retrieving tags, expecting Ok status.
    /// </summary>
    public new static List<TestCaseData> OkTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "tags",
            Method= HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new{
                    Name = "name1",
                    },
                new{
                    Name = "name2",
                    },
                },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new{
                    Name = "name1",
                    },
                new{
                    Name = "name2",
                    },
                }
            }).SetName("GetTags_ValidRequest_ReturnsOk"),
    };

    /// <summary>
    /// Test case for GET request to retrieve tags by category, expecting an Ok status with a payload.
    /// This test adds a tag category and two tags under that category, then verifies that a valid GET request 
    /// to retrieve the tags returns an HTTP 200 OK status, and that the response contains exactly two tags.
    /// </summary>
    [Test]
    public async Task GetTagsByCategory_ValidRequest_RespondsOkWithPayload()
    {
        // Add a tag category and two tags under that category
        var category = (database.Models.TagCategory)await tagCategoryFactory.Add();
        var firstTag = (database.Models.Tag)await tagFactory.Add(new
        {
            TagCategory = category
        });
        var secondTag = (database.Models.Tag)await tagFactory.Add(new
        {
            TagCategory = category
        });

        var request = new HttpRequestMessage(HttpMethod.Get, $"tags/{category.Name}");
        var response = await SendAuthenticatedRequestAsync(request);
        var result = await response.Content.ReadAsStringAsync();

        var paginatedResponse = JsonConvert.DeserializeObject<PaginatedResponse<Tag>>(result)!;
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        paginatedResponse.Results.Should().HaveCount(2); // Assuming `Results` is the correct property name
        paginatedResponse.Results.Should().ContainSingle(t => t.Id == firstTag.Id && t.Name == firstTag.Name);
        paginatedResponse.Results.Should().ContainSingle(t => t.Id == secondTag.Id && t.Name == secondTag.Name);
    }


}
