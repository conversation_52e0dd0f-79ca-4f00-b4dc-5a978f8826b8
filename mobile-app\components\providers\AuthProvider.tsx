// AuthProvider.tsx

import { createContext, ReactNode, useContext, useEffect, useState } from 'react';

interface AuthContextType {
  // TODO: Create a proper User type based on database schema
  // Example:
  // user: { id: number; email: string; name: string } | null;

  // TODO: Replace with actual user state
  // user: User | null;

  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // TODO: Setup user state
  // const [user, setUser] = useState<User | null>(null);

  const [loading, setLoading] = useState(true);

  /**
   * TODO: Load user session on mount (e.g., check token from local storage or SecureStore)
   * - Validate token with backend
   * - If valid, set user state
   * - Set loading to false once done
   */
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      // TODO: Fetch user or validate session here
      setLoading(false);
    };

    initializeAuth();
  }, []);


  return (
    <AuthContext.Provider value={{
      // user,
      loading,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
