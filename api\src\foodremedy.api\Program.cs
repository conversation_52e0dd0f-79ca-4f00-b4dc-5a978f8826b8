using foodremedy.api.Extensions;
using foodremedy.database.Extensions;
using foodremedy.api.Services;
using foodremedy.api.Utils;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Filters;
using Microsoft.EntityFrameworkCore;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;
using foodremedy.database.Models;
using foodremedy.database;
using System.Reflection;
using foodremedy.api.Repositories;
using Microsoft.Extensions.Options; //ADDED for RecipeRepository
using Microsoft.Extensions.Caching.Hybrid;

Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .WriteTo.File("/logs/foodremedy_api.log", outputTemplate: "[{Level}] {Message}{NewLine}{Exception}")
    .CreateLogger();

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

// Configure Serilog
builder.Logging.ClearProviders();
var path = Directory.GetCurrentDirectory();
builder.Logging.AddSerilog();

// Configure other services
builder.Configuration.AddJsonFiles();
builder.Services.ConfigureCors();
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddDistributedMemoryCache();
builder.Services.AddLocalization(opt => opt.ResourcesPath = "Resources");

builder.Services.AddSwaggerGen(options =>
{
    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));

    options.AddApiKeySupport();
    options.CustomSchemaIds(t => t.FullName);

    // First Swagger document
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Food Remedy API",
        Version = "user",
        Description = @"**API for Users**<br>
        Login and authorise with the bearer token to access all endpoints.<br>
        `{ ""identifier"": ""dev"", ""password"": ""12345"" }`"
    });


    // Second Swagger document
    // options.SwaggerDoc("v2", new OpenApiInfo
    // {
    //     Title = "Food Remedy API",
    //     Version = "client",
    //     Description = @"**API for Clients**<br>
    //     All these endpoints can be accessed using an API key.<br>
    //     Example key: `FR-yjAejucMY02-l1hJA75Mo-_DGHtHz4YJ3`"
    // });

    options.DocumentFilter<SwaggerFilter>();
});

builder.Services
    .AddDefaultApiKeyGenerator(new ApiKeyGenerationOptions
    {
        KeyPrefix = "FR-",
        GenerateUrlSafeKeys = true,
        LengthOfKey = 36
    })
    .AddDefaultClaimsPrincipalFactory()
    .AddScoped<IApiKeyService, ApiKeyService>()
    .AddMemoryCache()
    .AddScoped<IApiKeysCacheService, CacheService>();

builder.Services.AddMemoryCache();
try
{
    builder.Services.AddStackExchangeRedisCache(options =>
    {
        options.Configuration = builder.Configuration.GetConnectionString("RedisConnection");
        options.InstanceName = "FoodRemedy";
    });
}
catch (Exception ex)
{
    // Console.WriteLine("Redis setup error: " + ex.Message);
}

builder.Services.AddScoped<ICachingService, CachingService>();

// builder.Services.AddHybridCache(options =>
// {
//     options.MaximumKeyLength = 24;
//     options.MaximumPayloadBytes=1024*1024; //1 Mb
// });

builder.Services.UseLowercaseUrls();


builder.Services.AddDbContext<FoodRemedyDbContext>(options =>
    options.UseMySql(builder.Configuration.GetConnectionString("DefaultConnection"),
        new MySqlServerVersion(new Version(9, 0, 0)))); // Specify your MySQL server version

builder.Services.AddJwtAndApiKeyAuthentication(builder.Configuration);
builder.Services.AddInternalServices();

WebApplication app = builder.Build();

// Serve static files from wwwroot
app.UseStaticFiles();

app.UseSwagger();
app.UseStaticFiles();
app.UseSwaggerUI(c =>
{
    // Define endpoints for the multiple Swagger documents
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Food Remedy API User");
    // c.SwaggerEndpoint("/swagger/v2/swagger.json", "Food Remedy API Client");

    c.InjectStylesheet("/css/custom-swagger.css");

    // Inject custom JavaScript to hide the "Schemas" section for the client page
    c.InjectJavascript("/swagger-custom.js");
});


// Routing for the main page
app.MapGet("/", async context =>
{
    context.Response.ContentType = "text/html";
    await context.Response.SendFileAsync(Path.Combine("wwwroot", "index.html"));
});

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers().RequireAuthorization();

app.Run();