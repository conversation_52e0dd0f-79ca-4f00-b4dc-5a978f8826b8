﻿using foodremedy.database;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.Repositories;

public interface IFoodRepository
{
    Task<PaginatedResult<Food>> GetAsync(int skip = 0, int take = 20);
    Task<PaginatedResult<Food>> GetByTagAsync(string tagName, int skip = 0, int take = 20);
    Task<PaginatedResult<Food>> GetBySeasonAsync(string foodSeason, int skip = 0, int take = 20);
    Task<PaginatedResult<Food>> GetByTagAndSeasonAsync(string tagName, string foodSeason, int skip = 0, int take = 20);
    Task<PaginatedResult<Food>> GetByNutrientAsync(string nutrientName, int skip = 0, int take = 20);
     Task<PaginatedResult<Food>> GetByTagAndNutrientAsync(string tagName, string nutrientName, int skip = 0, int take = 20);
    Task<PaginatedResult<Food>> GetByNutrientAndSeasonAsync(string nutrientName, string foodSeason, int skip = 0, int take = 20);
    Task<PaginatedResult<Food>> GetByTagNutrientAndSeasonAsync(string tagName, string nutrientName, string foodSeason, int skip = 0, int take = 20);
    Food Add(Food food);
    void Remove(Food food);
    Food Update(Food food);
    Task SaveChangesAsync();
    Task<Food?> GetByIdAsync(Guid id);
    Task<IEnumerable<Food?>> GetByNameAsync(string name);

    Task<IEnumerable<Food>> GetFoodBySeason(string season);

    

}

public class FoodRepository : IFoodRepository
{
    private readonly FoodRemedyDbContext _dbContext;

    public FoodRepository(FoodRemedyDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<Food>> GetFoodBySeason(string season)
{
    // Ensure the season is normalized and perform a case-insensitive comparison
    return await _dbContext.Food
        .Where(f => f.FoodSeason.ToLower() == season.ToLower())  // Perform case-insensitive comparison
        .ToListAsync();
}


    public async Task<PaginatedResult<Food>> GetAsync(int skip = 0, int take = 20)
    {
        List<Food> result = await _dbContext
            .Food
            .Skip(skip)
            .Take(take)
            .ToListAsync();

        return new PaginatedResult<Food>(result.Count, _dbContext.Food.Count(), result);
    }

    public async Task<PaginatedResult<Food>> GetByTagAsync(string tagName, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(p => p.Tags.Any(t => t.Name == tagName))
            .ToListAsync();

        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }

    public async Task<PaginatedResult<Food>> GetBySeasonAsync(string foodSeason, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(f => f.FoodSeason == foodSeason)
            .ToListAsync();

        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }
    public async Task<PaginatedResult<Food>> GetByTagAndSeasonAsync(string tagName, string foodSeason, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(f => f.Tags.Any(t => t.Name == tagName) && f.FoodSeason == foodSeason)
            .ToListAsync();

        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }

    public async Task<PaginatedResult<Food>> GetByNutrientAsync(string nutrientName, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(f => f.Nutrients.Contains(nutrientName))
            .ToListAsync();
            Console.WriteLine(resultTotal[0].Nutrients.GetType());
        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }
    public async Task<PaginatedResult<Food>> GetByTagAndNutrientAsync(string tagName, string nutrientName, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(f => f.Tags.Any(t => t.Name == tagName) &&  f.Nutrients.Contains(nutrientName))
            .ToListAsync();

        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }
    public async Task<PaginatedResult<Food>> GetByNutrientAndSeasonAsync(string nutrientName, string foodSeason, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(f=> f.Nutrients.Contains(nutrientName) && f.FoodSeason == foodSeason)
            .ToListAsync();

        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }
    public async Task<PaginatedResult<Food>> GetByTagNutrientAndSeasonAsync(string tagName, string nutrientName, string foodSeason, int skip = 0, int take = 20)
    {
        List<Food> resultTotal = await _dbContext
            .Food
            .Where(f => f.Tags.Any(t => t.Name == tagName) && f.Nutrients.Contains(nutrientName) && f.FoodSeason == foodSeason)
            .ToListAsync();

        List<Food> result = resultTotal.Skip(skip).Take(take).ToList();

        return new PaginatedResult<Food>(result.Count, resultTotal.Count, result);
    }
    public Food Add(Food food)
    {
        return _dbContext.Food.Add(food).Entity;
    }

    public void Remove(Food food)
    {
        _dbContext.Food.Remove(food);
    }

    public Food Update(Food food)
    {
        return _dbContext.Food.Update(food).Entity;
    }

    public async Task<Food?> GetByIdAsync(Guid id)
    {
        return await _dbContext
            .Food
            .Include(p => p.Tags)
            .ThenInclude(p => p.TagCategory)
            .SingleOrDefaultAsync(p => p.Id == id);
    }
    

    public async Task<IEnumerable<Food?>> GetByNameAsync(string name)
    {
        var allFoods = await _dbContext
            .Food
            .Select(f => new Food(f.Description, f.Name, f.FoodSeason, f.FoodEnergyWithFibre, f.FoodEnergyWithoutFibre, f.ServingSize, f.Nutrients)
                {
                    Id = f.Id,
                    Tags = f.Tags.Select(t => new Tag
                    {
                        Id = t.Id,
                        Description = t.Description,
                        Name = t.Name,
                        TagCategory = new TagCategory
                        {
                            Id = t.TagCategory.Id,
                            Name = t.TagCategory.Name
                        }
                    }).ToList()
                })
            .ToListAsync();

        return allFoods.Where(f => f.Name.Any(n => n.Contains(name, StringComparison.OrdinalIgnoreCase)));
    }
   
    public async Task SaveChangesAsync()
    {
        await _dbContext.SaveChangesAsync();
    }
}
