# FoodRemedyAPI-Database
Repository for Food Remedy API Database Resources

## Contains of this repository
*Docs* - contains all design and decision documents of the project database.  
*Code* - contains the SQL codes for database definition and sample data.  

## Tech stack needs:
MySQL  
Docker  

## How to use SQL Codes to create the database.
1) Download **MySQL Community Edition** via this [link](https://www.mysql.com/products/community/).
2) Install MySQL Community Edition by following the instructions from this [Youtube video](https://www.youtube.com/watch?v=ho-zwGvnqB4).
3) Open **MySQL Benchmark** and login to the localhost database.
4) Open and run the SQL file **"foodremedyapi_tables.sql"** to create the database and tables.
5) Open and run the SQL file **"foodremedyapi_sampledata.sql"** to populate tables with sample data.

## Contributors to this repository
**T2 2023:**  
<PERSON>v  
<PERSON>  
