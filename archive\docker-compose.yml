services:
  # 🐘 PostgreSQL Database
  db:
    image: postgres:15
    container_name: foodremedy-db
    env_file:
      - .env
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      retries: 5
      start_period: 10s

  # 🖥️ Optional GUI: pgAdmin
  pgadmin:
    image: dpage/pgadmin4
    container_name: foodremedy-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin1234
    ports:
      - "5050:80"
    depends_on:
      db:
        condition: service_healthy

  # 🚀 FastAPI App Container
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile.api
    container_name: foodremedy-api
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - .env
    volumes:
      - ./data:/data

volumes:
  postgres-data:
