# 📝 Completing Code-Free Tickets

Not all tasks in the project require coding — and that’s perfectly okay. Contributions like **UI/UX design**, **solution architecture**, and **technology research** are just as valuable in shaping the success of the Food Remedy API project.

These tasks are considered *code-free tickets* and follow a slightly different workflow than technical ones.

<br/>


## 👥 Collaborative Work

Unlike coding tasks, code-free tickets can be worked on by **multiple contributors**. In fact, the more input we receive during the planning stage, the better the final outcome.

If you're starting or leading a code-free ticket:

- Add a comment to the Planner card to let others know how to join in  
  - e.g. “Working on this in [Google Doc link] — feel free to jump in!”  

This fosters collaboration and ensures everyone stays informed.


<br/>


## 📤 Submitting & Reviewing

Once you and your collaborators feel the task is complete:

1. Move the ticket to the **In Review** column in planner
2. Share your output (e.g. link to document or screenshots)
   - Post it in the **Teams group chat**, or  
   - Present it during the **weekly meeting**
3. Gather any feedback and make final adjustments
4. A team lead will:
   - Archive/store the final documents for reference
   - Move the ticket to **Done**


<br/>


## ⚠️ Important: Contribution Requirements

> To achieve a grade **higher than a Pass**, you must also contribute to **technical (code-based) tasks**.

You’re still encouraged to work on code-free tickets, but ensure you also take on work that contributes directly to the codebase if you're aiming for a higher distinction.
