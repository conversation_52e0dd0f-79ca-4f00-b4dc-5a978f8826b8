/**
 * api.ts
 *
 * This module sets up and exports a pre-configured Axios instance (`api`)
 * for making HTTP requests to the FastAPI backend.
 *
 * It dynamically resolves the backend API URL at runtime using Expo-specific
 * metadata, allowing it to work seamlessly across different development environments:
 * - Localhost (for web or simulator)
 * - Android emulator (`********`)
 * - Physical devices on the same Wi-Fi network
 * - Expo Go (manifest2 and legacy debuggerHost support)
 *
 * This avoids hardcoding the IP address and removes the need for a dynamic app config.
 *
 * API requests will timeout after 5 seconds and log response errors to help with debugging.
 */

import axios from 'axios';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

function getApiUrl(): string {
  // 1. Try modern Expo manifest hostUri (works in most dev setups)
  const m2 = Constants.manifest2 as any;
  const hostUri = m2?.extra?.expoClient?.hostUri ?? m2?.hostUri;
  if (hostUri) {
    const host = hostUri.split(':')[0];
    return `http://${host}:8000`;
  }

  // 2. Android emulator special case
  if (Platform.OS === 'android' && !Constants.isDevice) {
    return 'http://********:8000';
  }

  // 3. Legacy Expo Go support
  const legacy = (Constants.manifest as any)?.debuggerHost as string | undefined;
  if (legacy) {
    const host = legacy.split(':')[0];
    return `http://${host}:8000`;
  }

  // 4. Fallback to localhost
  return 'http://localhost:8000';
}

export const API_URL = getApiUrl();
console.log('[API_URL]', API_URL);

const api = axios.create({
  baseURL: API_URL,
  timeout: 5000,
});

api.interceptors.response.use(
  res => res,
  err => {
    console.error('[API][Error]', {
      message: err.message,
      status: err.response?.status,
      data: err.response?.data,
      requestUrl: err.config?.url,
    });
    return Promise.reject(err);
  }
);

export default api;
