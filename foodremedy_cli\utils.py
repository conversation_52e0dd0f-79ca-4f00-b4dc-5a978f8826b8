import sys
import os
import time
import tty
import termios
from colorama import Fore, init, Style # Keep these imports here as these functions need them

# No init() call here; it will be called in run_script.py after packages are confirmed

def get_key():
    """Reads a single keypress from stdin in raw mode."""
    fd = sys.stdin.fileno()
    old_settings = termios.tcgetattr(fd)
    try:
        tty.setraw(fd)
        char1 = sys.stdin.read(1)
        if char1 == '\x1b':  # Escape sequence starts (potential arrow key)
            # Set non-blocking read with a timeout to avoid hanging
            sys.stdin.timeout = 0.1 # small timeout
            try:
                 char2 = sys.stdin.read(1)
                 if char2 == '[':
                     char3 = sys.stdin.read(1)
                     return f'\x1b[{char3}' # Handle basic arrow keys \x1b[A, \x1b[B, etc.
                 else:
                     return char1 + char2 # Some other escape sequence
            except EOFError: # Timeout occurred
                 return char1 # Was just the escape key
            finally:
                sys.stdin.timeout = -1 # Reset timeout
        else:
            return char1
    finally:
        termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)


def center_text(text, width=None):
    """Centers a string within a specified width or the terminal width."""
    if width is None:
        try:
            width = os.get_terminal_size().columns
        except OSError:
            width = 80  # Default width if terminal size cannot be determined
    padding = (width - len(text)) // 2
    return " " * padding + text + " " * (width - len(text) - padding)

def menu_with_arrows_and_numbers(options):
    """Displays a menu with arrow navigation and number selection, centered."""
    index = 0
    typed_number = ""
    while True:
        os.system("clear") # Use os.system('cls') on Windows if needed
        width = os.get_terminal_size().columns

        # Use colorama objects directly now that the module is imported at the top
        print(Fore.YELLOW + center_text("Use arrow keys (or 'w'/'s') to navigate and Enter to select,", width))
        print(Fore.YELLOW + center_text("OR type the number of your choice and press Enter.\n", width))

        for i, option in enumerate(options):
            line = f"{option} ({i+1})"
            if i == index:
                print(Fore.YELLOW + center_text(f"> {line}", width))
            else:
                print(Fore.GREEN + center_text(f"  {line}", width))

        if typed_number:
            print(Fore.CYAN + center_text(f"\nYou are typing: {typed_number}", width))

        key = get_key()

        if key == '\x1b[A' or key.lower() == 'w':  # Up arrow or 'w'
            index = (index - 1) % len(options)
            typed_number = ""  # Reset typed number on navigation
        elif key == '\x1b[B' or key.lower() == 's':  # Down arrow or 's'
            index = (index + 1) % len(options)
            typed_number = ""  # Reset typed number on navigation
        elif key.isdigit():
            typed_number += key
        elif key in ('\r', '\n'):  # Enter key
            if typed_number:
                try:
                    choice = int(typed_number)
                    if 1 <= choice <= len(options):
                        return choice - 1 # Return 0-based index
                    else:
                        print(Fore.RED + center_text(f"\nInvalid choice: {choice}. Please enter a number between 1 and {len(options)}.", width))
                        time.sleep(1)
                except ValueError:
                    print(Fore.RED + center_text("\nInvalid number entered.", width))
                    time.sleep(1)
                typed_number = ""  # Reset after processing
            else:
                return index  # Enter pressed without typing a number, select highlighted option
        elif key == '\x03':  # Ctrl + C
            # Ctrl+C handling in a raw mode menu like this is tricky.
            # A robust solution might involve signal handlers.
            # For simplicity, let's print a message and exit.
            print(Fore.RED + center_text("\nCtrl + C pressed. Exiting...", width))
            sys.exit(0)
        # Ignore other keys, potentially add feedback for invalid keys if desired
