﻿using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using Microsoft.EntityFrameworkCore;
using Food = foodremedy.database.Models.Food;
using Tag = foodremedy.database.Models.Tag;
using TagCategory = foodremedy.database.Models.TagCategory;
using foodremedy.api.tests.Factories;
using Nutrient = foodremedy.database.Models.Nutrient;

namespace foodremedy.api.tests.Controllers;

/// <summary>
/// Contains integration tests for the FoodsController in the foodremedy API.
/// Tests various HTTP responses based on authentication state, resource existence,
/// input validation, and CRUD operations on Food entities.
/// </summary>
internal class FoodsControllerTests : ControllerTestFixture
{
     /// <inheritdoc/>
    public override IFactory Factory => foodFactory;

    /// <summary>
    /// Tests for unauthenticated requests and expects Unauthorized (401) responses.
    /// </summary>
    public new static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "foods", 
            Method= HttpMethod.Get
            }).SetName("GetFoods_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetFood_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "foods/byname/name",
            Method= HttpMethod.Get
            }).SetName("GetFoodByName_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteFood_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateFood_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateFood_UnauthenticatedRequest_ReturnsUnauthorized")
    };

    /// <summary>
    /// Tests for requests authenticated via API key but lacking proper permissions. 
    /// Expects Unauthorized (401) responses.
    /// </summary>
    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteFood_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateFood_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateFood_AuthenticatedByApiKey_ReturnsUnauthorized")
    };

    /// <summary>
    /// Tests where the resource does not exist and expects NotFound (404) responses.
    /// </summary>
    public new static List<TestCaseData> NotFoundTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "foods?tagName=TestTag", 
            Method= HttpMethod.Get
            }).SetName("GetFoods_ByTagThatDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetFood_FoodDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  "foods/byname/name",
            Method= HttpMethod.Get
            }).SetName("GetFoodByName_FoodDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteFood_FoodDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"foods/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateFood_NonExistingFood_ReturnsNotFound")
    };

    /// <summary>
    /// Tests for successful deletions that result in NoContent (204) responses.
    /// </summary>
    public new static List<TestCaseData> NoContentTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "foods", 
            Use_Real_Id_In_Path = true,
            Method= HttpMethod.Delete
            }).SetName("DeleteFood_ValidRequest_ReturnsNoContent"),
    };

    /// <summary>
    /// Tests for bad requests (e.g., invalid or conflicting data) that return BadRequest (400).
    /// </summary>
    public new static List<TestCaseData> BadRequestTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "foods",
            Request_With_Properties = new{
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"IDontExist", new[] {"TestTag"}}
                    }
                },
            Method= HttpMethod.Post
            }).SetName("CreateFood_NonExistingTagCategory_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "foods",
            Add_Tag_Category_Before= new{
                    Name = "TestCategory"
                },
            Request_With_Properties = new{
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"TestCategory",new [] {"IDontExist"}}
                    }
                },
            Method= HttpMethod.Post
            }).SetName("CreateFood_NonExistingTag_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "foods",
            Request_With_Properties = new{
                    Nutrients = new Dictionary<string, int>
                    {
                        {"Nutrients", 2}
                    }
                },
            Method= HttpMethod.Post
            }).SetName("CreateFood_NonExistingNutrient_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "foods",
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = new []{"some name"}
                },
            Request_With_Properties = new{
                    Name = new []{"some name"}
                },
            Method= HttpMethod.Put
            }).SetName("UpdateFood_NoChange_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "foods",
            Use_Real_Id_In_Path = true,
            Request_With_Properties = new{
                    Tags = new Dictionary<string, IEnumerable<string>>()
                    {
                        {"IDontExist", new[]{"TestTag" } }
                    }
                },
            Method= HttpMethod.Put
            }).SetName("UpdateFood_NonExistingTagCategory_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "foods",
            Use_Real_Id_In_Path = true,
            Add_Tag_Category_Before= new{
                    Name = "TestCategory"
                },
            Request_With_Properties = new{
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"TestCategory",new [] {"IDontExist"}}
                    }
                },
            Method= HttpMethod.Put
            }).SetName("UpdateFood_NonExistingTag_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "foods",
            Use_Real_Id_In_Path = true,
            Request_With_Properties = new{
                    Nutrients = new Dictionary<string, int>
                    {
                        {"Nutrients", 2}
                    }
                },
            Method= HttpMethod.Put
            }).SetName("UpdateFood_NonExistingNutrient_ReturnsBadRequest"),
    };

    /// <summary>
    /// Tests that validate successful creation or update of food entities, expecting Created (201).
    /// </summary>
    public new static List<TestCaseData> CreatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Post,
            Add_Tag_Category_Before= new{
                    Name = "TestCategory"
                },
            Add_Tag_Before= new{
                    Name = "TestTag",
                    TagCategory = "TestCategory"
                },
            Add_Nutrient_Before= new{
                    Name = "Nutrient"
                },
            Request_With_Properties = new{
                    Description = "TestFood",
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"TestCategory", new[] {"TestTag"}}
                    },
                    Nutrients = new Dictionary<string, int>
                    {
                        {"Nutrient",1}
                    }
                },
            Check_Id_Exists = true,
            Check_Result_Properties = new{
                    Description = "TestFood",
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"TestCategory", new[] {"TestTag"}}
                    },
                    Nutrients = new Dictionary<string, int>
                    {
                        {"Nutrient",1}
                    }
                }
            }).SetName("CreateFood_WithTagsAndNutrients_ReturnsCreated"),

        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Post,
            Request_With_Properties = new{},
            Check_Id_Exists = true
            }).SetName("CreateFood_NoTagsAndNoNutrients_ReturnsCreated"),

        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Put,
            Use_Real_Id_In_Path = true,
            Add_Tag_Category_Before= new{
                    Name = "TestCategory"
                },
            Add_Tag_Before= new{
                    Name = "TestTag",
                    TagCategory = "TestCategory"
                },
            Add_Nutrient_Before= new{
                    Name = "Nutrient"
                },
            Add_Before_With_Properties = new{
                    Description = "Description"
                },
            Request_With_Properties = new{
                    Description = "Description",
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"TestCategory", new[] {"TestTag"}}
                    },
                    Nutrients = new Dictionary<string, int>
                    {
                        {"Nutrient",1}
                    }
                },
            Check_Result_Properties = new{
                    Description = "Description",
                    Tags = new Dictionary<string, IEnumerable<string>>
                    {
                        {"TestCategory", new[] {"TestTag"}}
                    },
                    Nutrients = new Dictionary<string, int>
                    {
                        {"Nutrient",1}
                    }
                }
            }).SetName("UpdateFood_WithTagsAndNutrients_ReturnsCreated"),

        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Put,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Description = "Description"
                },
            Request_With_Properties = new{
                    Description = "Description",
                },
            Check_Result_Properties = new{
                    Description = "Description",
                }
            }).SetName("UpdateFood_NoTagsOrNutrients_ReturnsCreated"),
    };

    /// <summary>
    /// Tests for successful retrieval of resources and expects Ok (200) responses.
    /// </summary>
    public new static List<TestCaseData> OkTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new{
                    Description = "name1",
                    },
                new{
                    Description = "name2",
                    },
                },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new{
                    Description = "name1",
                    },
                new{
                    Description = "name2",
                    },
                }
            }).SetName("GetFoods_ValidRequest_ReturnsOk"),

        new TestCaseData(new {
            Path=  "foods?foodSeason=Summer",
            Method= HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new{
                    Season = "Summer",
                    },
                new{
                    Season = "Winter",
                    },
                },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new{
                    FoodSeason = "Summer",
                    }
                }
            }).SetName("GetFoods_BySeason_ReturnsOk"),

        new TestCaseData(new {
            Path=  "foods",
            Method= HttpMethod.Get,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Description = "name1",
                },
            Check_Result_Properties = new{
                    Description = "name1",
                }
            }).SetName("GetFoods_ById_ReturnsOk"),
    };

    /// <summary>
    /// Retrieves a list of foods filtered by a specific tag.
    /// </summary>
    [Test]
    public async Task GetFoods_ByTag_ReturnsOk()
    {
        var category = (database.Models.TagCategory) await tagCategoryFactory.Add();
        await tagFactory.Add(new{
            Name = "TestTag",
            TagCategory = category
        });

        await foodFactory.Add(new{
            Tags = category.Tags
        }); 

        await foodFactory.Add(new{
            Tags = category.Tags
        });

        await foodFactory.Add();

        var request = new HttpRequestMessage(HttpMethod.Get, $"foods?tagName=TestTag");
        var response = await SendAuthenticatedRequestAsync(request);
        var result = await response.Content.ReadFromJsonAsync<PaginatedResponse<FoodSummary>>();

        result!.Count.Should().Be(2);
        result.Total.Should().Be(2);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

    }

    /// <summary>
    /// Retrieves a food entity by its name and checks if it matches expected values.
    /// </summary>
   [Test]
    public async Task GetFood_ByName_ReturnsOk()
    {
            var foodName = "Test Food";
            var dbFood = (database.Models.Food) await foodFactory.Add(new{
            Name = new []{foodName}
        });
        
            var request = new HttpRequestMessage(HttpMethod.Get, $"foods/byname/{foodName}");
            var response = await SendAuthenticatedRequestAsync(request);

        var result = await response.Content.ReadFromJsonAsync<IEnumerable<Models.Responses.Food>>();

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.Should().NotBeNull();
        result.Should().ContainSingle();

            var foodResult = result!.First();
            foodResult.Id.Should().Be(dbFood.Id);
            foodResult.Description.Should().Be(dbFood.Description);
            foodResult.Name.Should().ContainSingle();
            foodResult.Name.First().Should().Be(foodName);
            foodResult.FoodSeason.Should().Be(dbFood.FoodSeason);
            foodResult.FoodEnergyWithFibre.Should().Be(dbFood.FoodEnergyWithFibre); 
    }

    /// <summary>
    /// Retrieves a list of food entities sorted by name and verifies that the sorting is correct.
    /// </summary>
    [Test]
    public async Task GetFoods_SortedByName_ReturnsSortedResults()
    {
        
        var food1 = await foodFactory.Add(new
        {
            Name = new[] { "Banana" },
            Description = "A soft, sweet fruit with a yellow peel."
        });

        var food2 = await foodFactory.Add(new
        {
            Name = new[] { "Apple" },
            Description = "A crisp, juicy fruit with a sweet or tart flavor."
        });

        var food3 = await foodFactory.Add(new
        {
            Name = new[] { "Blueberries" },
            Description = "A small, sweet blue-purple fruit, rich in antioxidants."
        });

       
        var request = new HttpRequestMessage(HttpMethod.Get, "foods?sortBy=name");
        var response = await SendAuthenticatedRequestAsync(request);
        var result = await response.Content.ReadFromJsonAsync<PaginatedResponse<FoodSummary>>();

        
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.Should().NotBeNull();
        result!.Results.Should().HaveCount(3);
        result.Results.ToList()[0].Name.First().Should().Be("Apple");
        result.Results.ToList()[1].Name.First().Should().Be("Banana");
        result.Results.ToList()[2].Name.First().Should().Be("Blueberries");
    }

    /// <summary>
    /// Attempts to sort foods by an invalid parameter and ensures the original unsorted result is returned.
    /// </summary>
    [Test]
    public async Task GetFoods_SortByInvalidParameter_ReturnsUnsortedResults()
    {
        var food1 = await foodFactory.Add(new
        {
            Name = new[] { "Banana" },
            Description = "A soft, sweet fruit with a yellow peel."
        });

        var food2 = await foodFactory.Add(new
        {
            Name = new[] { "Apple" },
            Description = "A crisp, juicy fruit with a sweet or tart flavor."
        });

        var food3 = await foodFactory.Add(new
        {
            Name = new[] { "Blueberries" },
            Description = "A small, sweet blue-purple fruit, rich in antioxidants."
        });

        var request = new HttpRequestMessage(HttpMethod.Get, "foods?sortBy=invalid");
        var response = await SendAuthenticatedRequestAsync(request);
        var result = await response.Content.ReadFromJsonAsync<PaginatedResponse<FoodSummary>>();

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.Should().NotBeNull();
        result!.Results.Should().HaveCount(3);
        result.Results.ToList()[0].Name.First().Should().Be("Banana");
        result.Results.ToList()[1].Name.First().Should().Be("Apple");
        result.Results.ToList()[2].Name.First().Should().Be("Blueberries");
    }

}
