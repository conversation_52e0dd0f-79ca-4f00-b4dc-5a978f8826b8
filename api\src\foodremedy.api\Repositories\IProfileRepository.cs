using foodremedy.database;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.Repositories;

public interface IProfileRepository
{
    Task<Profile?> GetByIdAsync(Guid profileId);
    Task<Profile?> GetByUserIdAsync(Guid userId);
    Task<IEnumerable<Profile>> GetProfilesByUserIdAsync(Guid userId);
    Task<PaginatedResult<Profile>> GetAsync(int skip = 0, int take = 20, string? order = null);
    Profile Add(Profile profile);
    void Update(Profile profile);
    void Remove(Profile profile);
    Task SaveChangesAsync();
    Task<bool> ExistsAsync(Guid profileId);
}

public class ProfileRepository : IProfileRepository
{
    private readonly FoodRemedyDbContext _dbContext;

    public ProfileRepository(FoodRemedyDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Profile?> GetByIdAsync(Guid profileId)
    {
        return await _dbContext.Profile
            .Include(p => p.User)
            .SingleOrDefaultAsync(p => p.Id == profileId);
    }

    public async Task<Profile?> GetByUserIdAsync(Guid userId)
    {
        return await _dbContext.Profile
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.UserId == userId);
    }

    public async Task<IEnumerable<Profile>> GetProfilesByUserIdAsync(Guid userId)
    {
        return await _dbContext.Profile
            .Include(p => p.User)
            .Where(p => p.UserId == userId)
            .ToListAsync();
    }

    public async Task<PaginatedResult<Profile>> GetAsync(int skip = 0, int take = 20, string? order = null)
    {
        var query = _dbContext.Profile
            .Include(p => p.User)
            .AsQueryable();

        // Apply ordering
        if (!string.IsNullOrEmpty(order))
        {
            query = order.ToLower() switch
            {
                "firstname" => query.OrderBy(p => p.FirstName),
                "firstname_desc" => query.OrderByDescending(p => p.FirstName),
                "lastname" => query.OrderBy(p => p.LastName),
                "lastname_desc" => query.OrderByDescending(p => p.LastName),
                "name" => query.OrderBy(p => p.FirstName).ThenBy(p => p.LastName),
                "name_desc" => query.OrderByDescending(p => p.FirstName).ThenByDescending(p => p.LastName),
                "age" => query.OrderBy(p => p.Age),
                "age_desc" => query.OrderByDescending(p => p.Age),
                _ => query.OrderBy(p => p.FirstName).ThenBy(p => p.LastName)
            };
        }
        else
        {
            query = query.OrderBy(p => p.FirstName).ThenBy(p => p.LastName);
        }

        var total = await query.CountAsync();
        var items = await query.Skip(skip).Take(take).ToListAsync();

        return new PaginatedResult<Profile>(items.Count, total, items);
    }

    public Profile Add(Profile profile)
    {
        return _dbContext.Profile.Add(profile).Entity;
    }

    public void Update(Profile profile)
    {
        _dbContext.Profile.Update(profile);
    }

    public void Remove(Profile profile)
    {
        _dbContext.Profile.Remove(profile);
    }

    public async Task SaveChangesAsync()
    {
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> ExistsAsync(Guid profileId)
    {
        return await _dbContext.Profile.AnyAsync(p => p.Id == profileId);
    }
}
