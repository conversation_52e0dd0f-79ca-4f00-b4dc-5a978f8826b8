{"Version": 1, "Hash": "RN+SRuVzF/bXjtNTCHG/xHDvMFXdv/8ROT2pXDhw/jo=", "Source": "foodremedy.api", "BasePath": "_content/foodremedy.api", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "foodremedy.api\\wwwroot", "Source": "foodremedy.api", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\davjr8vddz-8yjfewal8n.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "swagger-custom#[.{fingerprint=8yjfewal8n}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vj80dostp", "Integrity": "dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "FileLength": 950, "LastWriteTime": "2025-08-08T09:26:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\fhkm1uvdc8-rptzmrj8w9.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/styles#[.{fingerprint=rptzmrj8w9}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "viuyvf6xzf", "Integrity": "+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "FileLength": 491, "LastWriteTime": "2025-08-08T09:26:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\plygpeghl8-3wvalnw33v.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "index#[.{fingerprint=3wvalnw33v}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cslqv4jcl", "Integrity": "tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "FileLength": 1661, "LastWriteTime": "2025-08-08T09:26:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\rtzwsgrvat-6rc0bqxwf5.gz", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/custom-swagger#[.{fingerprint=6rc0bqxwf5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9q9n7bprz7", "Integrity": "AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "FileLength": 679, "LastWriteTime": "2025-08-08T09:26:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/custom-swagger#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6rc0bqxwf5", "Integrity": "kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\custom-swagger.css", "FileLength": 1578, "LastWriteTime": "2025-08-08T09:13:10+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rptzmrj8w9", "Integrity": "0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\styles.css", "FileLength": 1426, "LastWriteTime": "2025-08-08T09:13:10+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\images\\Logo.png", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "images/Logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b6gw5pqj59", "Integrity": "HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Logo.png", "FileLength": 7437, "LastWriteTime": "2025-08-08T09:13:10+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3wvalnw33v", "Integrity": "DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 6382, "LastWriteTime": "2025-08-08T09:13:10+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "swagger-custom#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8yjfewal8n", "Integrity": "wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\swagger-custom.js", "FileLength": 2689, "LastWriteTime": "2025-08-08T09:13:10+00:00"}], "Endpoints": [{"Route": "css/custom-swagger.6rc0bqxwf5.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\rtzwsgrvat-6rc0bqxwf5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001470588235"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rc0bqxwf5"}, {"Name": "label", "Value": "css/custom-swagger.css"}, {"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}]}, {"Route": "css/custom-swagger.6rc0bqxwf5.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1578"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rc0bqxwf5"}, {"Name": "label", "Value": "css/custom-swagger.css"}, {"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}]}, {"Route": "css/custom-swagger.6rc0bqxwf5.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\rtzwsgrvat-6rc0bqxwf5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rc0bqxwf5"}, {"Name": "label", "Value": "css/custom-swagger.css.gz"}, {"Name": "integrity", "Value": "sha256-AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M="}]}, {"Route": "css/custom-swagger.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\rtzwsgrvat-6rc0bqxwf5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001470588235"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}]}, {"Route": "css/custom-swagger.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1578"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}]}, {"Route": "css/custom-swagger.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\rtzwsgrvat-6rc0bqxwf5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M="}]}, {"Route": "css/styles.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\fhkm1uvdc8-rptzmrj8w9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002032520325"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}]}, {"Route": "css/styles.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1426"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}]}, {"Route": "css/styles.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\fhkm1uvdc8-rptzmrj8w9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo="}]}, {"Route": "css/styles.rptzmrj8w9.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\fhkm1uvdc8-rptzmrj8w9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002032520325"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rptzmrj8w9"}, {"Name": "label", "Value": "css/styles.css"}, {"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}]}, {"Route": "css/styles.rptzmrj8w9.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1426"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rptzmrj8w9"}, {"Name": "label", "Value": "css/styles.css"}, {"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}]}, {"Route": "css/styles.rptzmrj8w9.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\fhkm1uvdc8-rptzmrj8w9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rptzmrj8w9"}, {"Name": "label", "Value": "css/styles.css.gz"}, {"Name": "integrity", "Value": "sha256-+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo="}]}, {"Route": "images/Logo.b6gw5pqj59.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\images\\Logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7437"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b6gw5pqj59"}, {"Name": "label", "Value": "images/Logo.png"}, {"Name": "integrity", "Value": "sha256-HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8="}]}, {"Route": "images/Logo.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\images\\Logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7437"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8="}]}, {"Route": "index.3wvalnw33v.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\plygpeghl8-3wvalnw33v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000601684717"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3wvalnw33v"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}]}, {"Route": "index.3wvalnw33v.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6382"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3wvalnw33v"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}]}, {"Route": "index.3wvalnw33v.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\plygpeghl8-3wvalnw33v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3wvalnw33v"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\plygpeghl8-3wvalnw33v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000601684717"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6382"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\plygpeghl8-3wvalnw33v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA="}]}, {"Route": "swagger-custom.8yjfewal8n.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\davjr8vddz-8yjfewal8n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001051524711"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yjfewal8n"}, {"Name": "label", "Value": "swagger-custom.js"}, {"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}]}, {"Route": "swagger-custom.8yjfewal8n.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2689"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yjfewal8n"}, {"Name": "label", "Value": "swagger-custom.js"}, {"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}]}, {"Route": "swagger-custom.8yjfewal8n.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\davjr8vddz-8yjfewal8n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yjfewal8n"}, {"Name": "label", "Value": "swagger-custom.js.gz"}, {"Name": "integrity", "Value": "sha256-dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs="}]}, {"Route": "swagger-custom.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\davjr8vddz-8yjfewal8n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001051524711"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}]}, {"Route": "swagger-custom.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2689"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}]}, {"Route": "swagger-custom.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\obj\\Debug\\net9.0\\compressed\\davjr8vddz-8yjfewal8n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs="}]}]}