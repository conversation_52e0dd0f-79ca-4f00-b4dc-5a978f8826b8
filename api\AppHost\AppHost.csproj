<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.1.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>5a6eb385-162e-41eb-9633-2a4c9d6b3600</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.1.0" />

    <PackageReference Include="Aspire.Hosting.MySql" Version="9.1.0" />
    <PackageReference Include="Aspire.Hosting.NodeJs" Version="9.1.0" />
    <PackageReference Include="Aspire.MySqlConnector" Version="9.1.0" />
    <PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="MySqlConnector" Version="2.4.0" />
    <PackageReference Include="OpenTelemetry" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.1" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="9.0.0-preview.1" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\src\foodremedy.api\foodremedy.api.csproj" />
  </ItemGroup>

</Project>
