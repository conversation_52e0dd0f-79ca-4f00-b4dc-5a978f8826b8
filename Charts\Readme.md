---

# Project Setup and Deployment

## Prerequisites

Before you start, make sure you have the following tools installed:

- **Helm**: A package manager for Kubernetes that allows you to define, install, and upgrade even the most complex Kubernetes applications.
- **Kubectl**: The Kubernetes command-line tool to interact with your Kubernetes cluster.
- **Minikube**: A tool to run Kubernetes locally.

## Creating and Installing a Helm Chart

1. **Create a Helm Chart**

   To create a new Helm chart, use the following command:

   ```bash
   helm create <chartname>
   ```

   This will generate a new directory with a sample Helm chart structure.

2. **Install the Helm Chart**

Before installing the Helm chart, navigate to the directory Charts where your Helm chart is located. Then, use the following command to install it to your Kubernetes cluster:

```bash
helm install api api-chart
```

For frontend :

```bash
helm install app .\react\
```

## Verify Deployment

1. **Check Services**

   To check if the deployment is running, use:

   ```bash
   kubectl get svc
   ```

   This will list the services in your Kubernetes cluster, including the one you deployed with Helm.

2. **Access the Service**

   If the service is running properly, you can access it by port-forwarding:

   ```bash
   kubectl port-forward svc/api-api-chart 5000:5000
   ```

   For Frontend:

```bash
kubectl port-forward svc/app-react 3000:80
```
 You can now access  backend on `http://localhost:5000/swagger` and frontend on `http://localhost:3000`.
```
