﻿using System.ComponentModel.DataAnnotations;
using foodremedy.api.Extensions;
using Microsoft.IdentityModel.Tokens;

namespace foodremedy.api.Models.Requests;

public record CreateFood(
    [Required(AllowEmptyStrings = false)] string Description,
    [Required(AllowEmptyStrings = false)] IEnumerable<string> Name,
    [Required(AllowEmptyStrings = false)] string FoodSeason,
    [Required] int FoodEnergyWithFibre,
    [Required] int FoodEnergyWithoutFibre,
    [Required] int ServingSize,
    Dictionary<string, int>? Nutrients,
    Dictionary<string, IEnumerable<string>>? Tags
) : IRequestModel, IValidatableObject
{
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        List<string> ValidSeasons = new()
        {
            "Summer", "Winter", "Fall", "Spring", "All year"
        };

        if (!ValidSeasons.Contains(FoodSeason))
            yield return new ValidationResult("Food Season must be 'Summer', 'Winter', 'Fall', 'Spring' or 'All year'");


        if (Tags.IsNullOrEmpty())
            yield break;

        foreach (KeyValuePair<string, IEnumerable<string>> tagGroup in Tags!)
            if (tagGroup.Value.IsNullOrEmpty())
                Tags.Remove(tagGroup.Key);
    }
}
