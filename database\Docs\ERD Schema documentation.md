# FOOD REMEDY API ERD SCHEMA

![alt text](https://github.com/Gopher-Industries/foodremedy-database/blob/Code-Branch/Docs/ERD%20Schema%20-%20FOOD%20REMEDY%20API.png?raw=true)

# Explanation of tables derivation

The Australian Food Composition Database (AFCD) and Food Remedy Airtable were referred to in the creation of this schema.

Specific reference files used:

- Food Remedy Airtable
- AFCD Food Details
- AFCD Nutrient
- AFCD Measure
- AFCD Recipe
- Food Standard Australia New Zealand website and Food Standard Code (https://www.foodstandards.gov.au/Pages/default.aspx)

## Food table

How was the Main food table derived:
All field from Airtable was included with reference to AFCD Food Details and Nutrient. Nutrients that are not included in the table are not of the AFCD food labelling guidelines 1.2.8 and schedule 12. However, some of the nutrients not from this guideline are included for medical purposes such as iodine for thyroid diseases.

Variable name | Label | Variable Type | Extra Attributes |
--- | --- | --- | --- |
food_id | Unique ID for each food item in the table. | Integer | Primary Key, Unique and Auto-incremental |
food_name | Layman name of the food | Character of maximum length of 50 characters | - |
food_image | URL to Image of the food in system | Character of maximum length of 255 characters | - |
food_details | Details or description of the food | Character of maximum length of 100 characters | - |
food_energyfiber | Total calories with fibre for food in kilocalories | integer | Default to 0 if no data |

## Supplier table

The supplier table and linking table are thought out of the box for basic information to register supplier information as potential users.

## Classification table

The classification table is derived from AFCD food details: food name. The food is named Raw, Process, Wild, etc. \*List to expand during data cleaning.

## Medical Table

The medical benefit stores are based on typical food avoidance for particular diseases or drug interactions.

## Allergy table

Based on FSAZN Allergen labelling

## Recipe table

Based on AFCD Recipe.

## Connecting table (ingredient, supplier list, medical list, allergy list, class list)

These tables are created to store many to many relationships between the food table and its related interconnecting tables.
