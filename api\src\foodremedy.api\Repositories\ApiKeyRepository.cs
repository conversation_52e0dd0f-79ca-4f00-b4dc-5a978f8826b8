using foodremedy.database;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.Repositories;

public interface IApiKeyRepository
{
    ApiKey Add(ApiKey apiKey);
    Task<ApiKey?> GetByIdAsync(Guid apiKeyId);
    Task SaveChangesAsync();
    Task<PaginatedResult<ApiKey>> GetAsync(int skip = 0, int take = 20);
    Task<IEnumerable<ApiKey>> GetActiveAsync();
    ApiKey? GetByNameAsync(string name);
}

public class ApiKeyRepository : IApiKeyRepository
{
    private readonly FoodRemedyDbContext _dbContext;

    public ApiKeyRepository(FoodRemedyDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public ApiKey Add(ApiKey apiKey)
    {
        return _dbContext.ApiKey.Add(apiKey).Entity;
    }

    public async Task<ApiKey?> GetByIdAsync(Guid apiKeyId)
    {
        return await _dbContext.ApiKey.SingleOrDefaultAsync(p => p.Id == apiKeyId);
    }

    public async Task<PaginatedResult<ApiKey>> GetAsync(int skip = 0, int take = 20)
    {
        var results = await _dbContext
            .ApiKey
            .Include(p => p.User)
            .Skip(skip)
            .Take(take)
            .ToListAsync();

        return new PaginatedResult<ApiKey>(results.Count, _dbContext.ApiKey.Count(), results);
    }

    public async Task<IEnumerable<ApiKey>> GetActiveAsync()
    {
        var results = await _dbContext
            .ApiKey
            .Where(p => p.Status == true)
            .ToListAsync();

        return results;
    }

    public async Task SaveChangesAsync()
    {
        await _dbContext.SaveChangesAsync();
    }

    public ApiKey? GetByNameAsync(string name)
    {
        return _dbContext.ApiKey.Where(p => p.Name == name).SingleOrDefault();
    }

}
