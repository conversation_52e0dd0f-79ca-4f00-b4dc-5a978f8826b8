{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:17226;http://localhost:15245", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "https://localhost:21043", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "https://localhost:22140"}}, "http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:15245", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19071", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20144"}}}}