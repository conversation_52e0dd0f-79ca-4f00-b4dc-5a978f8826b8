{"GlobalPropertiesHash": "Ge1uJIrzAYQHOTBJO/yCtUehhSoEPD9KPkXnLaoxBLs=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vPhj9Cxoikn+kgn3wK5qMSFVz8bGY0j/lqz4tOE5S9k=", "trgGbRf8welwGZ6BdFasSSKNavyAYkH7INIhjALoDtA=", "C9f0H5qp2LR1TlyYF7cTaqrO3FkLIgZa61+pvpSPHeY=", "FALShC6SGqiXP7sZEP//u5OSxylTgU8MbHhBLzGDc1Y=", "jebi1d/6WrQ/wArKEqdukT60IkivJIPRhChY45k9n2w=", "dV/OMhy9RDkUHnjMyaCWS+2mrL2qPFi1t3jjCJvtBs4=", "i2BAUTtJ+VKlqnmHXG5ncLlhG9amq5DJNpeKd7lBy0c=", "+VTlHGvSO4OSlB3NR5/DfY4qRkguos81+TMu4sSJjs0=", "KbP5pftXt6R89HkSF8eMBoCExzw5VKbSW8FjgRwKnzM="], "CachedAssets": {"vPhj9Cxoikn+kgn3wK5qMSFVz8bGY0j/lqz4tOE5S9k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\custom-swagger.css", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/custom-swagger#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6rc0bqxwf5", "Integrity": "kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\custom-swagger.css", "FileLength": 1578, "LastWriteTime": "2025-08-08T09:13:10.0753132+00:00"}, "trgGbRf8welwGZ6BdFasSSKNavyAYkH7INIhjALoDtA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\css\\styles.css", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "css/styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rptzmrj8w9", "Integrity": "0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\styles.css", "FileLength": 1426, "LastWriteTime": "2025-08-08T09:13:10.0763217+00:00"}, "C9f0H5qp2LR1TlyYF7cTaqrO3FkLIgZa61+pvpSPHeY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\images\\Logo.png", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "images/Logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b6gw5pqj59", "Integrity": "HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Logo.png", "FileLength": 7437, "LastWriteTime": "2025-08-08T09:13:10.0763217+00:00"}, "FALShC6SGqiXP7sZEP//u5OSxylTgU8MbHhBLzGDc1Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\index.html", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3wvalnw33v", "Integrity": "DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 6382, "LastWriteTime": "2025-08-08T09:13:10.0773188+00:00"}, "jebi1d/6WrQ/wArKEqdukT60IkivJIPRhChY45k9n2w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\swagger-custom.js", "SourceId": "foodremedy.api", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\test\\api\\src\\foodremedy.api\\wwwroot\\", "BasePath": "_content/foodremedy.api", "RelativePath": "swagger-custom#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8yjfewal8n", "Integrity": "wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\swagger-custom.js", "FileLength": 2689, "LastWriteTime": "2025-08-08T09:13:10.0773188+00:00"}}, "CachedCopyCandidates": {}}