﻿using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net;
using System.ComponentModel;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.database;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using foodremedy.api.tests.Factories;
using foodremedy.api.tests.Utils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;


namespace foodremedy.api.tests.Controllers;

public abstract class ControllerTestFixture
{
    protected HttpClient _webApiClient;
    private SqliteConnection _sqliteConnection;
    private IServiceScope _scope;
    protected static FoodRemedyDbContext DbContext;
    public static IFactory nutrientFactory;
    public static IFactory foodFactory;
    public static IFactory tagCategoryFactory;
    public static IFactory tagFactory;
    public static IFactory userFactory;
    public static AuthenticationFactory authenticationFactory;
    public static IFactory recipeFactory;


    public virtual IFactory? Factory => null;
    public static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>{};
    public static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>{};
    public static List<TestCaseData> NotFoundTests = new List<TestCaseData>{};
    public static List<TestCaseData> NoContentTests = new List<TestCaseData>{};
    public static List<TestCaseData> BadRequestTests = new List<TestCaseData>{};
    public static List<TestCaseData> ConflictTests = new List<TestCaseData>{};
    public static List<TestCaseData> CreatedTests = new List<TestCaseData>{};
    public static List<TestCaseData> OkTests = new List<TestCaseData>{};


    [Test, TestCaseSource(nameof(UnauthenticatedTests))]
    public async Task UnauthenticatedTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await _webApiClient.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Test, TestCaseSource(nameof(AuthenticatedByApiKeyTests))]
    public async Task AuthenticatedByApiKeyTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendApiKeyAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Test, TestCaseSource(nameof(NotFoundTests))]
    public async Task NotFoundTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Test, TestCaseSource(nameof(NoContentTests))]
    public async Task NoContentTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
    }

    [Test, TestCaseSource(nameof(BadRequestTests))]
    public async Task BadRequestTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Test, TestCaseSource(nameof(ConflictTests))]
    public async Task ConflictTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Conflict);
    }
    
    [Test, TestCaseSource(nameof(CreatedTests))]
    public async Task CreatedTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        bool checkId = options.GetType().GetProperty("Check_Id_Exists") !=  null ? options.Check_Id_Exists : false;
        bool checkProperties = options.GetType().GetProperty("Check_Result_Properties") !=  null; 
        if(checkId || checkProperties){
        bool checks = await ControllerTestUtils.CheckTest(response, options, Factory);
        checks.Should().Be(true);}
    }

    [Test, TestCaseSource(nameof(OkTests))]
    public async Task OkTest(dynamic options)
    {
        var request = await ControllerTestUtils.Create_Request_with_Options(options, Factory, DbContext);
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        bool checkId = options.GetType().GetProperty("Check_Id_Exists") !=  null ? options.Check_Id_Exists : false;
        bool checkProperties = options.GetType().GetProperty("Check_Result_Properties") !=  null; 
        if(checkId || checkProperties){
        bool checks = await ControllerTestUtils.CheckTest(response, options, Factory);
        checks.Should().Be(true);}
    }

    [OneTimeSetUp]
    public void OneTimeSetup()
    {
        _sqliteConnection = new SqliteConnection("Filename=:memory:");
        _sqliteConnection.Open();
        
        var factory = new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                builder.ConfigureTestServices(services =>
                {
                    // Remove the existing DbContextOptions descriptor
                    ServiceDescriptor? descriptor = services.SingleOrDefault(p =>
                        p.ServiceType == typeof(DbContextOptions<FoodRemedyDbContext>));

                    if (descriptor != null)
                        services.Remove(descriptor);

                    // Add a new DbContext with SQLite in-memory database
                    services.AddDbContext<FoodRemedyDbContext>(config =>
                    {
                        config.UseSqlite(_sqliteConnection);
                    });
                });
            });

        // Create client for sending requests
        _webApiClient = factory.CreateClient();

        // Create scope and get the DbContext
        _scope = factory.Services.CreateScope();
        DbContext = _scope.ServiceProvider.GetRequiredService<FoodRemedyDbContext>();
        DbContext.Database.EnsureCreated();

        // Initialize factories
        nutrientFactory = new NutrientFactory(DbContext);
        foodFactory = new FoodFactory(DbContext);
        tagCategoryFactory = new TagCategoryFactory(DbContext);
        tagFactory = new TagFactory(DbContext);
        userFactory = new UserFactory(DbContext);
        authenticationFactory = new AuthenticationFactory(DbContext);
        recipeFactory = new RecipeFactory(DbContext);
    }

    [TearDown]
    public void TearDown()
    {
        // Clear data from tables to ensure clean state for each test
        DbContext.Database.ExecuteSqlRaw("DELETE FROM Nutrient");
        DbContext.Database.ExecuteSqlRaw("DELETE FROM Tag");
        DbContext.Database.ExecuteSqlRaw("DELETE FROM TagCategory");
        DbContext.Database.ExecuteSqlRaw("DELETE FROM Food");
        DbContext.Database.ExecuteSqlRaw("DELETE FROM User"); 
        DbContext.Database.ExecuteSqlRaw("DELETE FROM RefreshToken");
        DbContext.Database.ExecuteSqlRaw("DELETE FROM ApiKey");
    }

    [OneTimeTearDown]
    public void OneTimeTearDown()
    {
        // Dispose of resources
        _sqliteConnection.Dispose();
        _webApiClient.Dispose();
        DbContext.Dispose();
        _scope.Dispose();
    }

    protected async Task<HttpResponseMessage> SendAuthenticatedRequestAsync(HttpRequestMessage requestMessage, string? dbidentifier = null, string? dbpassword = null)
    {
        string email = dbidentifier ?? "dev@deakin";
        string username = dbidentifier ?? "dev";
        string password = dbpassword ?? "12345";

        // Register user if not already registered
        if (dbidentifier == null || dbpassword == null)
        {
            await userFactory.Add(new{
                Email = email, 
                Password = password, 
                Username = username
            });
        }

        // Login to get the access token
        var loginResponse = await _webApiClient.PostAsync("auth/login", JsonContent.Create(authenticationFactory.CreateLogin(email: email, password: password)));
        var result = await loginResponse.Content.ReadFromJsonAsync<AccessTokenCreated>();

        if (result == null)
        {
            throw new InvalidOperationException("Failed to retrieve access token.");
        }

        // Add the token to the request header
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", result.AccessToken);

        return await _webApiClient.SendAsync(requestMessage);
    }

    protected async Task<HttpResponseMessage> SendApiKeyAuthenticatedRequestAsync(HttpRequestMessage requestMessage, string? apiKey = null)
    {    
        string? key = null;

        if(apiKey == null)
        {
            var user = (database.Models.User) await userFactory.Add();
            var request = new HttpRequestMessage(HttpMethod.Post, $"auth/{user.Id}/api-key"){
                Content = JsonContent.Create(authenticationFactory.CreateApiKey())
            };
            var response = await SendAuthenticatedRequestAsync(request);
            var result = await response.Content.ReadFromJsonAsync<string>();
            key = result!; //public IEnumerable<Tag> Tags { get; set; }
        }
        else
        {
            key = apiKey;
        }
        
        requestMessage.Headers.Add("X-Api-Key", key);

        return await _webApiClient.SendAsync(requestMessage); 
    }
}
