import sys
import os

help = '''Valid parameters for this command are:
    Running all migrations:
e.g.    python db_migrate.py update

    Creating a new migration:
e.g.    python db_migrate.py add NameForYourMigration
'''

os.system("docker-compose up -d db")

os.chdir("api/src")

if len(sys.argv) > 1:
    command = sys.argv[1].lower()
    if command == "update":
        os.system(f"dotnet ef database {command} --project foodremedy.database --startup-project foodremedy.api")
    elif command == "add":
        if len(sys.argv) > 2:
            name = sys.argv[2]
            print("Current working directory:", os.getcwd())
            os.system(f"dotnet ef migrations {command} {name} --project foodremedy.database --startup-project foodremedy.api")
            print("Migration has been added. To apply your new migration run:")
            print("\tpython db_migrate.py update")
        else:
            print("Must include a name to Add a migration")
            print("e.g:\tpython db_migrate.py add NameForYourMigration")
    else:
        print(command, "is not a valid parameter")
        print(help)
else:
    print("Please include a parameter to run this command")
    print(help)