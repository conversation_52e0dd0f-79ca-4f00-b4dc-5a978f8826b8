// UI Input tsx

import React, { useState } from "react";
import { TextInput, StyleSheet, View, StyleProp, TextStyle, TextInputProps } from "react-native";

interface InputProps extends TextInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholderTextColor?: string;
  className?: string;
  style?: StyleProp<TextStyle>;
}

const Input = ({ value, onChangeText, placeholderTextColor = "#808080", className = "", style, ...props }: InputProps) => {
  const [isFocused, setIsFocused] = useState(false);

  const focusClass = isFocused ? "border-[#FF3F3F]" : "border-hsl70";

  return (
    <TextInput
      value={value}
      placeholderTextColor={placeholderTextColor}
      onChangeText={onChangeText}
      className={`text-black text-base font-inter px-4 py-2 bg-hsl98 border rounded ${className} ${focusClass}`}
      style={style}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      cursorColor='hsl(0, 100%, 62%)'
      {...props}
    />
  );
};

export default Input;


