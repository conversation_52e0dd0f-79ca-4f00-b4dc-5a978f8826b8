// UIText

import React from 'react';
import { Text, StyleProp, TextStyle } from 'react-native';

interface TtProps {
  onPress?: () => void;
  className?: string;
  style?: StyleProp<TextStyle>;
  children?: any;
}

const Tt = ({ onPress, style = {}, className = '', children, ...props }: TtProps) => {
  return (
    <Text
      {...props}
      className={`text-hsl5 font-inter ${className}`}
      style={style}
      onPress={onPress}>
      {children}
    </Text>
  );
};

export default Tt;

