using System.Linq;
using System.Net;
using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Food = foodremedy.api.Models.Responses.Food;
using TagCategory = foodremedy.database.Models.TagCategory;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("[controller]")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class FoodsController : ControllerBase
{
    private readonly IFoodRepository _foodRepository;
    private readonly INutrientRepository _nutrientRepository;
    private readonly ITagCategoryRepository _tagCategoryRepository;
    private readonly ITagRepository _tagRepository;

    public FoodsController(IFoodRepository foodRepository,
        ITagCategoryRepository tagCategoryRepository,
        ITagRepository tagRepository,
        INutrientRepository nutrientRepository)
    {
        _foodRepository = foodRepository;
        _tagCategoryRepository = tagCategoryRepository;
        _tagRepository = tagRepository;
        _nutrientRepository = nutrientRepository;
    }

    /// <summary>
    /// Gets a list of all foods
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <param name="foodSeason">The season to filter by</param>
    /// <param name="nutrientName">The nutrient to filter by</param>
    /// <param name="tagName">The tag to filter by</param>
    /// <param name="sortBy">The parameter value to sort by in ascending order (name, season, description, energywithfibre, energywithoutfibre, servingsize)</param> 
    /// <response code="200">Returns the list of Foods</response>
    /// <response code="404">One of the filtering paramters does not exist</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]

    public async Task<ActionResult<PaginatedResponse<FoodSummary>>> GetFoods(
    [FromQuery] PaginationRequest paginationRequest,
    [FromQuery] string? tagName,
    [FromQuery] string? foodSeason,
    [FromQuery] string? nutrientName,
    [FromQuery] string? sortBy)
    {
        // Normalize foodSeason to handle case insensitivity and replace "Autumn" with "Fall"
        if (!string.IsNullOrEmpty(foodSeason))
        {
            foodSeason = foodSeason.Trim().ToLower();
            if (foodSeason == "autumn")
            {
                foodSeason = "fall";
            }
            // Capitalize the first letter to match the expected values in the database
            foodSeason = char.ToUpper(foodSeason[0]) + foodSeason.Substring(1);
        }

        // If nutrientName is provided, check if the nutrient exists
        if (!string.IsNullOrEmpty(nutrientName))
        {
            var nutrient = await _nutrientRepository.GetByNameAsync(nutrientName);

            if (nutrient == null)
                return NotFound($"Nutrient '{nutrientName}' not found.");

            // Get foods by nutrient
            PaginatedResult<database.Models.Food> nutrientResults;

            if (string.IsNullOrEmpty(foodSeason))
            {
                if (string.IsNullOrEmpty(tagName))
                {
                    nutrientResults = await _foodRepository.GetByNutrientAsync(nutrientName, paginationRequest.Skip, paginationRequest.Take);
                }
                else
                {
                    nutrientResults = await _foodRepository.GetByTagAndNutrientAsync(tagName, nutrientName, paginationRequest.Skip, paginationRequest.Take);
                }
            }
            else
            {
                if (string.IsNullOrEmpty(tagName))
                {
                    nutrientResults = await _foodRepository.GetByNutrientAndSeasonAsync(nutrientName, foodSeason, paginationRequest.Skip, paginationRequest.Take);
                }
                else
                {
                    nutrientResults = await _foodRepository.GetByTagNutrientAndSeasonAsync(tagName, nutrientName, foodSeason, paginationRequest.Skip, paginationRequest.Take);
                }
            }
            
            var sortedResults = SortResults(nutrientResults, sortBy);
            return Ok(sortedResults.ToResponseModel(p => p.ToSummaryResponseModel()));
        }

        // Handle filtering by tagName and foodSeason if nutrientName is not provided
        if (tagName == null)
        {
            PaginatedResult<database.Models.Food> fullResults;

            if (string.IsNullOrEmpty(foodSeason))
            {
                fullResults = await _foodRepository.GetAsync(paginationRequest.Skip, paginationRequest.Take);
            }
            else
            {
                fullResults = await _foodRepository.GetBySeasonAsync(foodSeason, paginationRequest.Skip, paginationRequest.Take);
            }

            var sortedResults = SortResults(fullResults, sortBy);
            return Ok(sortedResults.ToResponseModel(p => p.ToSummaryResponseModel()));
        }

        database.Models.Tag? tag = _tagRepository.GetByNameAsync(tagName);

        if (tag == null)
            return NotFound();

        PaginatedResult<database.Models.Food> results;

        if (string.IsNullOrEmpty(foodSeason))
        {
            results = await _foodRepository.GetByTagAsync(tagName, paginationRequest.Skip, paginationRequest.Take);
        }
        else
        {
            results = await _foodRepository.GetByTagAndSeasonAsync(tagName, foodSeason, paginationRequest.Skip, paginationRequest.Take);
        }


        return Ok(SortResults(results, sortBy).ToResponseModel(p => p.ToSummaryResponseModel()));
    }

    /// <summary>
    /// Gets a food by ID
    /// </summary>
    /// <param name="foodId">The ID of the food</param>
    /// <returns> Food associated with inputted ID</returns>
    /// <response code="200">Returns the food</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Food does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{foodId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Food>> GetFood([FromRoute] Guid foodId)
    {
        database.Models.Food? result = await _foodRepository.GetByIdAsync(foodId);

        if (result == null)
            return NotFound();

        return Ok(result.ToResponseModel());
    }

    /// <summary>
    /// Gets foods by name
    /// </summary>
    /// <param name="name">The name of the food</param>
    /// <returns> The foods associated with the name</returns>
    /// <response code="200">Returns the foods</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">No food has that name</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("byname/{name}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<Food>>> GetFoodByName([FromRoute] string name)
    {
        var foods = await _foodRepository.GetByNameAsync(name);
        var firstfood = foods.FirstOrDefault();

        if (firstfood == null)
            return NotFound();

        return Ok(foods.Select(f => f!.ToResponseModel()));

    }

    /// <summary>
    /// Creates a new food
    /// </summary>
    /// <param name="createFood">The food to be added</param>
    /// <returns> The successfully created food</returns>
    /// <response code="201">Returns the created food</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="400">The createFood is null or if one of its properties is invalid</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Food>> CreateFood([FromBody] CreateFood createFood)
    {
        database.Models.Food? result;
        if (!createFood.Nutrients.IsNullOrEmpty())
        {
            foreach (KeyValuePair<string, int> nutrient in createFood.Nutrients!)
            {

                database.Models.Nutrient? dbNutrient = await _nutrientRepository.GetByNameAsync(nutrient.Key);

                if (dbNutrient == null)

                    return BadRequest(new ProblemDetails
                    {
                        Title = "Bad Request",
                        Status = (int)HttpStatusCode.BadRequest,
                        Detail = $"The nutrient {nutrient.Key} does not exist"
                    });
            }
        }

        if (createFood.Tags.IsNullOrEmpty())
        {
            result = _foodRepository.Add(createFood.ToDbModel());
            await _foodRepository.SaveChangesAsync();

            return Created($"/foods/{result.ToResponseModel().Id}", result.ToResponseModel());
        }

        var tagCategories = new List<TagCategory>();

        foreach (KeyValuePair<string, IEnumerable<string>> tagCategory in createFood.Tags!)
        {
            TagCategory? dbCategory = await _tagCategoryRepository.GetByName(tagCategory.Key);

            if (dbCategory == null)
                return BadRequest(new ProblemDetails
                {
                    Title = "Bad Request",
                    Status = (int)HttpStatusCode.BadRequest,
                    Detail = $"The tag type {tagCategory.Key} is invalid"
                });

            IEnumerable<string> invalidCategories = tagCategory.Value.Except(dbCategory.Tags!.Select(p => p.Name));
            if (invalidCategories.Any())
                return BadRequest(new ProblemDetails
                {
                    Title = "Bad Request",
                    Status = (int)HttpStatusCode.BadRequest,
                    Detail = $"The following tags are invalid: {string.Join(", ", invalidCategories)}"
                });

            tagCategories.Add(dbCategory);
        }

        result = _foodRepository.Add(createFood.ToDbModel(tagCategories));

        await _foodRepository.SaveChangesAsync();

        return Created($"/foods/{result.ToResponseModel().Id}", result.ToResponseModel());
    }

    /// <summary>
    /// Updates a food
    /// </summary>
    /// <param name="foodId">Current ID of the food</param>
    /// <param name="updateFood">The updated food details</param>
    /// <returns>The successfully updated food</returns>
    /// <response code="201">Returns the updated food</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="400">The updateFood is null or if one of its properties is invalid</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPut("{foodId:guid}")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Food>> UpdateFood([FromRoute] Guid foodId, [FromBody] CreateFood updateFood)
    {
        database.Models.Food? result;

        database.Models.Food? dbFood = await _foodRepository.GetByIdAsync(foodId);

        if (!updateFood.Nutrients.IsNullOrEmpty())
        {
            foreach (KeyValuePair<string, int> nutrient in updateFood.Nutrients!)
            {

                database.Models.Nutrient? dbNutrient = await _nutrientRepository.GetByNameAsync(nutrient.Key);

                if (dbNutrient == null)

                    return BadRequest(new ProblemDetails
                    {
                        Title = "Bad Request",
                        Status = (int)HttpStatusCode.BadRequest,
                        Detail = $"The nutrient {nutrient.Key} does not exist"
                    });
            }
        }

        string Nutrients = "";
        if (!updateFood.Nutrients.IsNullOrEmpty())
        {
            foreach (KeyValuePair<string, int> keyValues in updateFood.Nutrients!)
            {
                Nutrients += keyValues.Key + ":" + keyValues.Value.ToString() + ",";
            }
            Nutrients = Nutrients.TrimEnd(',');
        }

        if (dbFood != null)
        {
            if (dbFood.Description != updateFood.Description || !Enumerable.SequenceEqual(dbFood.Name, updateFood.Name) || dbFood.FoodSeason != updateFood.FoodSeason || dbFood.FoodEnergyWithFibre != updateFood.FoodEnergyWithFibre || dbFood.FoodEnergyWithoutFibre != updateFood.FoodEnergyWithoutFibre || dbFood.Nutrients != Nutrients || dbFood.ServingSize != updateFood.ServingSize)
            {
                dbFood.Description = updateFood.Description;
                dbFood.Name = updateFood.Name;
                dbFood.FoodSeason = updateFood.FoodSeason;
                dbFood.FoodEnergyWithFibre = updateFood.FoodEnergyWithFibre;
                dbFood.FoodEnergyWithoutFibre = updateFood.FoodEnergyWithoutFibre;
                dbFood.ServingSize = updateFood.ServingSize;
                dbFood.Nutrients = Nutrients;

                if (updateFood.Tags.IsNullOrEmpty())
                {
                    dbFood.Tags = new List<database.Models.Tag>();

                    result = _foodRepository.Update(dbFood);
                    await _foodRepository.SaveChangesAsync();

                    return Created($"/foods/{result.ToResponseModel().Id}", result.ToResponseModel());
                }

                var tagList = new List<database.Models.Tag>();
                foreach (KeyValuePair<string, IEnumerable<string>> tagCategory in updateFood.Tags!)
                {
                    TagCategory? dbCategory = await _tagCategoryRepository.GetByName(tagCategory.Key);

                    if (dbCategory == null)
                    {
                        return BadRequest(new ProblemDetails
                        {
                            Title = "Bad Request",
                            Status = (int)HttpStatusCode.BadRequest,
                            Detail = $"The tag type {tagCategory.Key} is invalid"
                        });
                    }

                    IEnumerable<string> invalidCategories = tagCategory.Value.Except(dbCategory.Tags!.Select(p => p.Name));
                    if (invalidCategories.Any())
                    {
                        return BadRequest(new ProblemDetails
                        {
                            Title = "Bad Request",
                            Status = (int)HttpStatusCode.BadRequest,
                            Detail = $"The following tags are invalid: {string.Join(", ", invalidCategories)}"
                        });
                    }

                    foreach (string tagName in tagCategory.Value)
                    {
                        database.Models.Tag tag = _tagRepository.GetByNameAsync(tagName)!;

                        tagList.Add(tag);
                    }

                    dbFood.Tags = tagList;
                }

                result = _foodRepository.Update(dbFood);
                await _foodRepository.SaveChangesAsync();

                return Created($"/foods/{result.ToResponseModel().Id}", result.ToResponseModel());
            }

            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = "Must change a field to update food"
            });
        }

        return NotFound();
    }

    /// <summary>
    /// Deletes a food
    /// </summary>
    /// <param name="foodId">The ID of the food to be deleted</param>
    /// <response code="204">Deletes food</response>
    /// <response code="404">Food does not exist</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpDelete("{foodId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Food>> DeleteFood([FromRoute] Guid foodId)
    {

        database.Models.Food? dbFood = await _foodRepository.GetByIdAsync(foodId);

        if (dbFood != null)
        {
            _foodRepository.Remove(dbFood);
            await _foodRepository.SaveChangesAsync();
            return NoContent();
        }

        return NotFound();
    }


    // This is the SortResults Method which is used to order the returned food results in ascending order depending on the parameter entered
    private PaginatedResult<database.Models.Food> SortResults(PaginatedResult<database.Models.Food> results, string? sortBy)
    {
        if (string.IsNullOrEmpty(sortBy)) return results;

        try
        {
            // Sort the items based on the sortBy parameter, if cannot be found - do not sort
            var sortedItems = sortBy.ToLower() switch
            {
                "name" => results.Results.OrderBy(food => food.Name.FirstOrDefault()).ToList(),
                "season" => results.Results.OrderBy(food => food.FoodSeason).ToList(),
                "description" => results.Results.OrderBy(food => food.Description).ToList(),
                "energywithfibre" => results.Results.OrderBy(food => food.FoodEnergyWithFibre).ToList(),
                "energywithoutfibre" => results.Results.OrderBy(food => food.FoodEnergyWithoutFibre).ToList(),
                "servingsize" => results.Results.OrderBy(food => food.ServingSize).ToList(),
                _ => results.Results // No sorting for invalid sortBy value
            };

            return new PaginatedResult<database.Models.Food>(results.Count,results.Total, sortedItems);
        }
        catch
        {
            return new PaginatedResult<database.Models.Food>(results.Count,results.Total, results.Results);
        }
    }
    
    /// <summary>
    /// Gets all foods by season
    /// </summary>
    /// <param name="season">The season to filter by</param>
    /// <returns>The list of foods associated with the season</returns>
    /// <response code="200">Returns the list of foods</response>
    /// <response code="400">Season parameter cannot be empty</response>
    /// <response code="404">No foods found for the season</response>
        
    [HttpGet("season/{season}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<Food>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    
    public async Task<ActionResult<IEnumerable<Food>>> GetFoodBySeason(string season)
{
    if (string.IsNullOrEmpty(season))
    {
        return BadRequest("Season parameter is required.");
    }

    // Await the repository call
    var foodsBySeason = await _foodRepository.GetFoodBySeason(season);

    // Ensure that the result is not null or empty
    if (foodsBySeason == null || !foodsBySeason.Any())
    {
        return NotFound($"No foods found for the season: {season}");
    }

    return Ok(foodsBySeason);
}
    
}

