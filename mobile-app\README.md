# 📱 Food Remedy Mobile App

This is the React Native frontend for the FoodRemedy platform.  
It uses **Expo** for fast development and cross-platform compatibility.  

## ⚙️ Prerequisites
Make sure you have:

- [Node.js](https://nodejs.org/) installed
- [Expo CLI](https://docs.expo.dev/get-started/installation/):  
   ```bash
   npm install -g expo-cli
   ```
- (Optional) Install the Expo Go app on your iOS or Android device to preview the app via QR code.


<br/>


## 🚀 Getting Started
1. Install dependencies:
   ```bash
   npm install
   ```
2. Start the app:
   ```bash
   npx expo start
   ```
This will launch the development server and display a QR code. Scan it with Expo Go, or choose one of the following:
 - Press `i` to open in iOS Simulator (macOS only)
 - Press `a` to open in Android Emulator (if available)
 - Press `w` to open in the browser (Web preview)


<br/>


## 🛠 Development Notes
In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).


<br/>


## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.
