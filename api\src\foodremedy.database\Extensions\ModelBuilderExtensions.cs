﻿using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace foodremedy.database.Extensions;

public static class ModelBuilderExtensions
{
    public static void ConfigureUsers(this ModelBuilder builder)
    {
        builder.Entity<User>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Email).IsRequired();
            model.Property(p => p.Username).IsRequired();
            model.Property(p => p.PasswordHash).IsRequired();
            model.Property(p => p.Status).IsRequired();
            model.HasIndex(p => p.Email).IsUnique();
            model.HasIndex(p => p.Username).IsUnique();

            model.HasOne<RefreshToken>().WithOne();
            model.HasMany(p => p.ApiKeys);
        });
    }

    public static void ConfigureRefreshTokens(this ModelBuilder builder)
    {
        builder.Entity<RefreshToken>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Token).IsRequired();
            model.HasIndex(p => p.Token).IsUnique();
        });
    }

    public static void ConfigureApiKeys(this ModelBuilder builder)
    {
        builder.Entity<ApiKey>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Token).IsRequired();
            model.Property(p => p.Status).IsRequired();
            model.Property(p => p.Name).IsRequired();
            model.HasIndex(p => p.Token).IsUnique();
        });
    }

    public static void ConfigureTags(this ModelBuilder builder)
    {
        builder.Entity<Tag>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Name).IsRequired();
            model.Property(p => p.Description).IsRequired();
        });
    }

    public static void ConfigureNutrients(this ModelBuilder builder)
    {
        builder.Entity<Nutrient>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Name).IsRequired();
            model.Property(p => p.Description).IsRequired();
        });
    }

    public static void ConfigureFoods(this ModelBuilder builder)
    {
        builder.Entity<Food>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Description).IsRequired();
            model.Property(p => p.FoodSeason).IsRequired();
            model.Property(p => p.FoodEnergyWithFibre).IsRequired();
            model.Property(p => p.FoodEnergyWithoutFibre).IsRequired();
            model.Property(p => p.ServingSize).IsRequired();
            model.Property(p => p.Nutrients);
            model.Property(p => p.Name)
                .HasConversion(
                    v => string.Join(',', v ?? Enumerable.Empty<string>()),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                );
            model.HasMany<Tag>(p => p.Tags).WithMany();
        });
    }

    public static void ConfigureTagCategories(this ModelBuilder builder)
    {
        builder.Entity<TagCategory>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Name).IsRequired();
            model.HasIndex(p => p.Name).IsUnique();
            model
                .HasMany(p => p.Tags)
                .WithOne(p => p.TagCategory)
                .HasForeignKey("TagCategoryId")
                .IsRequired();
        });
    }

public static void ConfigureAllergies(this ModelBuilder builder)
{
     builder.Entity<Allergy>(model =>
        {
            model.HasKey(p => p.Id);
            model.Property(p => p.Name).IsRequired();
            model.Property(p => p.Description).IsRequired(false);
        });
}

    public static void ConfigureProfiles(this ModelBuilder builder)
    {
        builder.Entity<Profile>(model =>
        {
            model.ToTable("Profile");
            model.HasKey(p => p.Id);
            
            // Configure properties with proper GUID handling for MySQL
            model.Property(p => p.Id)
                .HasColumnType("CHAR(36)")
                .IsRequired();
                
            model.Property(p => p.UserId)
                .HasColumnType("CHAR(36)")
                .IsRequired();
                
            model.Property(p => p.FirstName)
                .HasMaxLength(50)
                .IsRequired();
            model.Property(p => p.LastName)
                .HasMaxLength(50)
                .IsRequired();
            model.Property(p => p.Status)
                .IsRequired();
            model.Property(p => p.Relationship)
                .HasMaxLength(50)
                .IsRequired(false);
            model.Property(p => p.Age)
                .IsRequired(false);
            model.Property(p => p.AvatarUrl)
                .HasMaxLength(255)
                .IsRequired(false);
            model.Property(p => p.Allergies)
                .HasConversion(
                    v => v == null ? null : string.Join(',', v),
                    v => v == null ? null : v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .IsRequired(false);
            model.Property(p => p.Intolerances)
                .HasConversion(
                    v => v == null ? null : string.Join(',', v),
                    v => v == null ? null : v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .IsRequired(false);
            model.Property(p => p.DietaryForm)
                .HasMaxLength(100)
                .IsRequired(false);

            // Configure the foreign key relationship
            model.HasOne(p => p.User)
                .WithMany()
                .HasForeignKey(p => p.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }

}
