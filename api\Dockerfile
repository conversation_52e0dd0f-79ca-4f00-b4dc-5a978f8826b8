# # syntax=docker/dockerfile:1

# FROM mcr.microsoft.com/dotnet/sdk:9.0 as build-env
# WORKDIR /src
# COPY . .
# RUN dotnet restore
# RUN dotnet publish -c Release -o /publish

# FROM mcr.microsoft.com/dotnet/aspnet:9.0 as runtime
# WORKDIR /publish
# COPY --from=build-env /publish .
# EXPOSE 5000
# ENTRYPOINT ["dotnet", "foodremedy.api.dll"]

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

COPY foodremedy.api.sln ./
COPY src ./src
COPY AppHost ./AppHost
COPY ServiceDefaults ./ServiceDefaults

RUN dotnet restore foodremedy.api.sln
RUN dotnet publish AppHost/AppHost.csproj -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/aspnet:9.0
WORKDIR /publish
COPY --from=build /publish .

ENTRYPOINT ["dotnet", "AppHost.dll"]
