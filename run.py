#!/usr/bin/env python3

import os
import shutil
import sys
import subprocess
import time
import getpass # To get the current system username reliably

def find_python_executable():
    """Finds the preferred Python executable ('python3' or 'python')."""
    executables_to_try = ['python3', 'python']
    for executable in executables_to_try:
        try:
            # Use subprocess.run with a simple command to check if the executable works
            # check=True will raise CalledProcessError if the command fails
            # We use sys.version_info to ensure it's a Python 3 interpreter
            result = subprocess.run([executable, "-c", "import sys; assert sys.version_info.major == 3"],
                                   check=True, capture_output=True, text=True)
            print(f"Using Python executable: {executable}")
            return executable
        except (subprocess.CalledProcessError, FileNotFoundError):
            # If the command failed or the executable wasn't found, try the next one
            continue
    # If neither worked, raise an error
    raise RuntimeError("Could not find a suitable Python 3 executable ('python3' or 'python'). "
                       "Please ensure Python 3 is installed and in your system's PATH.")

try:
    # Find the correct Python executable once at the start
    PYTHON_EXECUTABLE = find_python_executable()
except RuntimeError as e:
    print(f"Initialization Error: {e}")
    sys.exit(1)


def install_package(package):
    subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def is_virtual_env():
    return (hasattr(sys, 'real_prefix') or 
            (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))

def create_virtual_env():
    if not is_virtual_env():
        venv_dir = os.path.join(os.path.dirname(__file__), ".venv")
        if not os.path.exists(venv_dir):
            subprocess.check_call([sys.executable, "-m", "venv", venv_dir])
            print("Virtual environment created. Please run 'source .venv/bin/activate' to activate the virtual environment.")
            proc = subprocess.Popen(["bash", "-c", "source .venv/bin/activate"], stdin=subprocess.PIPE, stdout=subprocess.PIPE)
            proc.wait()
            print("Virtual environment has been activated.")
    else:
        print("Virtual environment already created.")

try:
    import colorama
    import psutil
    from colorama import init, Fore, Style
    init(autoreset=True)
except ImportError:
    create_virtual_env()
    install_package("colorama")
    install_package("psutil")
    import colorama
    import psutil
    from colorama import init, Fore, Style
    init(autoreset=True)

def run_dotnet_watch():
    try:
        # Start Redis container (only if not running)
        print(Fore.CYAN + "Starting Redis container...")
        subprocess.run(["docker", "inspect", "-f", "{{.State.Running}}", "redis"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        redis_status = subprocess.run(["docker", "ps", "-q", "-f", "name=redis"], stdout=subprocess.PIPE).stdout.decode().strip()

        if not redis_status:
            subprocess.run(["docker", "run", "--name", "redis", "-p", "6379:6379", "-d", "redis"])
            print(Fore.GREEN + "Redis container started successfully.")
        else:
            print(Fore.YELLOW + "Redis container is already running.")

        # Start .NET API with dotnet watch
        print(Fore.GREEN + "Starting .NET API with dotnet watch...")
        dotnet_process = subprocess.Popen(
            ["dotnet", "watch", "run", "--project", "api/AppHost"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        for line in iter(dotnet_process.stdout.readline, b''):
            print(line.decode('utf-8').strip())

        log("logs/foodremedy_api.log")

    except KeyboardInterrupt:
        print(Fore.RED + "Terminating .NET API and Redis container...")
        os.system("pkill -f 'dotnet watch run --project api/AppHost'")
        subprocess.run(["docker", "stop", "redis"])
        subprocess.run(["docker", "rm", "redis"])
        print(Fore.GREEN + "Dotnet process and Redis container terminated.")

def log(filename):
    while not os.path.isfile(filename):
        time.sleep(0.1)
    with open(filename) as log:
        log.seek(0, 2)
        while True:
            line = log.readline()
            if not line:
                time.sleep(0.1)
                continue
            print(Style.DIM + line, end="")
            if "Now listening on:" in line:
                local_address = line.split("Now listening on: ")[1].strip()
                print(Fore.GREEN + f"Local host address: {local_address}")

def check_docker_status():
    try:
        result = subprocess.run(["docker", "ps", "--filter", "name=db", "--format", "table {{.Names}}\t{{.Status}}"], capture_output=True, text=True, check=True)
        output = result.stdout.strip()
        if output:
            print(Fore.GREEN + "\nDocker Container Status:")
            print(output)
        else:
            print(Fore.RED + "\nNo Docker container with the name 'db' found running.")
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError checking Docker status: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def run_docker_db_daemon():
    try:
        print(Fore.GREEN + "Starting Docker container in daemon mode...")
        subprocess.run(["docker-compose", "up", "-d", "db"], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError starting Docker container: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def stop_docker_db():
    try:
        print(Fore.GREEN + "Stopping Docker container...")
        subprocess.run(["docker-compose", "stop", "db"], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError stopping Docker container: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def print_tables_in_database():
    try:
        print(Fore.GREEN + "Fetching tables in the database...")
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", "SHOW TABLES;", "foodremedy"],
            capture_output=True, text=True, check=True
        )
        print(Fore.GREEN + "\nTables in the 'foodremedy' database:")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        if "service \"db\" is not running" in str(e.stderr):
            print(Fore.RED + "\nError: Please run the database service first by choosing option 3 in the main menu.")
        else:
            print(Fore.RED + f"\nError fetching tables: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def print_table_data_in_database():
    try:
        print(Fore.GREEN + "Fetching tables in the database...")
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", "SHOW TABLES;", "foodremedy"],
            capture_output=True, text=True, check=True
        )
        tables = {}
        for line in result.stdout.splitlines():
            if line != "Tables_in_foodremedy":
                tables[line] = None
        print(Fore.GREEN + "\nTables in the 'foodremedy' database:")
        for i, table in enumerate(tables.keys(), start=1):
            print(f"{i}. {table}")
        print(f"{len(tables) + 1}. Back")
        while True:
            try:
                choice = int(input("Enter the number to select a table to view its data: "))
                if 1 <= choice <= len(tables):
                    break
                else:
                    print(Fore.RED + "\nInvalid choice. Please enter a number between 1 and", len(tables))
            except ValueError:
                print(Fore.RED + "\nInvalid input. Please enter a number")
        table = list(tables.keys())[choice - 1]
        print(Fore.GREEN + f"\nFetching data from table '{table}'...")
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", f"SELECT * FROM {table};", "foodremedy"],
            capture_output=True, text=True, check=True
        )
        if result.stdout.strip() == "":
            print(Fore.YELLOW + "\nNo data in the table. Please add data to the table.")
        else:
            print(Fore.GREEN + "\nData in the table:")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        if "service \"db\" is not running" in str(e.stderr):
            print(Fore.RED + "\nError: Please run the database service first by choosing option 3 in the main menu.")
        else:
            print(Fore.RED + f"\nError fetching tables: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def trust_dotnet_dev_cert():
    try:
        print(Fore.GREEN + "Running dotnet command to trust the HTTPS development certificate...")
        subprocess.run(["dotnet", "dev-certs", "https", "--trust"], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError running dotnet command: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: dotnet command not found. Please ensure .NET SDK is installed and in your system's PATH.")

def populate_database():
    try:
        print(Fore.GREEN + "Populating the database with data from the sql file...")
        sql_file = "database/code/db_sampledata_Active.sql"
        if not os.path.exists(sql_file):
            print(Fore.YELLOW + "\nSql file not found: " + sql_file)
            return
        print(Fore.GREEN + f"Using sql file: {sql_file}")
        with open(sql_file, "r") as f:
            queries = f.read().split(";")
        for query in queries:
            if query.strip() != "":
                result = subprocess.run(
                    ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", query, "foodremedy"],
                    capture_output=True, text=True, check=True
                )
                print(Fore.GREEN + f"Executed query: {query}")
        print(Fore.GREEN + "\nDatabase has been populated with data.")
    except subprocess.CalledProcessError as e:
        if "service \"db\" is not running" in str(e.stderr):
            print(Fore.RED + "\nError: Please run the database service first by choosing option 3 in the main menu.")
        else:
            print(Fore.RED + f"\nError populating the database: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")
        
def run_migrations():
    try:
        print(Fore.GREEN + "Running migrations for the database...")
        subprocess.run(["python", "db_migrate.py", "update"], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError running migration: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Python command not found. Please ensure Python is installed and in your system's PATH.")

def create_new_migration(migration_name=None):
    """Creates a new migration using db_migrate.py, optionally with a provided name."""
    try:
        print(Fore.GREEN + "Creating a new migration...")
        # Check if db container is running first (migration creation might need it, depends on db_migrate.py)
        # db_status = subprocess.run(["docker", "ps", "-q", "-f", "name=db"], capture_output=True, text=True).stdout.strip()
        # if not db_status:
        #     print(Fore.RED + "\nError: Database container 'db' is not running. Please start it first (option 3).")
        #     # Decide if migration creation *requires* the DB to be running. Often it just generates files.
        #     # Assuming it only generates files for now, no check needed. If it needs DB, uncomment above.
            # return # Uncomment if DB is required

        if migration_name is None:
             migration_name = input(Fore.GREEN + "Enter the name of the migration: ")
             if not migration_name:
                 print(Fore.YELLOW + "Migration name cannot be empty. Aborting.")
                 return

        # Use the detected PYTHON_EXECUTABLE for running db_migrate.py
        subprocess.run([PYTHON_EXECUTABLE, "db_migrate.py", "add", migration_name], check=True)
        print(Fore.GREEN + f"New migration '{migration_name}' created successfully.")
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError creating migration: {e}")
        # Stderr might not be captured by check=True, but can be added with capture_output=True if needed
        # print(Fore.RED + f"Stderr: {e.stderr}")
        print(Fore.YELLOW + "Check the output above for specific migration creation errors.")
    except FileNotFoundError:
        print(Fore.RED + f"\nError: {PYTHON_EXECUTABLE} or db_migrate.py not found. Ensure {PYTHON_EXECUTABLE} is in PATH and db_migrate.py exists.")
    except Exception as e:
         print(Fore.RED + f"An unexpected error occurred while creating migration: {e}")

def remove_migrations_folder():
    try:
        print(Fore.GREEN + "Removing Migrations folder if it exists...")
        for root, dirs, files in os.walk(os.path.dirname(os.path.abspath(__file__))):
            for dir in dirs:
                if dir == "Migrations":
                    migrations_folder = os.path.join(root, dir)
                    print(Fore.GREEN + f"Checking if folder exists: {migrations_folder}")
                    print(Fore.GREEN + f"Removing folder: {migrations_folder}")
                    shutil.rmtree(migrations_folder)
                    print(Fore.GREEN + f"Folder removed: {migrations_folder}")
    except OSError as e:
        print(Fore.RED + f"\nError removing migrations folder: {e}")


def remove_docker_container():
    try:
        print(Fore.GREEN + "Removing Docker container 'db' if it exists...")
        subprocess.run(["docker-compose", "stop", "db"], check=True)
        subprocess.run(["docker-compose", "rm", "-f", "db"], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError stopping or removing Docker container: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")
        
def list_docker():
    try:
        print(Fore.GREEN + "Getting list of all containers, images, and volumes...")
        containers = subprocess.run(["docker", "ps", "--format", "{{.Names}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        images = subprocess.run(["docker", "images", "--format", "{{.Repository}}:{{.Tag}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        volumes = subprocess.run(["docker", "volume", "ls", "--format", "{{.Name}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        
        print(Fore.GREEN + "{:>45} {:>45} {:>45}".format("Containers", "Images", "Volumes"))
        print(Fore.GREEN + "-" * 135)
        for i in range(max(len(containers), len(images), len(volumes))):
            container = containers[i] if i < len(containers) else ""
            image = images[i] if i < len(images) else ""
            volume = volumes[i] if i < len(volumes) else ""
            print("{:>45} {:>45} {:>45}".format(container, image, volume))
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError listing containers, images, and volumes: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def check_user_in_db():
    """Checks if any user exists in the 'User' table of the 'foodremedy' database."""
    try:
        print(Fore.CYAN + "Checking for users in the 'User' table...")
        # Check if db container is running first
        db_status = subprocess.run(["docker", "ps", "-q", "-f", "name=db"], capture_output=True, text=True).stdout.strip()
        if not db_status:
            print(Fore.RED + "\nError: Database container 'db' is not running. Cannot check user existence.")
            return False # Cannot check if DB is not running

        # Execute SQL query to count users
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", "SELECT COUNT(*) FROM User;", "foodremedy"],
            capture_output=True, text=True, check=True
        )

        output_lines = result.stdout.strip().splitlines()
        if len(output_lines) < 2:
            print(Fore.YELLOW + "Could not parse user count from database response.")
            print(Fore.YELLOW + f"Raw output: {result.stdout.strip()}")
            return False # Assume no users or unable to verify

        # The second line should contain the count
        try:
            user_count = int(output_lines[1])
            print(Fore.GREEN + f"Found {user_count} user(s) in the 'User' table.")
            return user_count > 0
        except ValueError:
            print(Fore.YELLOW + "Could not parse user count as an integer.")
            print(Fore.YELLOW + f"Raw count line: {output_lines[1]}")
            return False # Unable to verify count

    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError checking user existence in database: {e}")
        # Stderr might not be captured by check=True, but can be added with capture_output=True if needed
        # print(Fore.RED + f"Stderr: {e.stderr}")
        if "Unknown database 'foodremedy'" in str(e): # Check error output string
            print(Fore.YELLOW + "The 'foodremedy' database might not exist yet. Migrations need to run first.")
        elif "Unknown table 'foodremedy.User'" in str(e): # Check error output string
             print(Fore.YELLOW + "The 'User' table might not exist yet. Migrations need to run first.")
        elif "Access denied" in str(e): # Check error output string
            print(Fore.YELLOW + "Database access denied. Check credentials in docker-compose.yml and mysql command.")
        return False # An error occurred, assume no users or inability to verify

    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker or docker-compose command not found. Please ensure Docker is installed and in your system's PATH.")
        return False # Cannot check without docker

    except Exception as e:
        print(Fore.RED + f"An unexpected error occurred during user check: {e}")
        return False # Unknown error

def remove_docker_image():
    try:
        images = subprocess.run(["docker", "images", "--format", "{{.Repository}}:{{.Tag}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        print(Fore.GREEN + "Select the Docker image to remove:")
        for i, image in enumerate(images):
            print(f"{i+1}. {image}")
        while True:
            try:
                index = int(input(Fore.GREEN + "Enter the number of the image to remove: ")) - 1
                if index < 0 or index >= len(images):
                    raise ValueError
                break
            except ValueError:
                print(Fore.RED + "Invalid input. Please enter a valid number.")
        image_name = images[index]
        print(Fore.GREEN + f"Removing Docker image '{image_name}' if it exists...")
        subprocess.run(["docker", "rmi", "-f", image_name], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError removing Docker image: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def remove_docker_volume():
    try:
        volumes = subprocess.run(["docker", "volume", "ls", "--format", "{{.Name}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        if not volumes:
            print(Fore.RED + "No Docker volumes available to remove.")
            return
        print(Fore.GREEN + "Select the Docker volume to remove:")
        for i, volume in enumerate(volumes):
            print(f"{i+1}. {volume}")
        while True:
            try:
                index = int(input(Fore.GREEN + "Enter the number of the volume to remove: ")) - 1
                if index < 0 or index >= len(volumes):
                    raise ValueError
                break
            except ValueError:
                print(Fore.RED + "Invalid input. Please enter a valid number.")
        volume_name = volumes[index]
        print(Fore.GREEN + f"Removing Docker volume '{volume_name}' if it exists...")
        subprocess.run(["docker", "volume", "rm", "-f", volume_name], check=True)
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError removing Docker volume: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")


def automate_fix_previous_builds():
    """
    Automates the process to fix previous builds by cleaning up, rebuilding,
    migrating, populating the DB, and running the application.
    """
    print(Fore.GREEN + "\n--- Starting Automated Build Fix Process ---")

    try:
        # Step 1: Clean up Docker resources related to the project
        print(Fore.CYAN + "\nStep 1: Cleaning up Docker resources...")
        try:
            # This command stops containers, removes them, removes the default network,
            # removes volumes declared in the docker-compose.yml, and removes images
            # used by services in the docker-compose.yml (by default 'local' images).
            subprocess.run(["docker-compose", "down", "--volumes", "--rmi", "local"], check=True)
            print(Fore.GREEN + "Docker resources cleaned up successfully using docker-compose down.")
        except subprocess.CalledProcessError as e:
             print(Fore.YELLOW + f"Warning: Error during docker-compose down cleanup: {e}")
             # Stderr might not be captured by check=True, but can be added with capture_output=True if needed
             # print(Fore.YELLOW + f"Stderr: {e.stderr}")
             print(Fore.YELLOW + "Attempting individual container/volume/image removal...")
             # Fallback: Try to remove specific items if docker-compose down fails or is incomplete
             remove_docker_container("db") # Remove specific db container
             remove_docker_container("redis") # Remove specific redis container if named consistently

             # Attempt to find and remove volumes potentially created by docker-compose
             # This is tricky as volume names can vary, but often include project name.
             try:
                 project_name = os.path.basename(os.getcwd()).lower().replace('-', '') # Infer project name
                 volumes_to_remove = subprocess.run(
                     ["docker", "volume", "ls", "--format", "{{.Name}}"],
                     capture_output=True, text=True, check=True
                 ).stdout.splitlines()
                 for volume in volumes_to_remove:
                      # Example heuristic: remove volumes containing project name or "foodremedy" or "db_data"
                     if project_name in volume or "foodremedy" in volume or "db_data" in volume:
                          print(Fore.CYAN + f"Found potential project volume: {volume}")
                          remove_docker_volume(volume)
             except Exception as vol_e:
                  print(Fore.YELLOW + f"Warning: Error during fallback volume listing/removal: {vol_e}")

             # Removing images used by docker-compose services is complex without docker-compose.
             # Skipping aggressive image removal in fallback to avoid removing unrelated images.


    except FileNotFoundError:
         print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")
         print(Fore.RED + "Automated build fix process aborted.")
         return # Abort if docker-compose is not available

    try:
        # Step 2: Remove Migrations folder
        print(Fore.CYAN + "\nStep 2: Removing Migrations folder...")
        remove_migrations_folder()
        # remove_migrations_folder already prints success/not found

        # Step 3: Run DB container
        print(Fore.CYAN + "\nStep 3: Starting Database container 'db'...")
        run_docker_db_daemon()
        print(Fore.GREEN + "Waiting 15 seconds for the database to become fully ready...")
        time.sleep(15) # Crude wait - assumes DB is up and accepting connections after this time
                      # A robust check would poll the DB connection status.

        # Step 4: Create a new migration based on current model state
        print(Fore.CYAN + "\nStep 4: Creating a new migration...")
        username = "unknown_user"
        try:
            username = getpass.getuser() # Use getpass for better reliability
            print(Fore.CYAN + f"Detected username: {username}")
        except Exception as e:
            print(Fore.YELLOW + f"Could not determine username: {e}. Using '{username}' for migration name.")

        migration_name = f"InitialCreate_{username}" # Use a descriptive name with username

        # Check if db_migrate.py needs the DB running to *add* a migration.
        # Entity Framework Core migrations usually just need the model and connection string config
        # to Scaffold (Add-Migration), but applying (Update-Database) needs the DB.
        # Assuming db_migrate.py add just needs the codebase:
        create_new_migration(migration_name)
        print(Fore.GREEN + f"Migration creation step complete (check output above).")


        # Step 5: Update the database with the new migration
        print(Fore.CYAN + "\nStep 5: Applying database migrations...")
        run_migrations()
        print(Fore.GREEN + "Database migration step complete (check output above).")


        # Step 6: Populate the database with sample data
        print(Fore.CYAN + "\nStep 6: Populating the database with sample data...")
        populate_database()
        print(Fore.GREEN + "Database population step complete (check output above).")


        # Step 7: Check if any user exists in the User table
        print(Fore.CYAN + "\nStep 7: Checking for existing users before starting application...")
        user_exists = check_user_in_db()


        # Step 8: Run the main application if user check passes (or if check failed but didn't indicate DB issue)
        if user_exists: # If user exists OR if db wasn't running to even check
            print(Fore.CYAN + "\nStep 8: Starting the main application...")
            run_dotnet_watch()
            print(Fore.GREEN + "\nMain application stopped.")
        else:
            print(Fore.YELLOW + "\nAutomated process finished. Main application was NOT started because no users were found or check failed.")
            print(Fore.YELLOW + "Please review the database population and migration steps.")


    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nAutomated process stopped due to an error during a critical step: {e}")
        # Stderr might not be captured by check=True, but can be added with capture_output=True if needed
        # print(Fore.RED + f"Stderr: {e.stderr}")
        print(Fore.YELLOW + "Review the output above for the specific error and try fixing it manually.")
    except FileNotFoundError as e:
        print(Fore.RED + f"\nAutomated process stopped because a required command was not found: {e}")
        print(Fore.YELLOW + "Ensure all necessary tools (docker, docker-compose, dotnet, python3) are installed and in your PATH.")
    except Exception as e:
        print(Fore.RED + f"\nAn unexpected error occurred during the automated process: {e}")
        import traceback
        traceback.print_exc() # Print full traceback for unexpected errors

    finally:
        print(Fore.GREEN + "\n--- Automated Build Fix Process Finished ---")

if __name__ == "__main__":
    while True:
        print(Fore.GREEN + "\nConsole Application Menu:")

        print(Fore.YELLOW + "\nApplication Options:")
        print("1. Run .NET Application with dotnet watch (assuming database container 'db' is already running)")
        print("2. Trust Dev Certification to the HTTPS development certificate")

        print(Fore.YELLOW + "\nDocker Options:")
        print("3. Run Docker container 'db' in daemon mode")
        print("4. Stop Docker container 'db'")
        print("5. Check Docker Container Status")
        print("6. Remove Docker container 'db'")
        print("7. List Docker Containers, Images, and Volumes")
        print("15. Remove a Docker image")
        print("16. Remove a Docker volume")

        print(Fore.YELLOW + "\nDatabase Options:")
        print("8. Print tables in the 'foodremedy' database")
        print("9. Print table data in the 'foodremedy' database")
        print("10. Add data to the 'foodremedy' database")
        print("11. Update current tables structure in database")
        print("12. Remove migrations folder")
        print("13. Create new migration")
        print(Fore.YELLOW + "\nAutomation Options:")
        print(Fore.CYAN + "17. Automate Fix Previous Builds (Clean Docker/Migrations, Setup DB, Run App)")

        print(Fore.YELLOW + "\nExit Options:")
        print("14. Exit")

        choice = input(Fore.GREEN + "Enter your choice (1-17): ")
        if choice == "1":
            run_dotnet_watch()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "2":
            trust_dotnet_dev_cert()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "3":
            run_docker_db_daemon()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "4":
            stop_docker_db()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "5":
            check_docker_status()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "6":
            remove_docker_container()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "7":
            list_docker()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "8":
            print_tables_in_database()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "9":
            print_table_data_in_database()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "10":
            populate_database()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "11":
            run_migrations()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "12":
            remove_migrations_folder()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "13":
            create_new_migration()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")
        elif choice == "14":
            print(Fore.GREEN + "Exiting the application.")
            break
        elif choice == "15":
            remove_docker_image()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "16":
            remove_docker_volume()
            print(Fore.GREEN + "\nReturning to the main menu...")
        elif choice == "17":
            automate_fix_previous_builds()
            input(Fore.GREEN + "\nPress Enter to return to the main menu...")

        else:
            print(Fore.RED + "Invalid choice. Please enter a number between 1 and 17.")


