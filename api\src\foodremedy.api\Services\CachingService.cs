using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace foodremedy.api.Services;

public class CachingService : ICachingService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly JsonSerializerOptions _serializerOptions = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };

    public CachingService(IMemoryCache memoryCache, IDistributedCache distributedCache)
    {
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;
    }

    public async Task<T?> Get<T>(string key)
    {
        // Check in-memory cache
        if (_memoryCache.TryGetValue(key, out T? value))
        {
            return value;
        }

        // Check distributed cache (Redis )
        var cachedData = await _distributedCache.GetStringAsync(key);
        if (cachedData is null) return default;

        value = JsonSerializer.Deserialize<T>(cachedData, _serializerOptions);

        // Update memory cache for next call
        _memoryCache.Set(key, value);

        return value;
    }

    public async Task Set<T>(string key, T value, TimeSpan? expiration = null)
    {
        var memoryOptions = new MemoryCacheEntryOptions();

        if (expiration.HasValue)
        {
            memoryOptions.SetAbsoluteExpiration(expiration.Value);
        }

        var serializedData = JsonSerializer.Serialize(value, _serializerOptions);

        // Set in-memory cache
        _memoryCache.Set(key, value, memoryOptions);

        // Set distributed cache
        var distributedOptions = new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expiration ?? TimeSpan.FromMinutes(5)
        };

        await _distributedCache.SetStringAsync(key, serializedData, distributedOptions);
    }

    public async Task Remove(string key)
    {
        _memoryCache.Remove(key);
        await _distributedCache.RemoveAsync(key);
    }
}
