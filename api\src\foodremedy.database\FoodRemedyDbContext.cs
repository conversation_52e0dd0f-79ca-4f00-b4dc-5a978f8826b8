using foodremedy.database.Extensions;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.database;

public sealed class FoodRemedyDbContext : DbContext
{
    public FoodRemedyDbContext(DbContextOptions<FoodRemedyDbContext> options) : base(options) { }

    public DbSet<User> User { get; set; }
    public DbSet<RefreshToken> RefreshToken { get; set; }
    public DbSet<ApiKey> ApiKey { get; set; }
    public DbSet<Tag> Tag { get; set; }
    public DbSet<Food> Food { get; set; }
    public DbSet<TagCategory> TagCategory { get; set; }
    public DbSet<Nutrient> Nutrient { get; set; }
    public DbSet<Profile> Profile { get; set; }
    public DbSet<ConfigModel> ConfigModel { get; set; }

    public DbSet<Allergy> Allergies { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.ConfigureUsers();
        builder.ConfigureRefreshTokens();
        builder.ConfigureApiKeys();
        builder.ConfigureTags();
        builder.ConfigureFoods();
        builder.ConfigureTagCategories();
        builder.ConfigureNutrients();
        builder.ConfigureAllergies();
        builder.ConfigureProfiles();
    }
}
