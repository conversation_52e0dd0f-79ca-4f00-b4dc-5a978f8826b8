﻿namespace foodremedy.database.Models;

public class Food : IDatabaseModel
{
    public Food(string description, IEnumerable<string> name, string foodSeason, int foodEnergyWithFibre, int foodEnergyWithoutFibre, int servingSize ,string nutrients)
    {
        Name = name;
        FoodSeason = foodSeason;
        FoodEnergyWithFibre = foodEnergyWithFibre;
        FoodEnergyWithoutFibre = foodEnergyWithoutFibre;
        Description = description;
        Nutrients = nutrients;
        ServingSize = servingSize;
        Tags = new List<Tag>();
    }

    public Guid Id { get; init; }
    public IEnumerable<string> Name { get; set; }
    public string FoodSeason { get; set; }
    public int FoodEnergyWithFibre { get; set; }    
    public int FoodEnergyWithoutFibre { get; set; }
    public string Description { get; set; }
    public string Nutrients { get; set; }
    public int ServingSize {get;set;}
    public IEnumerable<Tag> Tags { get; set; }
}
