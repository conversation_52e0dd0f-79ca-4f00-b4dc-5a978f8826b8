body {
  font-family: '<PERSON><PERSON>', sans-serif;
  background-color: #f9f9f9;
  margin: 0;
  padding: 20px;
  color: #333;
}

header {
  background-color: #4CAF50;
  color: white;
  padding: 20px;
  text-align: center;
  border-radius: 10px;
}
.api-testing
{
  background-color: #4CAF50;
  color: white;
  padding: 20px;
  text-align: center;
  border-radius: 10px;
}
header h1 {
  margin: 0;
  font-size: 2.5em;
  letter-spacing: 1px;
}

header p {
  margin: 5px 0 0;
  font-size: 1.2em;
}

section {
  margin-top: 20px;
}

h2 {
  color: #4CAF50;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 5px;
  font-size: 1.8em;
}

ul {
  list-style-type: none;
  padding: 0;
  margin-top: 20px;
}

ul.folder-list > li {
  background-color: #4CAF50;
  color: white;
  margin: 10px 0;
  padding: 15px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

ul.folder-list > li:hover {
  background-color: #45A049;
}

ul.folder-list ul {
  margin-left: 20px;
  background-color: #E8F5E9;
  color: #333;
  padding: 10px;
  border-radius: 5px;
}

ul.folder-list ul li {
  margin-bottom: 10px;
  padding-left: 10px;
  position: relative;
}

ul.folder-list ul li:before {
  content: "▶";
  position: absolute;
  left: -15px;
  top: 3px;
  font-size: 1.1em;
  color: #4CAF50;
}

ul.folder-list ul li:hover {
  background-color: #D0E7D2;
}

