﻿using System.Net;
using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Tag = foodremedy.api.Models.Responses.Tag;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("[controller]")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class TagsController : ControllerBase
{
    private readonly ITagRepository _tagRepository;
    private readonly ITagCategoryRepository _tagCategoryRepository;

    public TagsController(ITagRepository tagRepository, ITagCategoryRepository tagCategoryRepository)
    {
        _tagRepository = tagRepository;
        _tagCategoryRepository = tagCategoryRepository;
    }

    /// <summary>
    /// Gets a list af all tags
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <param name="sortBy">The property to sort the result by</param>
    /// <returns> List of tags</returns>
    /// <response code="200">Returns the list of Tags</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedResponse<Tag>>> Get([FromQuery] PaginationRequest paginationRequest, [FromQuery] string? sortBy)
    {
        PaginatedResult<database.Models.Tag> results = await _tagRepository.GetAsync(paginationRequest.Skip, paginationRequest.Take);

        var items = results.Results;

        if (!string.IsNullOrEmpty(sortBy))
        {
            items = sortBy.ToLower() switch
            {
                "name" => items.OrderBy(t => t.Name).ToList(),
                "description" => items.OrderBy(t => t.Description).ToList(),
                "category" => items.OrderBy(t => t.TagCategory.Name).ToList(), // Sorting by category name
                _ => items
            };
        }

        var paginatedResponse = new PaginatedResponse<Tag>(
            results.Total,
            items.Count,
            items.Select(t => t.ToResponseModel())
        );

        return Ok(paginatedResponse);
    }


    /// <summary>
    /// Creates a new tag
    /// </summary>
    /// <param name="createTag">Tag to be added</param>
    /// <param name="tagCategoryId">ID of the new tag</param>
    /// <returns> Successfully created tag</returns>
    /// <response code="201">Tag created</response>
    /// <response code="400">The createTag is null</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Tag category does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost("{tagCategoryId:guid}")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Tag>> CreateTag([FromBody] CreateTag createTag, [FromRoute] Guid tagCategoryId)
    {
        var tagCategory = await _tagCategoryRepository.GetByIdAsync(tagCategoryId);

        if (tagCategory == null)
            return NotFound();

        var result = _tagRepository.Add(createTag.ToDbModel(tagCategory));
        await _tagRepository.SaveChangesAsync();

        return Created($"/tags/{result.ToResponseModel().Id}", result.ToResponseModel());
    }

    /// <summary>
    /// Gets a list of tags by category
    /// </summary>
    /// <param name="tagCategoryName">The name of the category to search for</param>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <param name="sortBy">The property to sort the result by</param>
    /// <returns> List of tags by category</returns>
    /// <response code="200">Returns the list of Tags associated with the category</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Tag category does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{tagCategoryName}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PaginatedResponse<Tag>>> GetTagsByCategory([FromRoute] string tagCategoryName, [FromQuery] PaginationRequest paginationRequest, [FromQuery] string? sortBy)
    {
        var dbCategory = await _tagCategoryRepository.GetByName(tagCategoryName);

        if (dbCategory == null)
            return NotFound();

        // Handle possible null Tags
        var tags = dbCategory.Tags ?? Enumerable.Empty<database.Models.Tag>();

        // Apply sorting based on the 'sortBy' query parameter
        if (!string.IsNullOrEmpty(sortBy))
        {
            tags = sortBy.ToLower() switch
            {
                "name" => tags.OrderBy(t => t.Name).ToList(),
                "description" => tags.OrderBy(t => t.Description).ToList(),
                _ => tags
            };
        }

        // Apply pagination
        var paginatedTags = tags
            .Skip(paginationRequest.Skip)
            .Take(paginationRequest.Take == 0 ? 10 : paginationRequest.Take)
            .Select(t => t.ToResponseModel())
            .ToList();

        var paginatedResponse = new PaginatedResponse<Tag>(
            tags.Count(), // Use Count() to get the number of tags
            paginatedTags.Count,
            paginatedTags
        );

        return Ok(paginatedResponse);
    }

    /// <summary>
    /// Gets a specific tag by its ID.
    /// </summary>
    /// <param name="tagId">The ID of the tag to retrieve.</param>
    /// <returns>The retrieved tag, or a 404 Not Found response if the tag is not found.</returns>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{tagId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Tag>> GetById([FromRoute] Guid tagId)
    {
        database.Models.Tag? dbTag = await _tagRepository.GetByIdAsync(tagId);

        if (dbTag == null)
            return NotFound();

        return Ok(dbTag.ToResponseModel());
    }

    /// <summary>
    /// Updates an existing tag
    /// </summary>
    /// <param name="tagId">ID of the tag</param>
    /// <param name="updateTag">The updated tag details</param>
    /// <returns> Successfully updated Tag</returns>
    /// <response code="201">Tag updated</response>
    /// <response code="400">The createTag is null, the name already exists or no changes have been made</response>
    /// <response code="404">Tag does not exist</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPut("{tagId:guid}")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Tag>> UpdateTag([FromRoute] Guid tagId, [FromBody] CreateTag updateTag)
    {
        database.Models.Tag result;

        database.Models.Tag? dbTag = await _tagRepository.GetByIdAsync(tagId);

        if (dbTag == null) return NotFound();

        database.Models.Tag? existingTag = _tagRepository.GetByNameAsync(updateTag.Name);
        if (existingTag != null && existingTag.Id != dbTag.Id)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = "Tag with this name already exists"
            });
        }

        if (dbTag.Name == updateTag.Name)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.Conflict,
                Detail = "Tag data is already up to date"
            });
        }

        dbTag.Name = updateTag.Name;

        result = _tagRepository.Update(dbTag);
        await _tagRepository.SaveChangesAsync();

        return Created($"/tags/{result.Id}", result);
    }

    /// <summary>
    /// Deletes an existing tag
    /// </summary>
    /// <param name="tagId">ID of the tag to delete</param>
    /// <response code="201">Tag deleted</response>
    /// <response code="404">Tag does not exist</response>  
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpDelete("{tagId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Tag>> DeleteTag([FromRoute] Guid tagId)
    {

        database.Models.Tag? dbTag = await _tagRepository.GetByIdAsync(tagId);

        if (dbTag != null)
        {
            _tagRepository.Remove(dbTag);
            await _tagRepository.SaveChangesAsync();
            return NoContent();
        }

        return NotFound();
    }
}
