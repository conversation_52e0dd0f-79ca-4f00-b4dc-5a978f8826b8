// Member Edit Page tsx

import React, { useCallback, useState } from "react";
import { View, ScrollView, Pressable, Alert } from "react-native";
import { Picker } from '@react-native-picker/picker';
import { useRouter } from "expo-router";
import Header from "@/components/layout/Header";
import Tt from "@/components/ui/UIText";
import Input from "@/components/ui/UIInput";
import IconGeneral from "@/components/icons/IconGeneral";
import { useModalManager } from "@/components/providers/ModalManagerProvider";


const RELATIONS = ["Child", "Partner", "Parent", "Sibling", "Friend", "Other"];
const ALLERGIES = [
  "Peanuts", "Tree Nuts", "Shellfish", "Fish", "Eggs",
  "Milk", "Soy", "Wheat", "Sesame", "Mustard"
];
const INTOLERANCES = ["Lactose", "Gluten", "FODMAPs", "Fructose", "Sulfites",
  "Histamine", "Salicylates", "MSG", "Caffeine", "Artificial Sweeteners"
];
const DIETARIES = [
  "Vegetarian", "Vegan", "Pescatarian", "Keto", "Paleo",
  "Low-Carb", "Low-FODMAP", "Dairy-Free", "Gluten-Free", "Halal", "Kosher", "Organic"
];



type MultiSelectSectionProps = {
  title: string;
  items: string[];
  selected: string[];
  onToggle: (item: string) => void;
};

const MultiSelectSection: React.FC<MultiSelectSectionProps> = ({ title, items, selected, onToggle, }) => (
  <View className="mb-6">
    <Tt className="text-lg font-interMedium text-hsl25 mb-1">{title}</Tt>
    <View className="flex-row flex-wrap justify-start">
      {items.map((item, idx) => {
        const isSelected = selected.includes(item);
        return (
          <Pressable key={idx}
            onPress={() => onToggle(item)}
            className={`px-4 py-1 m-1 border rounded-full ${isSelected ? 'bg-primary border-primary' : 'bg-white border-hsl30'}`}
          >
            <Tt className={`font-interMedium ${isSelected ? 'text-white' : 'text-hsl30'}`}>
              {item}
            </Tt>
          </Pressable>
        );
      })}
    </View>
  </View>
);

export default function MembersEditPage() {
  const router = useRouter();
  const { openModal } = useModalManager();
  const [name, setName] = useState<string>("");
  const [age, setAge] = useState<string>("");
  const [relation, setRelation] = useState<string>("");
  const [selectedAllergies, setAllergies] = useState<string[]>([]);
  const [selectedIntolerances, setIntolerances] = useState<string[]>([]);
  const [selectedDietaries, setDietaries] = useState<string[]>([]);

  /**
   * Toggle Handler using Functional Updates
   */
  const toggle = useCallback((setter: React.Dispatch<React.SetStateAction<string[]>>, item: string) => {
    setter(prev => prev.includes(item) ? prev.filter(i => i !== item) : [...prev, item]);
  }, []);


  /**
   * Handle Save
   * @returns 
   */
  const handleSave = () => {
    if (!name.trim()) {
      Alert.alert("Validation Error", "Please enter the member's name.");
      return;
    }
    if (!age.trim()) {
      Alert.alert("Validation Error", "Please enter the age.");
      return;
    }
    if (isNaN(Number(age))) {
      Alert.alert("Invalid Age", "Age must be a number.");
      return;
    }

    const memberData = {
      name,
      age,
      relation,
      allergies: selectedAllergies,
      intolerances: selectedIntolerances,
    };

    console.log("Saved Member:", memberData);
    router.back(); // Go back to previous page
  };

  /**
   * Handle Delete
   */
  const handleDelete = () => {

  }


  return (
    <View className="flex-1 p-safe">
      <Header />

      <ScrollView>
        <View className="w-[90%] self-center">

          <View className="flex-row items-center justify-between mb-4">
            <Pressable onPress={() => router.back()}
              className="flex-row justify-center items-center self-end px-2 py-1">
              {({ pressed }) => (
                <IconGeneral type="arrow-backward-ios" fill={pressed ? "#FF3F3F" : "hsl(0 0%, 30%)"} />
              )}
            </Pressable>
            <Tt className="font-interBold text-xl">Household Member Profile</Tt>
            <IconGeneral type="arrow-backward-ios" fill="hsl(0 0%, 95%)" />
          </View>


          {/* 
            TODO:
            Update handle image upload 
          */}
          <Pressable onPress={() => { }}
            className="justify-center items-center my-2 mb-4">
            <IconGeneral type="account" fill="hsl(0 0% 40%)" size={100} />
            <Tt className="text-xl font-interMedium text-left">Upload Picture</Tt>
          </Pressable>

          {/* Name Input */}
          <Input
            value={name}
            onChangeText={setName}
            placeholder="Name"
            className="my-2 py-3"
          />

          {/* Age Input */}
          <Input
            value={age}
            onChangeText={setAge}
            placeholder="Age"
            keyboardType="number-pad"
            className="my-2 py-3"
          />

          {/* Relation Modal Open */}
          <Pressable onPress={() => openModal('chooseMemberRelationship')}
            className="flex-row justify-between items-center py-3 px-4 my-2 mb-8 rounded
            border border-hsl70 active:border-primary bg-white">
            {({ pressed }) => (
              <>
                <Tt className={relation.trim().length! ? 'text-hsl20 font-interSemiBold' : 'text-hsl50 font-interSemiBold'}>
                  {relation.trim().length! ? relation : 'Relationship'}
                </Tt>
                <IconGeneral type="arrow-down" fill={pressed ? "#FF3F3F" : "hsl(0, 0%, 30%)"} />
              </>
            )}
          </Pressable>

          {/* Allergies Section */}
          <MultiSelectSection
            title="Select Allergies"
            items={ALLERGIES}
            selected={selectedAllergies}
            onToggle={item => toggle(setAllergies, item)}
          />

          {/* Intolerances Section */}
          <MultiSelectSection
            title="Select Intolerance"
            items={INTOLERANCES}
            selected={selectedIntolerances}
            onToggle={item => toggle(setIntolerances, item)}
          />

          {/* Dietary Form */}
          <MultiSelectSection
            title="Dietary Form"
            items={DIETARIES}
            selected={selectedDietaries}
            onToggle={item => toggle(setDietaries, item)}
          />
        </View>
      </ScrollView>

      {/* BUTTONS */}
      <View className="flex-row justify-around items-center my-4">
        <Pressable
          /**
           * TODO: update handle function
           */
          onPress={() => { }}
          hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          className="py-2 px-6 rounded-lg bg-white active:border-primary"
        >
          {({ pressed }) => (
            <Tt className={`text-lg font-interSemiBold ${pressed ? 'text-primary' : 'text-hsl40'}`}>
              Delete
            </Tt>
          )}
        </Pressable>

        <Pressable
          /**
           * TODO: update handle function
           */
          onPress={() => { }}
          hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          className="py-2 px-6 rounded-lg border bg-primary border-hsl90 active:bg-transparent active:border-primary"
        >
          {({ pressed }) => (
            <Tt className={`text-lg font-interSemiBold ${pressed ? 'text-primary' : 'text-white'}`}>
              Save
            </Tt>
          )}
        </Pressable>
      </View>

    </View>
  );
}
