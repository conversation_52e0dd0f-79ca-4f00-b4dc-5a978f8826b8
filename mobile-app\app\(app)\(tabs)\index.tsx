// Index Page tsx

import IconGeneral from "@/components/icons/IconGeneral";
import Header from "@/components/layout/Header";
import { useProduct } from "@/components/providers/ProductProvider";
import PdBlk from "@/components/ui/UIPaddingBlock";
import Tt from "@/components/ui/UIText";
import { Link, router } from "expo-router";
import { View, Pressable } from "react-native";


/**
 * TODO
 * Delete Temp data
 */
const tempProductData = [
  { id: '**********', name: 'Noodles', brand: 'Big Noodle People' },
  { id: '**********', name: 'Up and Gone', brand: 'Big Milk Man Corp' },
  { id: '**********', name: '<PERSON> and Rice', brand: 'We love rice' },
]

export default function IndexPage() {
  const { setBarcode } = useProduct();

  /**
   * Select Product
   */
  const selectProduct = (productBarcode: string) => {
    setBarcode(productBarcode);
    router.push(`/product`);
  }

  return (
    <View className="flex-1 p-safe">
      <Header />

      <View className="w-[95%] self-center">

        {/* TODO: DELETE Preview link — temporary */}
        <Link href="/createProfile" className="w-full text-center my-4">
          Create Profile Page Link (Preview Only - Delete later)
        </Link>

        {/* TODO: DELETE Preview link — temporary */}
        <Link href="/register" className="w-full text-center my-4">
          Register Page Link (Preview Only - Delete later)
        </Link>

        {/* TODO: DELETE Preview link — temporary */}
        <Link href="/login" className="w-full text-center my-4">
          Login Page Link (Preview Only - Delete later)
        </Link>

        <Tt className="text-xl">
          Hello, User!
        </Tt>


        {/* TODO add next section here */}
        <PdBlk pad={30} />


        {/* Recent Products */}
        {/* Show only the most recent 3 */}
        <View className="flex-row justify-between items-center">
          <Tt className="text-lg font-interBold text-hsl30">Recent Products</Tt>
          <Link href={"/(app)/product"} className="text-sm text-hsl30 font-inter active:text-primary">View All</Link>
        </View>

        {tempProductData.length > 0 && tempProductData.map((product, idx) => (
          <Pressable key={idx}
            onPress={() => selectProduct(product.id)}
            hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
            className="w-full flex-row gap-x-2 items-center py-3 px-4 my-2 rounded-lg border border-hsl90 active:border-primary bg-white">
            {({ pressed }) => (
              <>
                <IconGeneral type="product" fill="hsl(0, 0%, 30%)" size={30} />
                <View className="flex-grow">
                  <Tt className="text-hsl30 font-interSemiBold">{product.name}</Tt>
                  <Tt className="text-hsl30 text-sm">{product.name}</Tt>
                </View>
                <IconGeneral type="arrow-forward-ios" fill={pressed ? "#FF3F3F" : "hsl(0, 0%, 30%)"} size={30} />
              </>
            )}
          </Pressable>
        ))}


        {/* Scan Barcode Button */}
        <Pressable
          onPress={() => router.push("/(app)/barcodeScanner")}
          hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          className="mt-8 w-full flex-row justify-between items-center py-3 px-4 my-4 rounded-lg border border-hsl90 active:border-primary bg-white">
          {({ pressed }) => (
            <>
              <Tt className={`text-lg font-interSemiBold flex-grow  ${pressed ? 'text-primary' : 'text-hsl30'}`}>Scan New Product</Tt>
              <IconGeneral type="barcode-scan" fill={pressed ? "#FF3F3F" : "hsl(0, 0%, 30%)"} size={30} />
            </>
          )}
        </Pressable>

      </View>
    </View>
  );
}