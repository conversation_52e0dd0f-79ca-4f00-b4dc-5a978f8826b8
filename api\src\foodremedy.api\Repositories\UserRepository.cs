﻿using foodremedy.database;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.Repositories;

public interface IUserRepository
{
    Task<User?> GetByEmailAsync(string email);
    Task<User?> GetByUsernameAsync(string username); // New method
    Task<User?> GetByUsernameOrEmailAsync(string identifier); // New method
    User Add(User user);
    void Remove(User user);
    Task SaveChangesAsync();
    Task<User?> GetByIdAsync(string userId);
    Task<PaginatedResult<User>> GetAsync(int skip = 0, int take = 20, string? status = null, string? order = null);
    Task<User?> GetByIdAsync(Guid userId);

    Task<bool> UsernameExistsAsync(string username); // New method

    Task ActivateAsync(Guid userId);
    Task DeactivateAsync(Guid userId);
}

public class UserRepository : IUserRepository
{
    private readonly FoodRemedyDbContext _dbContext;

    public UserRepository(FoodRemedyDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        return await _dbContext.User.SingleOrDefaultAsync(p => p.Email.Equals(email));
    }

    public async Task<User?> GetByUsernameAsync(string username)
    {
        return await _dbContext.User.SingleOrDefaultAsync(p => p.Username.Equals(username));
    }

    public async Task<User?> GetByUsernameOrEmailAsync(string identifier)
    {
        return await _dbContext.User
            .SingleOrDefaultAsync(p => (p.Email.Equals(identifier) || p.Username.Equals(identifier)) && p.Status == true);
    }

    public async Task<bool> UsernameExistsAsync(string username)
    {
        return await _dbContext.User.AnyAsync(u => u.Username.Equals(username));
    }


    public User Add(User user)
    {
        return _dbContext.User.Add(user).Entity;
    }

    public void Remove(User user)
    {
        _dbContext.User.Remove(user);
    }

    public async Task SaveChangesAsync()
    {
        await _dbContext.SaveChangesAsync();
    }

    public async Task<User?> GetByIdAsync(string userId)
    {
        if (!Guid.TryParse(userId, out Guid id))
            return null;

        return await _dbContext.User.SingleOrDefaultAsync(p => p.Id == id);
    }

    public async Task<User?> GetByIdAsync(Guid userId)
    {
        return await _dbContext.User.SingleOrDefaultAsync(p => p.Id == userId);
    }

    public async Task<PaginatedResult<User>> GetAsync(int skip = 0, int take = 20, string? status = null, string? order = null)
    {
        var query = _dbContext.User.AsQueryable();

        if (status == "Active")
        {
            query = query.Where(u => u.Status == true);
        }
        else if (status == "Disabled")
        {
            query = query.Where(u => u.Status == false);
        }

        var result = await query.Skip(skip).Take(take).ToListAsync();

        if (!string.IsNullOrEmpty(order))
        {
            result = order.ToLower() switch
            {
                "email" => result.OrderBy(u => u.Email).ToList(),
                "username" => result.OrderBy(u => u.Username).ToList(),
                _ => result
            };
        }

        return new PaginatedResult<User>(result.Count, _dbContext.User.Count(), result);
    }

    public async Task ActivateAsync(Guid userId)
    {
        var user = await GetByIdAsync(userId)
                ?? throw new KeyNotFoundException($"User {userId} not found");
        if (user.Status)
            throw new InvalidOperationException("Account is already active");

        user.Status = true;
        await SaveChangesAsync();
    }

    public async Task DeactivateAsync(Guid userId)
    {
        var user = await GetByIdAsync(userId)
                ?? throw new KeyNotFoundException($"User {userId} not found");
        if (!user.Status)
            throw new InvalidOperationException("Account is already deactivated");

        user.Status = false;
        await SaveChangesAsync();
    }

}
