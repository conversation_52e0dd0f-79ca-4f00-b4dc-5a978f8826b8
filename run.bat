@echo off
echo Activating virtual environment...
rem Use call to execute the activate script and return
call .venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo Failed to activate virtual environment!
    exit /b %errorlevel%
)
echo Virtual environment activated.

echo Running run_script.py...
rem Run your python script
python run_script.py

rem Optional: deactivate
rem deactivate

echo Script finished.
