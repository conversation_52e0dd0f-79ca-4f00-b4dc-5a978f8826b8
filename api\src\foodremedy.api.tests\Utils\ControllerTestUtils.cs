﻿using System.Net.Http.Json;
using System.ComponentModel;
using foodremedy.api.Models.Responses;
using foodremedy.database;
using Microsoft.Extensions.DependencyInjection;
using foodremedy.api.tests.Factories;


namespace foodremedy.api.tests.Utils;

public static class ControllerTestUtils
{
    //This function turns an object into a dictionary to make it easier to find values
    public static Dictionary<string, object> CreateDictionary(dynamic dyn)
    {
        var dictionary = new Dictionary<string, object>();
        var properties = dyn.GetType().GetProperties();
        foreach (var property in properties)
        {
            object obj = property.GetValue(dyn);
            dictionary.Add(property.Name, obj);
        }
        return dictionary;
    }

    //This function takes an object of options and preforms all the necessary setup before returning the request to be made
    public async static Task<object> Create_Request_with_Options(dynamic options, IFactory factory, FoodRemedyDbContext DbContext){
        
        //Create necessary factories
        IFactory nutrientFactory = new NutrientFactory(DbContext);
        IFactory tagCategoryFactory = new TagCategoryFactory(DbContext);
        IFactory tagFactory = new TagFactory(DbContext);
        
        //Get parameters
        if(options.GetType().GetProperty("Method") ==  null) throw new Exception("Test Method not defined");
        HttpMethod method = options.Method;
        if(options.GetType().GetProperty("Path") ==  null) throw new Exception("Test Path not defined");
        bool hasAddOptions = options.GetType().GetProperty("Add_Before_With_Properties") !=  null;
        bool AddTagCategory = options.GetType().GetProperty("Add_Tag_Category_Before") !=  null;
        bool AddTag = options.GetType().GetProperty("Add_Tag_Before") !=  null;
        bool AddNutrient = options.GetType().GetProperty("Add_Nutrient_Before") !=  null;
        bool hasBody = options.GetType().GetProperty("Has_Body") !=  null ? options.Has_Body : false; 
        bool hasBodyOptions = options.GetType().GetProperty("Request_With_Properties") !=  null;
        bool useRealIdInPath = options.GetType().GetProperty("Use_Real_Id_In_Path") !=  null ? options.Use_Real_Id_In_Path : false; 
        bool useCategoryIdInPath = method == HttpMethod.Post && factory is TagFactory && useRealIdInPath;
        
        //setup other objects. Multiple tests require tag categories, tags or nutrients to be created beforehand so this is where that happens
        //If a tag category needs to be set up before the test then do it now
        database.Models.TagCategory? category = null;
        if(AddTagCategory || (method == HttpMethod.Post && factory is TagFactory && useRealIdInPath)){
            category = await tagCategoryFactory.Add(options.Add_Tag_Category_Before); 
        }
        
        //If a tag needs to be set up before the test then do it now
        if(AddTag){
            await tagFactory.Add(options.Add_Tag_Before); 
        }

        //If a nutrient needs to be set up before the test then do it now
        if(AddNutrient){
            await nutrientFactory.Add(options.Add_Nutrient_Before); 
        }

        //If objects of the type that is being tested needs to be added to the database then do it now. 
        dynamic addOptions = hasAddOptions ? options.Add_Before_With_Properties : new{};
        dynamic dbObject = new{};
        if(hasAddOptions || useRealIdInPath){
            if(addOptions is Array){
                foreach(var obj in addOptions){
                    dbObject = await factory.Add(obj); 
                }
            }else{
                dbObject = await factory.Add(addOptions); 
            }
        }
        

        //Set up the request model for the body if needed
        dynamic bodyOptions = hasBodyOptions ? options.Request_With_Properties : new{};
        if(hasBodyOptions) hasBody = true;
        string path = !useRealIdInPath ? options.Path : !useCategoryIdInPath ? options.Path + '/' + dbObject.Id : options.Path + '/' + category!.Id;

        //Make the request
        var request = new HttpRequestMessage(method, path);
        if(hasBody){
            request = new HttpRequestMessage(method, path)
            {
                Content = JsonContent.Create(factory.Create((dynamic) bodyOptions))
            };
        }
        return request;
    }

    //This function takes in the response and produces the result as an object or array of objects 
    public static async Task<dynamic> GetResult(HttpResponseMessage response, dynamic options, IFactory Factory){

        //It checks whether the result willbe a lone object or a list
        bool hasMultiple = options.GetType().GetProperty("Expect_Result_As_List") !=  null ? options.Expect_Result_As_List : false; 
        
        //It produces the correct result by looking at the factory being used
        if(hasMultiple){
            dynamic? result = Factory switch
                {
                    TagCategoryFactory => await response.Content.ReadFromJsonAsync<PaginatedResponse<Models.Responses.TagCategory>>(),
                    TagFactory _ => await response.Content.ReadFromJsonAsync<PaginatedResponse<Models.Responses.Tag>>(),
                    UserFactory _ => await response.Content.ReadFromJsonAsync<PaginatedResponse<Models.Responses.User>>(),
                    FoodFactory _ => options.Path.Contains("byname") ? await response.Content.ReadFromJsonAsync<IEnumerable<Models.Responses.Food>>() : await response.Content.ReadFromJsonAsync<PaginatedResponse<Models.Responses.FoodSummary>>(),
                    NutrientFactory _ => await response.Content.ReadFromJsonAsync<PaginatedResponse<Models.Responses.Nutrient>>(),
                    _ => null
                };
            return result!;
        }else{
            dynamic? result = Factory switch
                {
                    TagCategoryFactory => await response.Content.ReadFromJsonAsync<Models.Responses.TagCategory>(),
                    TagFactory _ => await response.Content.ReadFromJsonAsync<Models.Responses.Tag>(),
                    UserFactory _ => await response.Content.ReadFromJsonAsync<Models.Responses.User>(),
                    FoodFactory _ => await response.Content.ReadFromJsonAsync<Models.Responses.Food>(),
                    NutrientFactory _ => await response.Content.ReadFromJsonAsync<Models.Responses.Nutrient>(),
                    _ => null
                };
            return result!;
        }
    }
    
    //This function takes in the response of a test and checks that it matches the expected response
    public static async Task<bool> CheckTest(HttpResponseMessage response, dynamic options, IFactory Factory){
    
        //Get the result from the response
        dynamic? result = await GetResult(response, options, Factory);
        bool correct = true;

        //If it needs to check the properties of the result it does so otherwise it just checks that an id exists
        bool checkProperties = options.GetType().GetProperty("Check_Result_Properties") !=  null; 
        if(checkProperties){
            var checks = options.Check_Result_Properties;

            //If there is an array of objects to check then it iterates over them if not it just checks 1
            if(checks is Array){
                for(int i = 0; i < checks.Length; i++){
                    var check = checks[i];
                    //It turns the result into a dictionary to make it easier to check
                    Dictionary<string, object> res = CreateDictionary(result.Results[i]);

                    //it checks that an Id exists on the result
                    correct = correct ? !string.IsNullOrEmpty(res["Id"].ToString()) : correct;
                    if (!correct) throw new Exception($"Id is empty");

                    //It iterates over each of the expected properties
                    foreach (PropertyDescriptor propertyDescriptor in TypeDescriptor.GetProperties(check))
                    {
                        var expected = propertyDescriptor.GetValue(check);
                        dynamic received = res[propertyDescriptor.Name];

                        //If the property is Tags then it needs to check that the corrects tags are contained in the result
                        //if it is nutrients it needs to check that the resulting array contains all the necessary values
                        //otherwise the property must be an ordinary string or integer so it checks that they are equal
                        if(propertyDescriptor.Name == "Tags"){
                            foreach(KeyValuePair<string,IEnumerable<string>> cat in expected){
                                foreach(string tag in cat.Value){
                                    if(!received[cat.Key].Contains(tag)) correct = false;
                                }
                            }
                        }else if(propertyDescriptor.Name == "Nutrients"){
                            foreach(KeyValuePair<string,int> ex in expected){
                                if(!received[ex.Key].Equals(ex.Value)) correct = false;
                            }
                        }else if(propertyDescriptor.Name == "ApiKeys"){
                            foreach(ApiKey ex in expected){
                                if(!received.Contains(ex)) correct = false;
                            }
                        }else{
                            if(!expected.Equals(received)) correct = false;
                        }
                        if (!correct) throw new Exception($"Expected {expected} as {propertyDescriptor.Name} but received {received}");
                    }
                }
            }else{

                //It turns the result into a dictionary to make it easier to check
                Dictionary<string, object> res = CreateDictionary(result);

                //it checks that an Id exists on the result
                correct = !string.IsNullOrEmpty(res["Id"].ToString());
                if (!correct) throw new Exception($"Id is empty");

                //It iterates over each of the expected properties
                foreach (PropertyDescriptor propertyDescriptor in TypeDescriptor.GetProperties(checks))
                {
                    var expected = propertyDescriptor.GetValue(checks);
                    dynamic received = res[propertyDescriptor.Name];

                    //If the property is Tags then it needs to check that the corrects tags are contained in the result
                    //if it is nutrients it needs to check that the resulting array contains all the necessary values
                    //otherwise the property must be an ordinary string or integer so it checks that they are equal
                    if(propertyDescriptor.Name == "Tags"){
                        foreach(KeyValuePair<string,IEnumerable<string>> cat in expected){
                            foreach(string tag in cat.Value){
                                    if(!received[cat.Key].Contains(tag)) correct = false;
                                }
                            }
                        }else if(propertyDescriptor.Name == "Nutrients"){
                            foreach(KeyValuePair<string,int> ex in expected){
                                if(!received[ex.Key].Equals(ex.Value)) correct = false;
                            }
                        }else{
                            if(!expected.Equals(received)) correct = false;
                        }
                        if (!correct) throw new Exception($"Expected {expected} as {propertyDescriptor.Name} but received {received}");
                    }
            }    
        }
        else{
            //It turns the result into a dictionary to make it easier to check
            Dictionary<string, object> res = CreateDictionary(result);

            //it checks that an Id exists on the result
            correct = correct ? !string.IsNullOrEmpty(res["Id"].ToString()) : correct;
            if (!correct) throw new Exception($"Id is empty");
        }

        return correct;
    }
}
