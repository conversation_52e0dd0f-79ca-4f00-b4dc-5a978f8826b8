// Root Layout

import "../global.css";
import { Slot } from "expo-router";
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useEffect } from "react";
import { useFonts } from "expo-font";
import { Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold } from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
// import * as NavigationBar from 'expo-navigation-bar';
import * as SystemUI from 'expo-system-ui';
import Providers from "@/components/providers/Providers";
import { StatusBar } from "expo-status-bar";


export default function RootLayout() {
  const [loaded, error] = useFonts({
    'SpaceMono-Regular': require('@/assets/fonts/SpaceMono-Regular.ttf'),
    'DrukWide-Medium': require('@/assets/fonts/DrukWide-Medium.otf'),
    Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold
  });

  /**
 * Set Default Colors
 */
  useEffect(() => {
    const setDefaultColors = async () => {
      // TODO:Set Navigation Bar
      // Disabled with Edge to Edge. Delete if not updated and uninstall expo-navigation-bar 
      // await NavigationBar.setBackgroundColorAsync('hsl(0, 0%, 95%)');

      // Set Background Color
      // Affects switching between pages
      await SystemUI.setBackgroundColorAsync("hsl(0 0% 95%)");
    };

    setDefaultColors();
  }, []);

  /**
   * Load Fonts
   */
  useEffect(() => {
    if (loaded || error) {
      SplashScreen.hideAsync();
    }
  }, [loaded, error]);

  if (!loaded && !error) return null;

  return (
    <Providers>
      <SafeAreaProvider>

        <Slot />

      </SafeAreaProvider>
      <StatusBar style="dark" hidden={false} translucent={true} />
    </Providers>
  );
}
