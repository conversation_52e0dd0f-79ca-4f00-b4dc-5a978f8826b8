import urllib.request
import gzip
import json

# URL of the  Open Food Facts JSONL dump (gzipped)
URL = "https://static.openfoodfacts.org/data/openfoodfacts-products.jsonl.gz"

# Output file where the filtered Australian products will be saved (in JSONL format)
OUTPUT = "openfoodfacts-australia.jsonl"

# List of fields we want to keep for each product.
# All other fields from the dataset will be discarded to keep the output smaller and more focused.
FIELDS = [
    "id", "code", "brands", "product_name", "generic_name",
    "additives_tags", "allergens", "allergens_tags", "categories_tags",
    "ingredients_tags", "ingredients_text", "ingredients_from_palm_oil_n",
    "ingredients_analysis_tags", "labels_tags", "nutrient_levels", "nutriments",
    "nutriscore_grade", "product_quantity", "product_quantity_unit", "quantity",
    "serving_quantity", "serving_quantity_unit", "serving_size", "traces",
    "traces_from_ingredients", "completeness"
]


def stream_australia_products(url, output_file):
    """
    Stream the Open Food Facts JSONL dump line by line,
    filter products that are sold in Australia (based on 'countries_tags'),
    keep only selected fields, and save them as a JSONL file.
    The function does not load the entire dataset into memory,
    so it can handle millions of products efficiently.
    """
    # Open the remote gzipped file, decompress it on the fly, and prepare the output file
    with urllib.request.urlopen(url) as resp, \
            gzip.GzipFile(fileobj=resp) as gz, \
            open(output_file, 'w', encoding='utf-8') as out:

        count = 0
        # Iterate over each line (each product is one JSON object per line)
        for raw in gz:
            # Decode the line from bytes and parse JSON
            product = json.loads(raw.decode('utf-8'))
            # Get the list of country tags, defaulting to an empty list if missing
            countries = product.get("countries_tags", [])
            # Check if any country tag contains "australia" (matches "en:australia" as well)
            if any("australia" in c for c in countries):
                # Keep only the fields we care about (others are discarded)
                filtered = {field: product.get(field, None)
                            for field in FIELDS}
                # Write the filtered JSON object as a single line (JSONL format)
                out.write(json.dumps(filtered, ensure_ascii=False) + "\n")
                count += 1

        # Print the total number of Australian products processed and saved
        print(f"Saved {count} Australian products to {output_file}")


if __name__ == "__main__":
    stream_australia_products(URL, OUTPUT)
