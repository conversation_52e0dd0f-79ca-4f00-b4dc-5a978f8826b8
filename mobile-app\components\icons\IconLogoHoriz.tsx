// Icon Logo Horizontal

import Svg, { SvgProps, G, <PERSON>, Defs, ClipPath } from "react-native-svg"

const IconLogoHoriz = (props: SvgProps) => (
  <Svg
    width={120}
    height={15}
    fill="none"
    {...props}
  >
    <G clipPath="url(#a)">
      <Path
        fill="#DC232B"
        d="M23.714 1.666a.976.976 0 0 0-1.275-1.47l-5.573 4.18h4.139l2.709-2.71Zm-.151 3.584H10.438a.437.437 0 0 0-.438.438v.875c0 .242.196.437.438.437h.437a5.248 5.248 0 0 0 3.312 4.876 3.508 3.508 0 0 0-.682 1.605.45.45 0 0 0 .44.52h6.11a.45.45 0 0 0 .44-.52 3.503 3.503 0 0 0-.682-1.605A5.248 5.248 0 0 0 23.125 7h.438A.437.437 0 0 0 24 6.563v-.875a.437.437 0 0 0-.438-.438Z"
      />
      <G clipPath="url(#b)">
        <Path
          fill="#8CC341"
          d="M14.933 3.622a1.77 1.77 0 0 0-.42-2.073c-1.11-1.11-2.831-.867-2.831-.867s-.242 1.722.867 2.83a1.769 1.769 0 0 0 2.073.42c.205-.105.205-.105.31-.31Z"
        />
      </G>
      <G clipPath="url(#c)">
        <Path
          fill="#8CC341"
          d="M15.603 3.8a1.246 1.246 0 0 0 1.46-.296c.78-.78.61-1.993.61-1.993s-1.212-.17-1.993.61a1.245 1.245 0 0 0-.296 1.46c.***************.219.22Z"
        />
      </G>
      <G clipPath="url(#d)">
        <Path
          fill="#8CC341"
          d="M15.435 4.615a1.025 1.025 0 0 0 .244 1.203c.643.643 1.641.503 1.641.503s.14-.999-.502-1.642a1.026 1.026 0 0 0-1.203-.244c-.118.062-.118.062-.18.18Z"
        />
      </G>
    </G>
    <Path
      fill="#000"
      d="M.761 13.568c.4-.112.688-.216.864-.312a.853.853 0 0 0 .36-.432c.064-.176.096-.448.096-.816V5.936c0-.64-.048-1.144-.144-1.512a3.248 3.248 0 0 0-.552-1.104l-.192-.264.192-.264h5.04c.88 0 1.472-.104 1.776-.312l.288-.216.24.216v3l-.48.168c-.288-.864-.576-1.456-.864-1.776-.272-.32-.672-.48-1.2-.48h-.792c-.416 0-.688.08-.816.24-.128.144-.192.464-.192.96v3.6H6.09c.624 0 1.032-.24 1.224-.72l.12-.336.312.168v2.688l-.528.192-.096-.504c-.064-.352-.208-.584-.432-.696-.224-.128-.648-.192-1.272-.192H4.385v2.064c0 .624.048 1.128.144 1.512.112.368.304.736.576 1.104l.192.288-.192.24H.93l-.168-.432Zm30.48.648c-1.665 0-2.985-.52-3.96-1.56-.976-1.04-1.464-2.456-1.464-4.248 0-1.792.488-3.208 1.463-4.248.976-1.056 2.296-1.584 3.96-1.584 1.665 0 2.985.528 3.96 1.584.992 1.04 1.488 2.456 1.488 4.248 0 1.776-.495 3.192-1.488 4.248-.976 1.04-2.296 1.56-3.96 1.56Zm-.072-.552c.591 0 1.112-.216 1.56-.648.464-.448.824-1.064 1.08-1.848.256-.784.384-1.688.384-2.712 0-1.024-.128-1.944-.384-2.76-.24-.816-.584-1.448-1.032-1.896-.433-.464-.928-.696-1.489-.696-.591 0-1.12.232-1.584.696-.463.448-.823 1.088-1.08 1.92-.255.816-.383 1.752-.383 2.808 0 1.552.264 2.8.791 3.744.529.928 1.24 1.392 2.137 1.392Zm6.627-.096c.4-.112.688-.216.864-.312a.853.853 0 0 0 .36-.432c.064-.176.096-.448.096-.816V5.504c0-.56-.048-.968-.144-1.224-.08-.256-.28-.48-.6-.672l-.288-.168.096-.312c.528-.16 1.184-.288 1.968-.384a15.364 15.364 0 0 1 2.16-.168c2.032 0 3.576.504 4.632 1.512 1.072.992 1.608 2.432 1.608 4.32 0 1.712-.496 3.072-1.488 4.08C46.084 13.496 44.748 14 43.052 14h-5.088l-.168-.432Zm5.04-.048c2.16 0 3.24-1.704 3.24-5.112 0-1.568-.312-2.824-.936-3.768-.608-.96-1.416-1.44-2.424-1.44-.416 0-.736.064-.96.192-.224.128-.336.312-.336.552v7.344c0 .752.112 1.312.336 1.68.24.368.6.552 1.08.552Zm21.386-.336c-.576.688-1.328 1.032-2.256 1.032-.576 0-1.032-.128-1.368-.384-.32-.272-.6-.728-.84-1.368l-.696-2.112c-.16-.432-.304-.744-.432-.936-.128-.208-.328-.368-.6-.48l-.408-.216.048-.24c.128.016.296.024.504.024.192 0 .328-.008.408-.024.592-.096 1.056-.368 1.392-.816.336-.448.504-1.024.504-1.728 0-.848-.2-1.52-.6-2.016-.4-.512-.928-.768-1.584-.768-.72 0-1.08.336-1.08 1.008v6.696c0 .624.048 1.128.144 1.512.112.368.304.736.576 1.104l.192.288-.192.24h-4.176l-.168-.432c.4-.112.688-.216.864-.312a.853.853 0 0 0 .36-.432c.064-.176.096-.448.096-.816V5.624c0-.56-.048-.984-.144-1.272-.08-.304-.28-.552-.6-.744l-.288-.168.096-.312c.496-.16 1.128-.288 1.896-.384.784-.112 1.48-.168 2.088-.168 1.52 0 2.72.304 3.6.912.88.608 1.32 1.44 1.32 2.496a2.99 2.99 0 0 1-.72 1.968 3.382 3.382 0 0 1-1.8 1.128c.576.256.976.688 1.2 1.296l.84 2.352c.128.384.376.576.744.576.288 0 .584-.128.888-.384l.192.264Zm1.589-7.248c0-.64-.048-1.144-.144-1.512a3.248 3.248 0 0 0-.552-1.104l-.192-.264.192-.264h5.304c.88 0 1.472-.104 1.776-.312l.288-.216.24.216v3l-.48.168c-.288-.864-.576-1.456-.864-1.776-.272-.32-.664-.48-1.176-.48h-1.08c-.416 0-.688.08-.816.24-.128.144-.192.464-.192.96V8h1.728c.48 0 .832-.048 1.056-.144.24-.096.416-.288.528-.576l.12-.336.312.192v2.688l-.528.192-.096-.528c-.064-.336-.24-.568-.528-.696-.288-.128-.752-.192-1.392-.192h-1.2v3.624c0 .48.072.8.216.96.144.144.464.216.96.216h1.176c.48 0 .856-.168 1.128-.504.288-.336.56-.92.816-1.752l.504.168v2.88l-.264.216a2.083 2.083 0 0 0-.768-.336c-.288-.048-.688-.072-1.2-.072h-6.024l-.168-.432c.4-.112.688-.216.864-.312a.853.853 0 0 0 .36-.432c.064-.176.096-.448.096-.816V5.936Zm20.834 5.04c.032.64.128 1.176.288 1.608.16.432.416.824.768 1.176l-.192.24h-4.176l-.168-.432c.528-.144.864-.288 1.008-.432.16-.144.24-.408.24-.792 0-.208-.128-2.152-.384-5.832l-.144-2.088-3.24 9.216h-.672l-3.216-9.144-.336 5.472a22.75 22.75 0 0 0-.096 1.608c0 .416.048.784.144 1.104.112.304.312.664.6 1.08l-.216.24h-2.664l-.144-.432c.416-.128.712-.24.888-.336a.734.734 0 0 0 .384-.408c.08-.176.136-.448.168-.816l.288-4.608c.08-1.072.12-1.776.12-2.112 0-.544-.064-.944-.192-1.2-.112-.256-.376-.584-.792-.984l.168-.312h3.504l2.472 7.464 2.616-7.464h3.096l.24.264c-.304.448-.512.816-.624 1.104-.096.288-.144.664-.144 1.128 0 .304.032.776.096 1.416l.312 4.272Zm3.47-5.04c0-.64-.048-1.144-.144-1.512a3.248 3.248 0 0 0-.552-1.104l-.192-.264.192-.264h5.304c.88 0 1.472-.104 1.776-.312l.288-.216.24.216v3l-.48.168c-.288-.864-.576-1.456-.864-1.776-.272-.32-.664-.48-1.176-.48h-1.08c-.416 0-.688.08-.816.24-.128.144-.192.464-.192.96V8h1.728c.48 0 .832-.048 1.056-.144.24-.096.416-.288.528-.576l.12-.336.312.192v2.688l-.528.192-.096-.528c-.064-.336-.24-.568-.528-.696-.288-.128-.752-.192-1.392-.192h-1.2v3.624c0 .48.072.8.216.96.144.144.464.216.96.216h1.176c.48 0 .856-.168 1.128-.504.288-.336.56-.92.816-1.752l.504.168v2.88l-.264.216a2.083 2.083 0 0 0-.768-.336c-.288-.048-.688-.072-1.2-.072h-6.024l-.168-.432c.4-.112.688-.216.864-.312a.853.853 0 0 0 .36-.432c.064-.176.096-.448.096-.816V5.936Zm8.618 7.632c.4-.112.688-.216.864-.312a.853.853 0 0 0 .36-.432c.064-.176.096-.448.096-.816V5.504c0-.56-.048-.968-.144-1.224-.08-.256-.28-.48-.6-.672l-.288-.168.096-.312c.528-.16 1.184-.288 1.968-.384a15.364 15.364 0 0 1 2.16-.168c2.032 0 3.576.504 4.632 1.512 1.072.992 1.608 2.432 1.608 4.32 0 1.712-.496 3.072-1.488 4.08-.976 1.008-2.312 1.512-4.008 1.512h-5.088l-.168-.432Zm5.04-.048c2.16 0 3.24-1.704 3.24-5.112 0-1.568-.312-2.824-.936-3.768-.608-.96-1.416-1.44-2.424-1.44-.416 0-.736.064-.96.192-.224.128-.336.312-.336.552v7.344c0 .752.112 1.312.336 1.68.24.368.6.552 1.08.552Zm15.699-10.464-.24.264c-.432.528-.8 1.08-1.104 1.656l-2.184 4.176v2.856c0 .368.032.648.*************.208.312.384.408.176.096.456.2.84.312l-.144.432h-4.2l-.192-.24.192-.288c.272-.352.456-.712.552-1.08.112-.384.168-.896.168-1.536V9.2l-2.28-4.176c-.256-.512-.496-.904-.72-1.176a3.992 3.992 0 0 0-.84-.792l.216-.264h4.392l.12.432-.36.096c-.464.096-.696.28-.696.552 0 .*************.408.08.176.2.416.36.72l1.632 3.072 1.728-3.288c.128-.208.192-.416.192-.624a.766.766 0 0 0-.192-.528.872.872 0 0 0-.48-.312l-.456-.096.144-.432h2.76l.192.264Z"
    />
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M10 0h14v14H10z" />
      </ClipPath>
      <ClipPath id="b">
        <Path fill="#fff" d="M11 5.279v-5.28h5.279v5.28z" />
      </ClipPath>
      <ClipPath id="c">
        <Path fill="#fff" d="M14.436 1.031h3.717v3.717h-3.717z" />
      </ClipPath>
      <ClipPath id="d">
        <Path fill="#fff" d="M17.716 3.655v3.06h-3.061v-3.06z" />
      </ClipPath>
    </Defs>
  </Svg>
)

export default IconLogoHoriz;