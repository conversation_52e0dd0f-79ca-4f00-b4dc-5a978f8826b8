using foodremedy.database;
using ConfigModel = foodremedy.database.Models.ConfigModel;
using Microsoft.EntityFrameworkCore;




namespace foodremedy.api.Repositories
{
    public interface IConfigurationRepository
    {
        Task<IEnumerable<ConfigModel>> GetAllAsync();
        Task<ConfigModel?> GetByKeyAsync(string key);
        Task AddAsync(ConfigModel config);
        Task DeleteAsync(ConfigModel config);
        Task UpdateAsync(ConfigModel config);
        Task SaveChangesAsync();
    }


    public class ConfigurationRepository : IConfigurationRepository
    {
        private readonly FoodRemedyDbContext _dbContext;

        public ConfigurationRepository(FoodRemedyDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<IEnumerable<ConfigModel>> GetAllAsync()
        {
            return await _dbContext.ConfigModel.ToListAsync();
        }

        public async Task<ConfigModel?> GetByKeyAsync(string key)
        {
            return await _dbContext.ConfigModel.FirstOrDefaultAsync(c => c.Key == key);
        }

        public async Task AddAsync(ConfigModel config)
        {
            await _dbContext.ConfigModel.AddAsync(config);
        }

        public async Task UpdateAsync(ConfigModel config)
        {
            _dbContext.ConfigModel.Update(config);
        }

        public async Task SaveChangesAsync()
        {
            await _dbContext.SaveChangesAsync();
        }

        public async Task DeleteAsync(ConfigModel config)
        {
            _dbContext.ConfigModel.Remove(config);
        }
    }
}