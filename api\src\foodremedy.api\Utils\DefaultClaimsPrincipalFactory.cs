﻿using System.Security.Claims;

namespace foodremedy.api.Utils;

/// <summary>
/// Service used to generate <see cref="ClaimsPrincipal"/>s.
/// </summary>
public interface IClaimsPrincipalFactory
{
    /// <summary>
    /// Creates a claims principal to use for authenticating a request.
    /// </summary>
    /// <param name="apiKeyOwnerId">The ID of the owner of the API Key.</param>
    /// <returns>A <see cref="ClaimsPrincipal"/> that represents the entity that initiated the HTTP request.</returns>
    Task<ClaimsPrincipal> CreateClaimsPrincipal(string apiKeyOwnerId);
}

/// <summary>
/// Default implementation for the <see cref="IClaimsPrincipalFactory"/> service.
/// Creates a principal with the single claim of the owner ID.
/// </summary>
internal sealed class DefaultClaimsPrincipalFactory : IClaimsPrincipalFactory
{
    public Task<ClaimsPrincipal> CreateClaimsPrincipal(string apiKeyOwnerId)
    {
        var claims = new[] { new Claim(ClaimTypes.Name, apiKeyOwnerId) };
        var identity = new ClaimsIdentity(claims, ApiKeyAuthenticationOptions.DefaultScheme);
        var identities = new List<ClaimsIdentity> { identity };
        var principal = new ClaimsPrincipal(identities);

        return Task.FromResult(principal);
    }
}