using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Responses;
using foodremedy.api.Models.Requests;
using foodremedy.api.tests.Factories;

namespace foodremedy.api.tests.Controllers;

/// <summary>
/// Contains unit tests for the NutrientsController endpoints. 
/// Categorizes test cases based on authentication level, expected response type, and request validity.
/// </summary>
internal class NutrientsControllerTests : ControllerTestFixture
{
    /// <summary>
    /// Gets the factory used to generate nutrient test data.
    /// </summary>
    public override IFactory Factory => nutrientFactory;

    /// <summary>
    /// Test cases for unauthenticated requests to nutrient endpoints that should return 401 Unauthorized.
    /// </summary>
    public new static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "nutrients", 
            Method= HttpMethod.Get
            }).SetName("GetNutrients_UnauthenticatedRequest_ReturnsUnauthorized"),
        
        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetNutrient_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "nutrients/byname/name",
            Method= HttpMethod.Get
            }).SetName("GetNutrientByName_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteNutrient_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateNutrient_UnauthenticatedRequest_RespondsUnauthorized"),

        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateNutrient_UnauthenticatedRequest_ReturnsUnauthorized")
    };

    /// <summary>
    /// Test cases where the request is authenticated with an API key but lacks sufficient privileges, expecting 401 Unauthorized.
    /// </summary>
    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteNutrient_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateNutrient_AuthenticatedByApiKey_RespondsUnauthorized"),

        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateNutrient_AuthenticatedByApiKey_ReturnsUnauthorized")
    };

    /// <summary>
    /// Test cases where a nutrient is not found, expecting 404 Not Found.
    /// </summary>
    public new static List<TestCaseData> NotFoundTests = new List<TestCaseData>
    {   
        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetNutrient_NutrientDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  "nutrients/byname/name",
            Method= HttpMethod.Get
            }).SetName("GetNutrientByName_NutrientDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Delete
            }).SetName("DeleteNutrient_NutrientDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"nutrients/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateNutrient_NutrientDoesNotExist_ReturnsNotFound")
    };

    /// <summary>
    /// Test cases for valid delete requests that return 204 No Content.
    /// </summary>
    public new static List<TestCaseData> NoContentTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "nutrients", 
            Use_Real_Id_In_Path = true,
            Method= HttpMethod.Delete
            }).SetName("DeleteNutrient_ValidRequest_ReturnsNoContent"),
    };

    /// <summary>
    /// Test cases for invalid requests that result in 400 Bad Request, such as trying to update with no actual change.
    /// </summary>
    public new static List<TestCaseData> BadRequestTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "nutrients",
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = "Existing Name"
                },
            Request_With_Properties = new{
                    Name = "Existing Name"
                },
            Method= HttpMethod.Put
            }).SetName("UpdateNutrient_NoChange_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "nutrients",
            Use_Real_Id_In_Path = true,
            Add_Nutrient_Before =  new{
                    Name = "Nutrient 1"
                },
            Add_Before_With_Properties = new{
                    Name = "Nutrient 2"
                },
            Request_With_Properties = new{
                    Name = "Nutrient 1"
                },
            Method= HttpMethod.Put
            }).SetName("UpdateNutrient_ExistingName_ReturnsBadRequest"),
    };

    /// <summary>
    /// Test cases where the request attempts to create a duplicate nutrient, expecting 409 Conflict.
    /// </summary>
    public new static List<TestCaseData> ConflictTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "nutrients",
            Add_Before_With_Properties = new{
                    Name = "Existing Name"
                },
            Request_With_Properties = new{
                    Name = "Existing Name"
                },
            Method= HttpMethod.Post
            }).SetName("CreateNutrient_DuplicateName_ReturnsConflict"),
    };
    
    /// <summary>
    /// Test cases for valid requests that result in 201 Created, such as creating or updating nutrients successfully.
    /// </summary>
    public new static List<TestCaseData> CreatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Post,
            Request_With_Properties = new{
                    Name = "Name",
                    Description = "Description"
                },
            Check_Id_Exists = true,
            Check_Result_Properties = new{
                    Name = "Name",
                    Description = "Description"
                }
            }).SetName("CreateNutrient_ValidRequest_ReturnsCreated"),

        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Put,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = "Existing Name",
                    Description = "Description"
                },
            Request_With_Properties = new{
                    Name = "Changed Name",
                    Description = "Description"
                },
            Check_Id_Exists = true,
            Check_Result_Properties = new{
                    Name = "Changed Name",
                    Description = "Description"
                }
            }).SetName("UpdateNutrient_DifferentName_ReturnsCreated"),

        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Put,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = "Name",
                    Description = "Existing Description"
                },
            Request_With_Properties = new{
                    Name = "Name",
                    Description = "Changed Description"
                },
            Check_Id_Exists = true,
            Check_Result_Properties = new{
                    Name = "Name",
                    Description = "Changed Description"
                }
            }).SetName("UpdateNutrient_DifferentDescription_ReturnsCreated"),
    };

    /// <summary>
    /// Test cases for valid GET requests that return 200 OK with expected nutrient data.
    /// </summary>
    public new static List<TestCaseData> OkTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new{
                    Name = "name1",
                    },
                new{
                    Name = "name2",
                    },
                },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new{
                    Name = "name1",
                    },
                new{
                    Name = "name2",
                    },
                }
            }).SetName("GetNutrients_ValidRequest_ReturnsOk"),
        
        new TestCaseData(new {
            Path=  "nutrients",
            Method= HttpMethod.Get,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Name = "name1",
                },
            Check_Result_Properties = new{
                    Name = "name1",
                },
            }).SetName("GetNutrient_ById_ReturnsOk"),
        
        new TestCaseData(new {
            Path=  "nutrients/byname/name1",
            Method= HttpMethod.Get,
            Add_Before_With_Properties = new{
                    Name = "name1",
                },
            Check_Result_Properties = new{
                    Name = "name1",
                },
            }).SetName("GetNutrient_ByName_ReturnsOk"),
    };
}
