// Barcode Scanner Page tsx

import IconCameraPermission from "@/components/icons/IconCameraPermission";
import IconGeneral from "@/components/icons/IconGeneral";
import Header from "@/components/layout/Header";
import PdBlk from "@/components/ui/UIPaddingBlock";
import Tt from "@/components/ui/UIText";
import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import { router } from "expo-router";
import { useState } from 'react';
import { Pressable, View } from 'react-native';
import LoadingPage from "../(misc)/loading";
import BarcodeOverlayMask from "@/components/layout/BarcodeOverlayGuide";
import { useProduct } from "@/components/providers/ProductProvider";

export default function BarcodeScanner() {
  const { setBarcode } = useProduct();
  const [scanned, setScanned] = useState(false);
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();

  // Camera permissions are still loading.
  if (!permission) {
    return <LoadingPage />;
  }

  // Camera permissions are not granted yet.
  if (!permission.granted) {
    return (
      <View className="flex-1 p-safe bg-cyan-100/50">
        <View className="items-center justify-center flex-1 w-[90%] self-center">
          <IconCameraPermission width={150} height={150} />

          <View className="w-full mt-8">
            <Tt className="text-2xl text-center font-drukWide">ALLOW CAMERA</Tt>
            <Tt className="my-2 mb-4 text-sm text-center text-hsl20">Allow Food Remedy to use the camera to scan barcodes</Tt>

            <Pressable
              onPress={requestPermission}
              hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
              className="px-4 py-3 my-4 border rounded-md bg-primary border-primary active:bg-primary/80">
              <Tt className="text-lg text-center text-white">Allow</Tt>
            </Pressable>

            <Pressable
              onPress={() => router.replace("/(app)/(tabs)/scan")}
              hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
              className="w-full px-4 py-3 my-4 border rounded-md border-hsl10 active:bg-hsl10">
              {({ pressed }) => (
                <Tt className={`text-lg text-center text-black  ${pressed && 'text-white'}`}>Don't Allow</Tt>
              )}
            </Pressable>
          </View>
        </View>
      </View>
    );
  }

  /**
   * Handle Barcode Scanned
   * @param param 
   */
  const handleBarCodeScanned = ({ type, data }: { type: string, data: string }) => {
    // TODO: Clean up
    setScanned(true);
    console.log(`Barcode with type ${type} and data ${data} has been scanned!`);
    alert(`Barcode Scanned. Type: ${type} | Barcode: ${data}`);
    setBarcode(data);
    router.replace("/(app)/(tabs)/scan");
    setScanned(false);
  };

  /**
   * Toggle Camera Facing
   */
  function toggleCameraFacing() {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  }

  return (
    <View className="flex-1 p-safe">
      <Header />
      <PdBlk pad={5} />

      {/* CAMERA WRAPPER */}
      <View className="relative flex-1 ">
        <CameraView
          style={{ flex: 1 }}
          facing={facing}
          onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
        />

        <BarcodeOverlayMask />
      </View>

      {/* BOTTOM TAB */}
      <View className="flex flex-row items-center justify-between px-4 mt-4">
        <Pressable onPress={() => router.back()}
          className="flex-row items-center flex-1 gap-x-1"
        >
          {({ pressed }) => (
            <IconGeneral type="arrow-backward-ios" fill={pressed ? '#FF3F3F' : 'hsl(0, 0%, 30%)'} size={28} />
          )}
        </Pressable>

        <Pressable onPress={toggleCameraFacing}
          className="flex flex-col items-center gap-y-1"
        >
          {({ pressed }) => (
            <>
              <IconGeneral type="camera-switch" fill={pressed ? '#FF3F3F' : 'hsl(0, 0%, 30%)'} />
              <Tt className="text-xs" style={{ color: pressed ? '#FF3F3F' : 'hsl(0, 0%, 30%)' }}>Toggle Camera</Tt>
            </>
          )}
        </Pressable>
      </View>

    </View >
  );
}