using foodremedy.api.Models.Requests;
using foodremedy.database;
using Nutrient = foodremedy.database.Models.Nutrient;

namespace foodremedy.api.tests.Factories;

public class NutrientFactory : IFactory
{
    const string DefaultName = "DefaultName" ;
    const string DefaultDescription = "DefaultDescription";
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public NutrientFactory(FoodRemedyDbContext db){
        Db = db;
    }

    public async Task<IDatabaseModel> Add(dynamic? args = null){
        num++;
        args = args ?? new {Name = DefaultName + num, Description = DefaultDescription};
        Nutrient nutrient = Db.Nutrient.Add(new Nutrient(
            args.GetType().GetProperty("Name") !=  null ? args.Name : DefaultName + num, 
            args.GetType().GetProperty("Description") !=  null ? args.Description : DefaultDescription
            )).Entity;
        await Db.SaveChangesAsync();
        
        return nutrient;
    }

    public IRequestModel Create(dynamic? args = null){
        num++;
        args = args ?? new {Name = DefaultName + num, Description = DefaultDescription};
        return new CreateNutrient(
            args.GetType().GetProperty("Name") !=  null ? args.Name : DefaultName + num, 
            args.GetType().GetProperty("Description") !=  null ? args.Description : DefaultDescription
            );
    }
}