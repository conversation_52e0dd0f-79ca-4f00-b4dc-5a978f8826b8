-------------- <incomplete> --------------

# Running external database scripts

1. Create a new Microsoft SQL Server on your local machine.
2. Execute queries to create two primary databases, one for the food database, and another one for the user management database. You can use "CREATE DATABASE <name_of_database>" 
3. Execute queries to create all of the corresponding tables in the two databases, ensure the correct database is selected and then execute the SQL code located in each of the .sql files. 
4. Modify the config file 'databaseCredentials.ini' to match your newely created SQL Server. 
5. Run 'encryptCredentials.py' to encrpt the the file and generate/store the secret key.
6. Modify and Run 'Import Data.py' with the correct .csv source files and target columns. 
7. <>
