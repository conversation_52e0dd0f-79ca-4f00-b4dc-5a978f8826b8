using Aspire.Hosting;
using Microsoft.Extensions.Configuration;
using MySqlConnector;
using Microsoft.Extensions.DependencyInjection;

var builder = DistributedApplication.CreateBuilder(args);


// // The MySQL setup
// var mysql = builder.AddMySql("mysql")
//     .WithLifetime(ContainerLifetime.Persistent);

// var mysqldb = mysql.AddDatabase("foodremedy");

var backend = builder.AddProject<Projects.foodremedy_api>("Backend");

builder.Build().Run();
