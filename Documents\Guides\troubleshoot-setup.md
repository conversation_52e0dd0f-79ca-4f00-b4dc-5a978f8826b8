
# 🧯Troubleshoot Set Up
Run into issues? Here are common problems and quick fixes to get you back on track.


<br/>


## 🐳 Build Failing (Database or Docker)
Why this happens:
- Wrong version of .NET or Entity Framework
- Port `3306` (MySQL) is already in use by another process

Typical error:
  ```bash
  Error response from daemon: Ports are not available: exposing port TCP 0.0.0.0:3306 -> 0.0.0.0:0: listen tcp 0.0.0.0:3306: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
  ```
How to fix:
1. Open Task Manager `(Ctrl + Shift + Esc)`
2. Look for MySQL or other processes using port `3306`
3. End those processes
4. Re-run the CLI and choose:
   ```bash
   3. Run Docker container 'db' in daemon mode
   ```


<br/>  


## 🔒 Exclusive Lock Error
When you might see: 

```bash
cannot take exclusive lock for project "foodremedy-main": process with PID 12056 is still running
```

Why this happens:
- A Docker session closed unexpectedly (for example, closing VSCode or terminal while running the app)

How to fix:
1. Press Windows + R, type: `%localappdata%`  
2. Open the folder named `docker-compose`  
3. Delete the file called: `foodremedy-main.pid`  
4. Re-run the CLI.


<br/> 


## ⏳ .NET Watch Stuck on Build
When this happens:  
- If you select option 1 and it just sits on:  
   ```bash 
   dotnet watch ⌚ Building foodremedy-main\api\AppHost\AppHost.csproj …
   ```

How to fix:
1. Press `Ctrl + C` to cancel the process
2. Re-run the CLI and choose:
     ```bash
     1. Run .NET Application with dotnet watch
     ```
