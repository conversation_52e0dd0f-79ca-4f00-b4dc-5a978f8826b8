export MYSQL_PWD=password
RED='\033[0;31m'
PURPLE='\033[0;35m'
END='\n\033[0m'

run_mysql_file () {
  printf "${PURPLE}\nRunning database/code/$1${END}"
  if ! mysql -umysqluser -h mysql-headless foodremedy < $1; then
    printf "${RED} ERROR: Failed Running database/code/$1${END}"
    exit 3
  fi
}

cd database/code;

for ((n=10;n>0;n--)) ; do
  mysql -umysqluser -h mysql-headless foodremedy -e 'SELECT "** Connected to database" as ""' && break
  sleep 2
done

if [ $n -eq 0 ]; then
  echo "Error: Failed to connect to the database after 10 attempts"
  exit 1
fi

run_mysql_file db_tables.sql

run_mysql_file db_sampledata.sql

echo
