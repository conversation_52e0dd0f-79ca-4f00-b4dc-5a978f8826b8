import os

main_dir = os.getcwd()

try:
    # Start the database container
    os.system("docker-compose up -d db")

    # Drop and recreate the database
    os.chdir(os.path.join(main_dir, "api", "src"))
    # os.chdir("C:\\Users\\<USER>\\Desktop\\Capstone\\foodremedy-main\\api\\src")
    os.system("dotnet ef database drop --project foodremedy.database --startup-project foodremedy.api --force")
    os.system("dotnet ef database update --project foodremedy.database --startup-project foodremedy.api")

    # Setup the database schema and mock data
    os.chdir(main_dir)
    result = os.system("docker-compose exec -T db bash database/code/setup_schema.sh")

    if result == 3:
        raise RuntimeError("A MySQL script failed to run in the database\n")
    elif result > 0:
        raise RuntimeError("Docker failed to setup the database. Error code: " + str(result))

except RuntimeError as err:
    raise

finally:
    os.system("docker-compose stop db")