from sqlalchemy import Column, Text
from sqlalchemy.dialects.postgresql import ARRAY, JSONB
from backend.database import Base


class Food(Base):
    __tablename__ = "food"

    code = Column(Text,  primary_key=True, index=True)
    brands = Column(Text)
    product_name = Column(Text)
    additives_tags = Column(ARRAY(Text))
    allergens_tags = Column(ARRAY(Text))
    generic_name = Column(Text)
    ingredients_text = Column(Text)
    nutriments = Column(JSONB)     # raw JSON blob
    quantity = Column(Text)
    serving_size = Column(Text)
