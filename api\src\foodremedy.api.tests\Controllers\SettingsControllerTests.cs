using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Responses;
using foodremedy.database.Models;
using foodremedy.api.Models.Requests;
using foodremedy.api.tests.Factories;

namespace foodremedy.api.tests.Controllers;

internal sealed class SettingsControllerTests : ControllerTestFixture
{
    public override IFactory Factory => userFactory;

    public new static List<TestCaseData> UnauthenticatedTests = new()
    {
        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Get
        }).SetName("GetAllSettings_Unauthenticated_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Post,
            Has_Body = true
        }).SetName("AddSetting_Unauthenticated_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path = $"settings/{Guid.NewGuid()}",
            Method = HttpMethod.Delete
        }).SetName("DeleteSetting_Unauthenticated_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path = $"settings/{Guid.NewGuid()}",
            Method = HttpMethod.Put,
            Has_Body = true
        }).SetName("UpdateSetting_Unauthenticated_ReturnsUnauthorized"),
    };

    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new()
    {
        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Get
        }).SetName("GetAllSettings_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Post,
            Has_Body = true
        }).SetName("AddSetting_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path = $"settings/{Guid.NewGuid()}",
            Method = HttpMethod.Delete
        }).SetName("DeleteSetting_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path = $"settings/{Guid.NewGuid()}",
            Method = HttpMethod.Put,
            Has_Body = true
        }).SetName("UpdateSetting_AuthenticatedByApiKey_ReturnsUnauthorized"),
    };

    public new static List<TestCaseData> NotFoundTests = new()
    {
        new TestCaseData(new {
            Path = $"settings/{Guid.NewGuid()}",
            Method = HttpMethod.Delete
        }).SetName("DeleteSetting_NotFound_ReturnsNotFound"),

        new TestCaseData(new {
            Path = $"settings/{Guid.NewGuid()}",
            Method = HttpMethod.Put,
            Has_Body = true,
            Request_With_Properties = new {
                Value = "UpdatedValue"
            }
        }).SetName("UpdateSetting_NotFound_ReturnsNotFound"),
    };

    public new static List<TestCaseData> BadRequestTests = new()
    {
        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Post,
            Request_With_Properties = new {
                Key = "", // Invalid key
                Value = "Something"
            }
        }).SetName("AddSetting_EmptyKey_ReturnsBadRequest"),
    };

    public new static List<TestCaseData> ConflictTests = new()
    {
        new TestCaseData(new {
            Path = "settings",
            Add_Before_With_Properties = new {
                Key = "duplicate-key",
                Value = "value1"
            },
            Request_With_Properties = new {
                Key = "duplicate-key",
                Value = "value2"
            },
            Method = HttpMethod.Post
        }).SetName("AddSetting_DuplicateKey_ReturnsConflict"),
    };

    public new static List<TestCaseData> CreatedTests = new()
    {
        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Post,
            Request_With_Properties = new {
                Key = "new-setting",
                Value = "setting-value"
            },
            Check_Result_Properties = new {
                Key = "new-setting",
                Value = "setting-value"
            },
            Check_Id_Exists = false
        }).SetName("AddSetting_ValidRequest_ReturnsCreated")
    };

    public new static List<TestCaseData> OkTests = new()
    {
        new TestCaseData(new {
            Path = "settings",
            Method = HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new { Key = "Alpha", Value = "1" },
                new { Key = "Beta", Value = "2" }
            },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new { Key = "Alpha", Value = "1" },
                new { Key = "Beta", Value = "2" }
            }
        }).SetName("GetAllSettings_ValidRequest_ReturnsOk"),

        new TestCaseData(new {
            Path = "settings?key=beta",
            Method = HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new { Key = "Alpha", Value = "1" },
                new { Key = "Beta", Value = "2" }
            },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new { Key = "Beta", Value = "2" }
            }
        }).SetName("GetSettings_FilterByKey_ReturnsFiltered"),

        new TestCaseData(new {
            Path = "settings?sortBy=key",
            Method = HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new { Key = "z-key", Value = "Z" },
                new { Key = "a-key", Value = "A" }
            },
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new { Key = "a-key", Value = "A" },
                new { Key = "z-key", Value = "Z" }
            }
        }).SetName("GetSettings_SortByKey_ReturnsSortedList"),

        new TestCaseData(new {
            Path = "settings",
            Use_Real_Id_In_Path = true,
            Request_With_Properties = new {
                Value = "Updated"
            },
            Method = HttpMethod.Put
        }).SetName("UpdateSetting_ValidRequest_ReturnsOk"),

        new TestCaseData(new {
            Path = "settings",
            Use_Real_Id_In_Path = true,
            Method = HttpMethod.Delete
        }).SetName("DeleteSetting_ValidRequest_ReturnsOk"),
    };
}
