# FoodRemedy API

This API serves nutritional information for different foods through HTTP endpoints.

Gopher Industries are building this product for an external client "FoodRemedy."

## Contents
- [Authentication](#authentication)
- [Migrations](#migrations)
- [Testing](#testing)
- [Terraform - GCP](#terraform---gcp)
- [Debugging](#debugging)

**This README is a work in progress**

## Authentication
You will initially find that most endpoints give an unauthorized response if tried.

To solve this, you must log in with the existing dev user. Do this by trying out the `POST /auth/login` endpoint and filling out the identifier as "dev" or "dev@deakin" and the password as "12345". The response for this endpoint should contain a string called `accessToken`. Copy this string.

Select the "Authorize" button on the top left and paste the `accessToken` in the Bearer section. After you select "Authorize," you should now be logged in and able to try out any endpoint. You will also be able to create new users and log in with any of them as well.

If you want to try logging in as a client, go to the `POST /auth/api-key` endpoint to create an API key. In the response body, copy the token and go to the "Authorize" option in the top right, then paste it in the `ApiKey` section. Once you click "Authorize" (and after you log out from the Bearer section), you will be logged in as a client with restricted endpoint access.

## Migrations
To update the database, first make your desired changes to the models in the `api/src/foodremedy.database/Models` folder.

Then run this command to create a migration file that will contain the changes to be made to the database:

```bash
python db_migrate.py add NameForYourMigration
```

Check that your new migration looks correct in the `api/src/foodremedy.database/Migrations` folder, then run this to apply it to your database:

```bash
python db_migrate.py update
```

### Common issues with migrations
If the migrations are failing or the database is not being built successfully then this might be caused by you having the wrong version of entity framework or dotnet.

Another possibility is that the port for the database (3306) is already in use. If this is the case then the error will display the following:

```bash
Error response from daemon: Ports are not available: exposing port TCP 0.0.0.0:3306 -> 0.0.0.0:0: listen tcp 0.0.0.0:3306: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
```

This is usually caused by some other mysql process running which also uses port 3306 as default. This can be fixed by ending this process in task manager and then trying to build the database again.

## Testing
To run the automated tests, navigate to the repository in a terminal and run the following command:

```bash
dotnet test api
```

Alternatively, you can navigate into the `api` directory and run:

```bash
dotnet test
```

The response to either should provide the results of the various tests.

If you are experiencing an error while trying to run this, make sure that you do not have an old version of dotnet.

## Terraform - GCP

### Initial Setup
Follow the Terraform GCP setup guide: https://developer.hashicorp.com/terraform/tutorials/gcp-get-started/google-cloud-platform-build

Repeating these steps should not be necessary unless a full rebuild of the project is required.

The `.tfstate` file for this project will be hosted in GCP in a storage bucket. This bucket was created manually and is not managed by Terraform.

In `main.tf`, you can see the bucket configuration:

```terraform
backend "gcs" {
    bucket = "foodremedy-api-tfstate"
    prefix = "env/dev"
}
```

This ensures resources managed by Terraform have a consistent state regardless of who is making changes.

### Terraform Development

* Must have the Terraform CLI installed.
* Must have a credentials file for the service account in the `/terraform` directory named `credentials.json` (**DO NOT COMMIT TO GIT**).
* Run `terraform init` to connect to the GCP backend `tfstate`.
* Make your changes, run `terraform plan`, and set your environment variable in the command line based on the environment you're developing for.
* If the plan is as expected, run `terraform apply` to deploy your changes.

## Debugging

### Viewing Logs
To troubleshoot errors or monitor activity in real-time, you can use the following command to view console logs:

```bash
docker-compose logs -f
```

This will display the logs for all running services, allowing you to track any issues or data being processed during runtime. If you ran the project in the terminal using `python run.py` then these logs will also be showing there.

### Adding Logs for Debugging in .NET
In .NET, you can add logging to track data coming into the API or to help diagnose issues. Below is an example of how you can log details in the `GetFood` endpoint to see the incoming `foodId` and track any errors.

#### Example of a .NET API Debugging Setup

```csharp
[ApiController]
[Route("api/[controller]")]
public class FoodController : ControllerBase
{
    private readonly IFoodRepository _foodRepository;
    private readonly ILogger<FoodController> _logger;

    public FoodController(IFoodRepository foodRepository, ILogger<FoodController> logger)
    {
        _foodRepository = foodRepository;
        _logger = logger;
    }

    [HttpGet("{foodId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Food>> GetFood([FromRoute] Guid foodId)
    {
        // Log the incoming request
        _logger.LogInformation("Received request to get food with ID: {foodId}", foodId);

        try
        {
            // Fetch the food from the database
            var result = await _foodRepository.GetByIdAsync(foodId);

            // Log the result or lack thereof
            if (result == null)
            {
                _logger.LogWarning("Food with ID {foodId} not found.", foodId);
                return NotFound();
            }

            _logger.LogInformation("Successfully retrieved food with ID: {foodId}", foodId);
            return Ok(result.ToResponseModel());
        }
        catch (Exception ex)
        {
            // Log the exception details for debugging
            _logger.LogError(ex, "An error occurred while trying to fetch food with ID: {foodId}", foodId);
            return StatusCode(500, "Internal server error. Please try again later.");
        }
    }
}

```

### What is happening here?
1. **Logging the Request:**
   The line `_logger.LogInformation("Received request to get food with ID: {foodId}", foodId);` helps track when the API receives a request and the specific ID being looked up.

2. **Logging Errors:**
   If something goes wrong, the catch block logs the error with `_logger.LogError(ex, "An error occurred while trying to fetch food with ID: {foodId}", foodId);`, giving details about the exception.

3. **Logging Successful Operations:**
   The message `_logger.LogInformation("Successfully retrieved food with ID: {foodId}", foodId);` confirms the operation was successful and logs the food ID.

### Viewing Logs in Docker
Once you have added logging in the code, you can check these logs in real time using:

```bash
docker-compose logs -f
```

This command will show the logs from all services, including the API, where you can see the information being logged from the code.
