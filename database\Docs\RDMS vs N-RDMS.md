# Relational Database or Non-Relational Database

**Relational databases** are the best choice if your data is **predictable in terms of size**, structure, and access frequency. database's capability to **maintain data integrity** despite errors or interruptions in data processing.

A **non-relational** model works better for storing data that is **flexible in shape or size**, or that may change in the future. can use them **to store images, videos, and documents**.

| **Category** | **Relational database** | **Non-relational database** |
| --- | --- | --- |
| Data model | Tabular | Key-value, document, or graph |
| Data integrity | High with full ACID compliance. | Eventual consistency model. |
| Performance | Improved by adding more resources to the server. | Improved by adding more server nodes |
| Scaling | requires additional data management strategies | scaling is straightforward. |
||||

In general, NoSQL databases are well-suited for storing large amounts of semi-structured or unstructured data, serving real-time data to users, and big data and analytics applications.

Relational databases are a good fit for applications that rely on relationships between data and need easy organization and retrieval of data, complex querying, and transaction support.

# Which Database to use?

Use cases where PostgreSQL is preferred:

1. Web applications requiring high reliability and stability, such as bank systems, process management applications, analytics, and geospatial data.
2. Applications where data security and transactional guarantees are important, such as database automation tools or banking applications.
3. Applications that require support for various SQL features like joins, partitions, and replication.
4. Supported Languages: .Net, C, C++, Java, Perl, Python, TC.
5. Query Language: SQL

## MongoDB:

MongoDB gives us the flexibility to change the data schema at any time. MongoDB can handle operational, transactional, and analytical workloads easily.

Use cases where MongoDB is preferred:

1. Applications that require high scalability and flexibility, such as those dealing with unstructured or semi-structured data.
2. Applications that require fast performance and lower latency.
3. Online applications that have very large data stores where data is required to be kept for years.
4. Supported Languages: Actionscript, C, C#, C++, Clojure, ColdFusion, D, Dart, Delphi, Erlang, Go, Groovy, Haskell, Java, JavaScript, Lisp, Lua, MatLab, Perl, PHP, PowerShell, Prolog, Python, R, Ruby, Scala, SmalltalkSmalltalk.
5. Query Language: API calls, Javascript, REST.

# Concluion:

## Use MongoDB in the below scenarios:

- In MongoDB, we don't have to define the schema to handle data that follows any structure. So in a use case where you have to save unstructured data, MongoDB is the best database to fulfill your requirement.
- If you are handling massive data and, in the future, the data will keep growing in such a case, go for MongoDB.
- Go for MongoDB if most of your applications are cloud-based.
- Use MongoDB if you need Automatic failover and replication.

## Use PostgreSQL in the below scenarios:

- PostgreSQL is ideal if your data is structured and follows a specific pattern.
- Choose PostgreSQL as a database if you need to perform joins frequently.
- PostgreSQL can be a better choice if you have fewer resources.
- Always go with PostgreSQL if your data is not very huge.
- PostgreSQL excels over Mongodb the ease of use. PostgreSQL is an RDBMS; it supports standard SQL queries, which are comparatively easy to learn and implement.
- Go for PostgreSQL if you need transactional and ACID-compliant out-of-the-box.
- PostgreSQL is widely used in large systems where read and write speeds are crucial, and data needs to be validated.
- Images also can be added in PostgreSQL.

## Use MySQL in the below scenarios:

- MySQL provides better security
- The official website of MySQL offers a free download and use of MySQL.
- The majority of operating systems are compatible with MySQL
- MySQL is based on client-server architecture, it can act as a connector, which enables connections between front-end and back-end architecture.
- **Everyone has experience working on MySQL**
