using foodremedy.api.Models.Requests;
using foodremedy.database;
using TagCategory = foodremedy.database.Models.TagCategory;

namespace foodremedy.api.tests.Factories;

public class TagCategoryFactory : IFactory
{
    const string DefaultName = "DefaultName" ;
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public TagCategoryFactory(FoodRemedyDbContext db){
        Db = db;
    }

    public async Task<IDatabaseModel> Add(dynamic? args = null){
        num++;
        args = args ?? new {Email = DefaultName + num};
        TagCategory tagCategory = Db.TagCategory.Add(
            new TagCategory{
                Name = args.GetType().GetProperty("Name") !=  null ? args.Name : DefaultName + num
                }
            ).Entity;
        await Db.SaveChangesAsync();
        return tagCategory;
    }

    public IRequestModel Create(dynamic? args = null){
        num++;
        args = args ?? new {Email = DefaultName + num};
        return new CreateTagCategory(
            args.GetType().GetProperty("Name") !=  null ? args.Name : DefaultName + num
            );
    }
}