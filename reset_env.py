import subprocess

def is_foodremedy(name):
    return "foodremedy" in name

def reset_docker():
    # Stop and remove all containers
    containers = list(filter(is_foodremedy,  subprocess.run(["docker", "ps", "-a", "--format", "{{.Names}}"], capture_output=True, text=True).stdout.split()))
    subprocess.run(["docker", "stop", *containers])

    subprocess.run(["docker", "rm", "--force", *containers])

    # Delete all volumes
    volumes = list(filter(is_foodremedy,  subprocess.run(["docker", "volume", "ls", "--quiet"], capture_output=True, text=True).stdout.split()))
    subprocess.run(["docker", "volume", "rm", "--force", *volumes])

    # Rebuild all images without and call run.py
    subprocess.run(["docker-compose", "build", "--no-cache"])

if __name__ == "__main__":
    reset_docker()
