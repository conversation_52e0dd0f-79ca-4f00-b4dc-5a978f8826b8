using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Collections.Generic;
using System.Linq;

class SwaggerFilter : IDocumentFilter
{
    private readonly List<(string Path, OperationType HttpMethod)> userOnly = new()
    {
        ("/auth/login", OperationType.Post),
        ("/auth/refresh", OperationType.Post),
        ("/auth/{userId}/api-key", OperationType.Post),
        ("/auth/api-key", OperationType.Get),
        ("/auth/api-key/{apiKeyId}", OperationType.Delete),
        ("/foods", OperationType.Post),
        ("/foods/{foodId}", OperationType.Put),
        ("/foods/{foodId}", OperationType.Delete),
        ("/nutrients", OperationType.Post),
        ("/nutrients/{nutrientId}", OperationType.Put),
        ("/nutrients/{nutrientId}", OperationType.Delete),
        ("/tags/categories", OperationType.Post),
        ("/tags/{tagCategoryId}", OperationType.Post),
        ("/tags/{tagId}", OperationType.Put),
        ("/tags/{tagId}", OperationType.Delete),
        ("/users/register", OperationType.Post),
        ("/users", OperationType.Get),
        ("/users/{userId}", OperationType.Get),
        ("/users/{userId}", OperationType.Put),
        ("/users/{status}/{userId}", OperationType.Patch),
    };

    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        if (swaggerDoc.Info.Version == "user")
        {
            swaggerDoc.Components.SecuritySchemes.Add("Bearer", new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Enter a token",
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                BearerFormat = "JWT",
                Scheme = "Bearer"
            });
            swaggerDoc.SecurityRequirements.Add(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    new string[] { }
                }
            });
        }
    }
}