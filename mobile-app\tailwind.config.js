/** @type {import('tailwindcss').Config} */

module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: [
    "./app/**/*.{js,jsx,ts,tsx}",
    "./App.tsx",
    "./components/**/*.{js,jsx,ts,tsx}"
  ],

  presets: [require("nativewind/preset")],

  theme: {
    extend: {
      fontSize: {
        'xxs': '0.625rem'
      },
      fontFamily: {
        spaceMono: ['SpaceMono-Regular', 'ui-sans-serif', 'system-ui', 'sans-serif'],
        drukWide: ['DrukWide-Medium', 'Inter_600SemiBold', 'sans-serif'],
        inter: ['Inter_400Regular'],
        interMedium: ['Inter_500Medium'],
        interSemiBold: ['Inter_600SemiBold'],
        interBold: ['Inter_700Bold'],
      },
      colors: {
        /**
         * Grey Scale Values (Black to White)
         * HSL stands for Hue Saturation Lightness
         */
        hsl5: 'hsl(0, 0% 5%)',
        hsl10: 'hsl(0, 0%, 10%)',
        hsl13: 'hsl(0, 0%, 13%)',
        hsl15: 'hsl(0, 0%, 15%)',
        hsl20: 'hsl(0, 0%, 20%)',
        hsl25: 'hsl(0, 0%, 25%)',
        hsl30: 'hsl(0, 0%, 30%)',
        hsl40: 'hsl(0, 0%, 40%)',
        hsl50: 'hsl(0, 0%, 50%)',
        hsl60: 'hsl(0, 0%, 60%)',
        hsl70: 'hsl(0, 0%, 70%)',
        hsl80: 'hsl(0, 0%, 80%)',
        hsl85: 'hsl(0, 0%, 85%)',
        hsl90: 'hsl(0, 0%, 90%)',
        hsl95: 'hsl(0, 0%, 95%)',
        hsl98: 'hsl(0, 0%, 98%)',
        hsl100: 'hsl(0, 0%, 100%)',

        /**
         * Food Remedy API Primary Colour
         * RGB values (255, 63, 63)
         * Hex #FF3F3F
         */
        primary: 'hsl(0, 100%, 62%)',
      },
    },
  },

  plugins: [],
}