-- IMPORTANT!!!
-- This file contains fake data and insecure credentials so should only be imported in a development enviroment

-- Insert dummy data into the Allergy table
-- INSERT INTO Allergies (Id, Name, Description) VALUES
-- (UUID(), 'Peanut Allergy', 'A common food allergy where even small amounts of peanuts can cause severe and potentially life-threatening reactions.'),
-- (UUID(), 'Shellfish Allergy', 'An allergy to shellfish such as shrimp, crab, or lobster, which can lead to hives, swelling, or anaphylaxis.'),
-- (UUID(), 'Lactose Intolerance', 'A sensitivity to lactose, the sugar in milk and dairy products, causing bloating, cramps, and digestive discomfort.'),
-- (UUID(), 'Gluten Allergy (Celiac Disease)', 'An autoimmune disorder triggered by consuming gluten, a protein in wheat, barley, and rye, leading to intestinal damage.'),
-- (UUID(), 'Egg Allergy', 'A reaction to proteins found in eggs, which can affect the skin, respiratory system, and digestive tract.'),
-- (UUID(), 'Tree Nut Allergy', 'An allergy to nuts such as almonds, walnuts, or cashews, often causing serious allergic reactions.'),
-- (UUID(), 'Soy Allergy', 'Triggered by soy or soy-based products, this allergy can cause a range of symptoms from mild to severe.'),
-- (UUID(), 'Wheat Allergy', 'A reaction to proteins found in wheat, leading to symptoms like skin rashes, nausea, and breathing difficulties.'),
-- (UUID(), 'Fish Allergy', 'An allergy to fish such as salmon, tuna, or cod, which can cause symptoms from hives to anaphylaxis.'),
-- (UUID(), 'Sesame Allergy', 'An increasingly common food allergy involving sesame seeds or oil, which may cause severe allergic responses.');

INSERT INTO User (Id, FirstName, LastName, Username, Email, Status, PasswordHash, PasswordSalt, DateCreated, Role) VALUES
('00000000-0000-0000-0000-000000000000', 'Developer', 'User', 'dev', 'dev@deakin', TRUE, 'K5TcMa10UGRSPWouF9pIknoh/sVy6DLX9MpCY5Thy5Y=', 'e3kEiMyYULitRB/ZOOsN5Q==', NOW(), 'User');

INSERT INTO ApiKey (Id, Token, Name, Status, UserId) VALUES
(UUID(), 'FR-yjAejucMY02-l1hJA75Mo-_DGHtHz4YJ3', 'swagger key', TRUE, '00000000-0000-0000-0000-000000000000');

-- test profile data
-- Development profile data (for user: dev@deakin)

INSERT INTO Profile (Id, UserId, FirstName, LastName, Status, Relationship, Age, AvatarUrl, Allergies, Intolerances, DietaryForm)
VALUES (
  UUID(), '00000000-0000-0000-0000-000000000000', 'Emily', 'Johnson', TRUE, 'Child', 12, NULL,
  'Peanuts,Gluten', 'Lactose', 'Vegetarian'
);


-- INSERT INTO Food (Id, Name, Description, FoodSeason, FoodEnergyWithFibre, FoodEnergyWithoutFibre, ServingSize, Nutrients) VALUES
-- (UUID(), 'Apple', 'A crisp, juicy fruit with a sweet or tart flavor.', 'Fall', 80, 95, 100, "Fiber: 25,Vitamin C: 15"),
-- (UUID(), 'Broccoli', 'A green vegetable with dense florets and a thick stem.', 'Spring', 55, 34, 91, ""),
-- (UUID(), 'Salmon', 'A fatty fish with a distinctive orange-red color and a rich, buttery flavor.', 'Summer', 206, 0, 85, "Omega-3s: 100"),
-- (UUID(), 'Quinoa', 'A grain-like crop with edible seeds, often used as a substitute for rice or couscous.', 'Spring', 222, 4, 185, ""),
-- (UUID(), 'Spinach', 'A leafy green vegetable with a mild, slightly sweet flavor.', 'Spring', 23, 3, 30, "Iron: 20"),
-- (UUID(), 'Banana', 'A soft, sweet fruit with a yellow peel.', 'Summer', 89, 105, 118, "Potassium: 27,Vitamin B6: 20"),
-- (UUID(), 'Carrot', 'A root vegetable, typically orange, with a crisp texture.', 'Fall', 41, 35, 61, "Beta-carotene: 50,Vitamin A: 17"),
-- (UUID(), 'Chicken Breast', 'A lean protein source with a mild flavor.', 'All year', 165, 0, 100, "Protein: 31"),
-- (UUID(), 'Almonds', 'A tree nut high in healthy fats and protein.', 'Fall', 576, 0, 28, "Fiber: 12,Vitamin E: 37"),
-- (UUID(), 'Blueberries', 'A small, sweet blue-purple fruit, rich in antioxidants.', 'Summer', 57, 42, 148, "Vitamin C: 24,Fiber: 8"),
-- (UUID(), 'Walnuts', 'A type of tree nut rich in omega-3 fatty acids and antioxidants.', 'Fall', 654, 0, 28, "Omega-3s: 100,Fiber: 7"),
-- (UUID(), 'Yogurt', 'A dairy product made by fermenting milk, rich in probiotics and calcium.', 'All year', 59, 0, 100, "Calcium: 30,Protein: 10"),
-- (UUID(), 'Sweet Potato', 'A starchy root vegetable with a sweet taste, rich in beta-carotene.', 'Fall', 86, 77, 130, "Beta-carotene: 100,Vitamin A: 38"),
-- (UUID(), 'Lentils', 'A legume rich in protein, fiber, and various nutrients.', 'All year', 116, 0, 100, "Protein: 9,Fiber: 8"),
-- (UUID(), 'Eggs', 'A high-protein food that is versatile in cooking, contains all essential amino acids.', 'All year', 155, 0, 50, "Protein: 13,Vitamin B6: 10");

-- INSERT INTO Nutrient (Id, Name, Description) VALUES
-- (UUID(), 'Vitamin C', 'Supports immune function and collagen production.'),
-- (UUID(), 'Calcium', 'Promotes bone health and muscle function.'),
-- (UUID(), 'Iron', 'Carries oxygen in the blood and supports energy production.'),
-- (UUID(), 'Omega-3s', 'Supports heart health and brain function.'),
-- (UUID(), 'Fiber', 'Supports digestive health and promotes feelings of fullness.'),
-- (UUID(), 'Vitamin A', 'Supports vision, skin health, and immune function.'),
-- (UUID(), 'Vitamin E', 'Acts as an antioxidant and supports skin health.'),
-- (UUID(), 'Beta-carotene', 'A precursor to vitamin A and acts as an antioxidant.'),
-- (UUID(), 'Vitamin B6', 'Helps with brain development and function.'),
-- (UUID(), 'Potassium', 'Regulates fluid balance, muscle contractions, and nerve signals.');

-- INSERT INTO TagCategory (Id, Name) VALUES
-- ('11111111-1111-1111-1111-111111111111', 'Classifications'),
-- ('*************-2222-2222-************', 'Medicinal Values'),
-- ('*************-3333-3333-************', 'Allergies');

-- INSERT INTO Tag (Id, Name, Description, TagCategoryId) VALUES
-- (UUID(), 'Fruit', 'Edible plant parts that are sweet and fleshy.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Vegetable', 'Edible plant parts that are not sweet and fleshy.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Grain', 'Edible seeds from grasses.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Protein', 'Foods high in protein.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Dairy', 'Foods made from milk.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Antioxidant', 'Helps protect cells from damage.', '*************-2222-2222-************'),
-- (UUID(), 'Anti-inflammatory', 'Reduces inflammation in the body.', '*************-2222-2222-************'),
-- (UUID(), 'Probiotic', 'Supports gut health and digestion.', '*************-2222-2222-************'),
-- (UUID(), 'Prebiotic', 'Feeds the good bacteria in the gut.', '*************-2222-2222-************'),
-- (UUID(), 'Cholesterol-lowering', 'Helps lower LDL cholesterol levels.', '*************-2222-2222-************'),
-- (UUID(), 'Gluten', 'Protein found in wheat, barley, and rye.', '*************-3333-3333-************'),
-- (UUID(), 'Lactose', 'Found in Milk and milk products.', '*************-3333-3333-************'),
-- (UUID(), 'Peanuts', 'Legumes with a nutty flavor.', '*************-3333-3333-************'),
-- (UUID(), 'Tree nuts', 'Nuts from trees, such as almonds and walnuts.', '*************-3333-3333-************'),
-- (UUID(), 'Shellfish', 'Crustaceans and mollusks.', '*************-3333-3333-************'),
-- (UUID(), 'Nut', 'Edible seeds or fruit of a plant.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Low-fat', 'Foods that are naturally low in fat content.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'High-protein', 'Foods that are rich in protein content.', '11111111-1111-1111-1111-111111111111'),
-- (UUID(), 'Heart-healthy', 'Foods beneficial for cardiovascular health.', '*************-2222-2222-************');

-- INSERT INTO FoodTag (FoodId, TagsId)
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Apple') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Fruit') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Broccoli') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Vegetable') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Apple') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Antioxidant') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Broccoli') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Antioxidant') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Salmon') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Protein') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Salmon') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Anti-inflammatory') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Quinoa') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Grain') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Spinach') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Antioxidant') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Spinach') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Anti-inflammatory') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Quinoa') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Protein') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Banana') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Fruit') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Banana') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Heart-healthy') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Carrot') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Vegetable') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Carrot') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Antioxidant') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Chicken Breast') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'High-protein') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Chicken Breast') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Low-fat') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Almonds') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Nut') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Almonds') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Heart-healthy') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Blueberries') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Fruit') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Blueberries') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Antioxidant') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Almonds') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Tree nuts') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Walnuts') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Nut') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Walnuts') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Heart-healthy') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Walnuts') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Tree nuts') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Yogurt') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Dairy') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Yogurt') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Lactose') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Yogurt') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Probiotic') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Sweet Potato') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Vegetable') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Sweet Potato') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Antioxidant') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Lentils') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'High-protein') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Lentils') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Grain') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Eggs') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Protein') AS TagsId
-- UNION ALL
-- SELECT 
--     (SELECT Id FROM Food WHERE Name = 'Eggs') AS FoodId, 
--     (SELECT Id FROM Tag WHERE Name = 'Heart-healthy') AS TagsId;

SELECT "** Imported Sample Data" as ""