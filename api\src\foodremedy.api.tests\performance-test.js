import http from 'k6/http';
import { sleep, check } from 'k6';

export let options = {
    vus: 50, // Number of virtual users
    duration: '30s', // Test duration
};

export default function () {
    let res = http.get('http://localhost:5001/swagger');
    check(res, {
        'is status 200': (r) => r.status === 200,
        'response time < 200ms': (r) => r.timings.duration < 200,
    });
    sleep(1);
}

//k6 run C:\Users\<USER>\Documents\GitHub\foodremedy-main\api\src\foodremedy.api.tests\performance-test.js