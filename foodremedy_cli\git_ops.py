# foodremedy_cli/git_ops.py
import subprocess
import os
from colorama import Fore, Style

# Helper function to run git commands
def run_command(command, cwd=None):
    """Runs a shell command and returns output, handles errors."""
    try:
        result = subprocess.run(
            command,
            cwd=cwd, # Run command in a specific directory if needed
            capture_output=True,
            text=True, # Capture output as text (str)
            check=True, # Raise CalledProcessError if command returns non-zero exit status
            shell=False # Don't use shell for security/predictability unless needed
        )
        return result.stdout.strip()
    except FileNotFoundError:
        # This specific error should ideally be caught by the bootstrap check in run_script.py
        print(Fore.RED + f"\nError: Command '{command[0]}' not found.")
        print(Fore.RED + "Please ensure git is installed and in your system's PATH.")
        return None
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError executing command: {' '.join(command)}")
        print(Fore.RED + f"Stderr: {e.stderr.strip()}")
        return None
    except Exception as e:
        print(Fore.RED + f"\nAn unexpected error occurred running command: {e}")
        return None

# --- Git Interaction Functions ---

def get_git_log():
    """Runs 'git log' and returns the output."""
    print(Fore.CYAN + "Running git log...")
    # Use common log formatting options
    log_command = ["git", "log", "--oneline", "--graph", "--decorate", "--all"]
    # Consider adding --max-count=N for very large repos

    # Determine the repository root - run git command from project root
    # Assuming run_script.py is at the project root, and this file is two levels down
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if not os.path.exists(os.path.join(project_root, '.git')):
        print(Fore.YELLOW + f"Warning: Not in a git repository. Cannot run git log.")
        print(Fore.YELLOW + f"Expected git repo at: {project_root}")
        return None

    output = run_command(log_command, cwd=project_root)

    if output is not None: # Only print if command didn't return None due to error
        print(Fore.GREEN + "\nGit Log:")
        print(Style.RESET_ALL + output) # Print logs with default style
    return output

def get_git_status():
    """Runs 'git status' and returns the output."""
    print(Fore.CYAN + "Running git status...")
    status_command = ["git", "status"]

    # Determine the repository root - run git command from project root
    # Assuming run_script.py is at the project root, and this file is two levels down
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if not os.path.exists(os.path.join(project_root, '.git')):
        print(Fore.YELLOW + f"Warning: Not in a git repository. Cannot run git status.")
        print(Fore.YELLOW + f"Expected git repo at: {project_root}")
        return None

    output = run_command(status_command, cwd=project_root)

    if output is not None: # Only print if command didn't return None due to error
        print(Fore.GREEN + "\nGit Status:")
        print(Style.RESET_ALL + output) # Print status with default style
    return output


# Add other git commands here as needed
# def git_pull(): ... subprocess.run(["git", "pull"]) ...
# def git_push(): ... subprocess.run(["git", "push"]) ...
# def git_commit(message): ... subprocess.run(["git", "commit", "-m", message]) ...
