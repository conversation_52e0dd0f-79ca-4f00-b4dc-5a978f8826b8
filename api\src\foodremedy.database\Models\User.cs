﻿namespace foodremedy.database.Models;

public class User : IDatabaseModel
{
    public User(string firstName, string lastName, string email, string username, string passwordHash, string passwordSalt, DateTime dateCreated, int? age, string role)
    {
        FirstName = firstName;
        LastName = lastName;
        Email = email;
        Username = username;
        PasswordHash = passwordHash;
        PasswordSalt = passwordSalt;
        Status = true;
        DateCreated = DateTime.UtcNow;
        Age = age;
        Role = role;
        ApiKeys = new List<ApiKey>();
    }

    public Guid Id { get; init; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string Username { get; set; }
    public string PasswordHash { get; set; }
    public string PasswordSalt { get; set; }
    public bool Status { get; set; }

    public DateTime DateCreated { get; set; }
    public int? Age { get; set; }
    public string? Address { get; set; }
    public string? PhoneNumber { get; set; }
    public Guid? RefreshTokenId { get; set; }
    public IEnumerable<ApiKey>? ApiKeys { get; init; }
    
    public string Role{ get; set; }

}
