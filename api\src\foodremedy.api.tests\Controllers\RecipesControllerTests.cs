using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.tests.Factories;
using Microsoft.VisualBasic;

namespace foodremedy.api.tests.Controllers;

    /// <summary>
    /// Contains the test cases for the RecipesController.
    /// Tests include unauthenticated, authenticated, conflict, bad request, and created scenarios for the Recipe API.
    /// </summary>
internal class RecipesControllerTests : ControllerTestFixture
{
    /// <summary>
    /// Gets the factory used to create recipe objects.
    /// </summary>
    public override IFactory Factory => recipeFactory;

    // Unauthenticated tests
    /// <summary>
    /// Test cases where the request is unauthenticated and should return an Unauthorized status.
    /// </summary>
    public new static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>
    {
        new TestCaseData(new
        {
            Path = "recipe",
            Method = HttpMethod.Post,
            Has_Body = true
        }).SetName("CreateRecipe_UnauthenticatedRequest_ReturnsUnauthorized")
    };

    // Authenticated but unauthorized (e.g., API key instead of user)
    /// <summary>
    /// Test cases where the request is authenticated but by API key instead of a user, expecting Unauthorized status.
    /// </summary>
    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>
    {
        new TestCaseData(new
        {
            Path = "recipe",
            Method = HttpMethod.Post,
            Has_Body = true
        }).SetName("CreateRecipe_AuthenticatedByApiKey_ReturnsUnauthorized")
    };

    // Conflict tests (duplicate name)
    /// <summary>
    /// Test cases for conflicts such as attempting to create a recipe with a duplicate name.
    /// </summary>
    public new static List<TestCaseData> ConflictTests = new List<TestCaseData>
    {
        new TestCaseData(new
        {
            Path = "recipe",
            Add_Before_With_Properties = new
            {
                Name = "Grilled Chicken",
                Details = "Existing recipe",
                Calories = 200.00m,
                Fat = 8.00m,
                Protein = 25.00m,
                Carbohydrates = 4.00m,
                Reference = "https://example.com/existing"
            },
            Request_With_Properties = new
            {
                Name = "Grilled Chicken", // Same name as existing
                Details = "A simple grilled chicken recipe.",
                Calories = 250.50m,
                Fat = 10.00m,
                Protein = 30.00m,
                Carbohydrates = 5.00m,
                Reference = "https://example.com/grilled-chicken"
            },
            Method = HttpMethod.Post
        }).SetName("CreateRecipe_DuplicateName_ReturnsConflict")
    };

    // Bad request tests (invalid input)
    /// <summary>
    /// Test cases for invalid requests, expecting Bad Request status.
    /// </summary>
    public new static List<TestCaseData> BadRequestTests = new List<TestCaseData>
    {
        new TestCaseData(new
        {
            Path = "recipe",
            Request_With_Properties = new
            {
                Name = "", // Invalid: empty name
                Details = "A simple grilled chicken recipe.",
                Calories = 250.50m,
                Fat = 10.00m,
                Protein = 30.00m,
                Carbohydrates = 5.00m,
                Reference = "https://example.com/grilled-chicken"
            },
            Method = HttpMethod.Post
        }).SetName("CreateRecipe_EmptyName_ReturnsBadRequest"),

        new TestCaseData(new
        {
            Path = "recipe",
            Request_With_Properties = new
            {
                Name = "Grilled Chicken",
                Details = "A simple grilled chicken recipe.",
                Calories = -1.00m, // Invalid: negative calories
                Fat = 10.00m,
                Protein = 30.00m,
                Carbohydrates = 5.00m,
                Reference = "https://example.com/grilled-chicken"
            },
            Method = HttpMethod.Post
        }).SetName("CreateRecipe_NegativeCalories_ReturnsBadRequest")
    };

    // Created tests
    /// <summary>
    /// Test cases for successfully creating a recipe, expecting Created status.
    /// </summary>
    public new static List<TestCaseData> CreatedTests = new List<TestCaseData>
    {
        new TestCaseData(new
        {
            Path = "recipe",
            Method = HttpMethod.Post,
            Request_With_Properties = new
            {
                Name = "SimpleRecipe_" + Guid.NewGuid().ToString(),
                Details = "A simple test recipe.",
                Calories = 100.00m,
                Fat = 5.00m,
                Protein = 10.00m,
                Carbohydrates = 15.00m,
                Reference = "https://example.com/simple-recipe"
            }
        }).SetName("CreateRecipe_SimpleValidRequest_ReturnsCreated")
    };
}