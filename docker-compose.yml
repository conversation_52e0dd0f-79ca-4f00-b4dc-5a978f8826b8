# This file is for testing purposes only
# This should not be deployed

services:
  api:
    # image: darsh1808/food-remedy:latest
    build:
      context: api/
      dockerfile: Dockerfile
    ports:
      - "5001:80" 
    volumes:
      - ./logs:/logs
    develop: 
      watch:
        - action: rebuild
          path: api/src
      
  db:
    image: mysql:5.7
    platform: linux/amd64
    environment:
      MYSQL_DATABASE: 'foodremedy'
      MYSQL_ROOT_PASSWORD: 'password'
    ports:
      - '3306:3306'
    expose:
      - '3306'
    volumes:
      - my-db:/var/lib/mysql
      - './database/code:/database/code'
volumes:
  my-db: