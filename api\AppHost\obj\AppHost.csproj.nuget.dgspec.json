{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost\\AppHost.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost\\AppHost.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost\\AppHost.csproj", "projectName": "AppHost", "projectPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost\\AppHost.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\soft\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aspire.Dashboard.Sdk.win-x64": {"target": "Package", "version": "[9.1.0, )", "autoReferenced": true}, "Aspire.Hosting.AppHost": {"target": "Package", "version": "[9.1.0, )"}, "Aspire.Hosting.MySql": {"target": "Package", "version": "[9.1.0, )"}, "Aspire.Hosting.NodeJs": {"target": "Package", "version": "[9.1.0, )"}, "Aspire.Hosting.Orchestration.win-x64": {"target": "Package", "version": "[9.1.0, )", "autoReferenced": true}, "Aspire.MySqlConnector": {"target": "Package", "version": "[9.1.0, )"}, "Azure.Monitor.OpenTelemetry.AspNetCore": {"target": "Package", "version": "[1.2.0, )"}, "DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "MySqlConnector": {"target": "Package", "version": "[2.4.0, )"}, "OpenTelemetry": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Exporter.Console": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Exporter.Jaeger": {"target": "Package", "version": "[1.5.1, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.11.1, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.11.1, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}