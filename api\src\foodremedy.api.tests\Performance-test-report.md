This is a document reporting the findings of using k6 to test the performance of the swagger site. 
# Tools needed to run this code
    For anyone to be able to use the code in "performance-test.js" you will first need to download k6 using this link: https://github.com/grafana/k6/releases
    Once downloaded you will need to extract it and place it in a directory (e.g C:\k6)
    You will also need to add that directory's path to your systems PATH to be able to run k6 from any terminal

    To run this js file, the API/ server must be actively running then go into any terminal and type "k6 run performance-test.js" it will execute the script

# Scenario Details:
    VUs (Virtual Users): Your test used 50 virtual users (VUs), simulating real user activity on the API.

    Test Duration: The test was set to run for 30 seconds with a graceful stop period of 30 seconds, which allows users to finish their requests before stopping.

# Total Results:
    checks_total: 3000 checks were performed. This represents the total number of requests made by the virtual users.

    checks_succeeded: 100% of the checks succeeded (3000 out of 3000). This means all requests were successful.

    checks_failed: 0% failed, indicating that no request resulted in an error.

# HTTP Request Results:
    http_req_duration: This shows the time it took for HTTP requests to complete.

    avg=12.51ms: The average time it took for the requests to complete was 12.51 milliseconds.

    min=544.29µs: The minimum response time was 544.29 microseconds.

    max=425.82ms: The maximum response time was 425.82 milliseconds, which is the highest time for a single request.

    p(90)=13.88ms: 90% of requests were completed in less than 13.88 milliseconds.

    p(95)=23.85ms: 95% of requests were completed in less than 23.85 milliseconds.

    http_req_failed: No requests failed (0% failure rate).

    http_reqs: 3000 total HTTP requests were made, with a rate of 97.35 requests per second.

# Execution Details:
    iteration_duration: The average duration for one complete iteration (from start to finish of all actions in the script) was 1.02 seconds.

    min=1s: The minimum duration of an iteration was 1 second.

    max=1.46s: The maximum duration of an iteration was 1.46 seconds.

    p(90)=1.02s: 90% of iterations completed in less than 1.02 seconds.

    iterations: A total of 1500 iterations were completed, with a rate of 48.67 iterations per second.

    vus: The test ran with 50 virtual users, and this number remained constant throughout the test.

# Network Details:
    data_received: 7.9 MB of data was received from the server during the test, with a rate of 257 kB/s.

    data_sent: 347 kB of data was sent to the server during the test, with a rate of 11 kB/s.

# General Observations:
    The test was successful with no failed requests.

    The response time is quite low, with an average of 12.51ms, which is a good indication of a fast and efficient API.

    The maximum response time was 425.82ms, which may be something to investigate further if the API is expected to handle requests in a real-world environment where low latency is critical.

    The requests per second rate of 97.35 and the iteration rate of 48.67 per second indicate that the server is capable of handling a substantial load without errors.