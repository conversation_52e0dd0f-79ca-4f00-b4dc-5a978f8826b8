﻿﻿using foodremedy.api.Configuration;
using foodremedy.api.Providers;
using foodremedy.api.Repositories;
using foodremedy.api.Utils;
using Microsoft.IdentityModel.Tokens;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using foodremedy.database.Models;

using System.Security.Claims;

namespace foodremedy.api.Extensions;

public static class ServiceCollectionExtensions
{
    public static void UseLowercaseUrls(this IServiceCollection serviceCollection)
    {
        serviceCollection.Configure<RouteOptions>(options => { options.LowercaseUrls = true; });
    }

    public static void AddInternalServices(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddTransient<IUserRepository, UserRepository>();
        serviceCollection.AddTransient<IRefreshTokenRepository, RefreshTokenRepository>();
        serviceCollection.AddTransient<IAuthenticationProvider, AuthenticationProvider>();
        serviceCollection.AddTransient<ITagRepository, TagRepository>();
        serviceCollection.AddTransient<IFoodRepository, FoodRepository>();
        serviceCollection.AddTransient<ITagCategoryRepository, TagCategoryRepository>();
        serviceCollection.AddTransient<INutrientRepository, NutrientRepository>();
        serviceCollection.AddTransient<IApiKeyRepository, ApiKeyRepository>();
        serviceCollection.AddTransient<IConfigurationRepository, ConfigurationRepository>();
        serviceCollection.AddTransient<IAllergyRepository, AllergyRepository>(); // Add AllergyRepository
        serviceCollection.AddTransient<IProfileRepository, ProfileRepository>(); // Add ProfileRepository

    }

    public static void AddJwtAndApiKeyAuthentication(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        serviceCollection
            .AddOptions<AuthenticationConfiguration>()
            .Bind(configuration.GetSection(AuthenticationConfiguration.ConfigurationSection))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        var authenticationConfiguration = configuration
            .GetSection(AuthenticationConfiguration.ConfigurationSection)
            .Get<AuthenticationConfiguration>();

        serviceCollection.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer("Bearer", conf =>
        {
            conf.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidAudience = authenticationConfiguration!.Audience,
                ValidIssuer = authenticationConfiguration.Issuer,
                IssuerSigningKey = SigningKeyFactory.Get(authenticationConfiguration.SigningKey),
                RoleClaimType = ClaimTypes.Role
            };
        })
        .AddScheme<ApiKeyAuthenticationOptions, ApiKeyAuthenticationHandler>(ApiKeyAuthenticationOptions.DefaultScheme, options =>
        {
            options.InvalidApiKeyLog = (LogLevel.Warning, "Someone attempted to use an invalid API Key: {ApiKey}");
        });

        serviceCollection.AddAuthorization(options =>
        {
            options.AddPolicy("UserOnly", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme);
            });

            // options.AddPolicy("UserOnly", policy =>
            // {
            //     policy.RequireAuthenticatedUser();
            //     policy.AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme, ApiKeyAuthenticationOptions.DefaultScheme);
            // });

        });
    }

    public static void ConfigureCors(this IServiceCollection serviceCollection)
    {
        if (!EnvironmentHelper.IsDevelopment()) //TODO: Configure CORS for Production environment
            return;

        serviceCollection.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder.AllowAnyHeader();
                builder.AllowAnyMethod();
                builder.AllowAnyOrigin();
            });
        });
    }

    public static IServiceCollection AddDefaultApiKeyGenerator(this IServiceCollection services, ApiKeyGenerationOptions apiKeyGenerationOptions)
    {
        apiKeyGenerationOptions.Validate();
        return services.AddSingleton<IApiKeyFactory, DefaultApiKeyFactory>(sp => new DefaultApiKeyFactory(apiKeyGenerationOptions));
    }

    /// <summary>
    /// Adds the default claims principal factory.
    /// </summary>
    /// <remarks>
    /// Creates a principal with the single claim of the owner ID.
    /// </remarks>
    /// <param name="services">The container to add the services to.</param>
    /// <returns>The service container for further chaining.</returns>
    public static IServiceCollection AddDefaultClaimsPrincipalFactory(this IServiceCollection services)
    {
        return services.AddSingleton<IClaimsPrincipalFactory, DefaultClaimsPrincipalFactory>();
    }
}
