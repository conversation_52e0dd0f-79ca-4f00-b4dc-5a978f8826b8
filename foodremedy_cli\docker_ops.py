import subprocess
import os
import time
from colorama import Fore, Style # Import necessary colorama components

def check_docker_status():
    """Checks the status of the Docker container named 'db'."""
    try:
        print(Fore.GREEN + "Checking Docker Container Status for 'db'...")
        # Note: Using docker ps --filter name=db might return nothing if the container exists but is not running.
        # A more robust check might involve `docker inspect` or `docker ps -a`.
        # Let's stick to the original script's logic for now.
        result = subprocess.run(["docker", "ps", "--filter", "name=db", "--format", "table {{.Names}}\t{{.Status}}"], capture_output=True, text=True, check=True)
        output = result.stdout.strip()
        if "db" in output: # Check if 'db' container info is in the output
            print(Fore.GREEN + "\nDocker Container Status:")
            print(output)
        else:
             # Even if check=True didn't raise an error, the container might not be running
             print(Fore.RED + "\nNo Docker container with the name 'db' found running.")

    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError checking Docker status: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")

def run_docker_db_daemon():
    """Starts the 'db' service using docker-compose up -d."""
    try:
        print(Fore.GREEN + "Starting Docker container 'db' in daemon mode...")
        # Assuming docker-compose.yml is in the parent directory or project root
        subprocess.run(["docker-compose", "up", "-d", "db"], check=True)
        print(Fore.GREEN + "Docker container 'db' started successfully in daemon mode.")
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError starting Docker container: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")

def stop_docker_db():
    """Stops the Docker container 'db' using docker-compose stop."""
    try:
        print(Fore.GREEN + "Stopping Docker container 'db'...")
        # Assuming docker-compose.yml is in the parent directory or project root
        subprocess.run(["docker-compose", "stop", "db"], check=True)
        print(Fore.GREEN + "Docker container 'db' stopped.")
    except subprocess.CalledProcessError as e:
        # docker-compose stop exits with 1 if the service isn't running. Handle this specifically.
        if b"service \"db\" is not running" in e.stderr:
             print(Fore.YELLOW + "Docker container 'db' is not running.")
        else:
            print(Fore.RED + f"\nError stopping Docker container: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")

def remove_docker_container():
    """Removes the Docker container 'db' using docker-compose rm."""
    try:
        print(Fore.GREEN + "Removing Docker container 'db' if it exists...")
        # Use docker-compose down or stop then rm for reliability with compose
        # stop_docker_db() # Optionally stop it first, though rm -f should handle it
        # Assuming docker-compose.yml is in the parent directory or project root
        subprocess.run(["docker-compose", "rm", "-f", "db"], check=True)
        print(Fore.GREEN + "Docker container 'db' removed.")
    except subprocess.CalledProcessError as e:
        # Handle case where the container doesn't exist
        if b"no such service: db" in e.stderr or b"No stopped containers" in e.stderr:
             print(Fore.YELLOW + "Docker container 'db' does not exist or is already removed.")
        else:
            print(Fore.RED + f"\nError removing Docker container: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")

def list_docker():
    """Lists all Docker containers, images, and volumes."""
    try:
        print(Fore.GREEN + "Getting list of all containers, images, and volumes...")
        # Using --all to show stopped containers too
        containers = subprocess.run(["docker", "ps", "-a", "--format", "{{.Names}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        images = subprocess.run(["docker", "images", "--format", "{{.Repository}}:{{.Tag}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        volumes = subprocess.run(["docker", "volume", "ls", "--format", "{{.Name}}"], capture_output=True, text=True, check=True).stdout.splitlines()

        print(Fore.GREEN + "\nDocker Resources:")
        print(Fore.GREEN + "{:<30} {:<30} {:<30}".format("Containers (All)", "Images", "Volumes"))
        print(Fore.GREEN + "-" * 90) # Adjusted width for formatting
        for i in range(max(len(containers), len(images), len(volumes))):
            container = containers[i] if i < len(containers) else ""
            image = images[i] if i < len(images) else ""
            volume = volumes[i] if i < len(volumes) else ""
            print("{:<30} {:<30} {:<30}".format(container, image, volume))
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError listing containers, images, and volumes: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")


def remove_docker_image():
    """Lists Docker images, prompts user to select and remove one."""
    try:
        images = subprocess.run(["docker", "images", "--format", "{{.Repository}}:{{.Tag}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        if not images or (len(images) == 1 and images[0] == "<none>:<none>"): # Handle empty or only dangling images initially
             print(Fore.YELLOW + "No Docker images available to remove.")
             return

        print(Fore.GREEN + "Select the Docker image to remove:")
        for i, image in enumerate(images):
             print(f"{i+1}. {image}")

        # Using a simple input here, could integrate with the menu_with_arrows_and_numbers from utils if preferred
        while True:
            try:
                index = int(input(Fore.GREEN + "Enter the number of the image to remove: ")) - 1
                if index < 0 or index >= len(images):
                    print(Fore.RED + "Invalid number. Please select from the list.")
                else:
                    break
            except ValueError:
                print(Fore.RED + "Invalid input. Please enter a number.")

        image_name = images[index]
        print(Fore.GREEN + f"Removing Docker image '{image_name}'...")
        # Use -f to force removal even if tagged by multiple images or used by stopped containers
        subprocess.run(["docker", "rmi", "-f", image_name], check=True)
        print(Fore.GREEN + f"Docker image '{image_name}' removed.")

    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError removing Docker image: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")


def remove_docker_volume():
    """Lists Docker volumes, prompts user to select and remove one."""
    try:
        volumes = subprocess.run(["docker", "volume", "ls", "--format", "{{.Name}}"], capture_output=True, text=True, check=True).stdout.splitlines()
        if not volumes:
            print(Fore.YELLOW + "No Docker volumes available to remove.")
            return

        print(Fore.GREEN + "Select the Docker volume to remove:")
        for i, volume in enumerate(volumes):
            print(f"{i+1}. {volume}")

        # Using a simple input here, could integrate with the menu_with_arrows_and_numbers from utils if preferred
        while True:
            try:
                index = int(input(Fore.GREEN + "Enter the number of the volume to remove: ")) - 1
                if index < 0 or index >= len(volumes):
                     print(Fore.RED + "Invalid number. Please select from the list.")
                else:
                    break
            except ValueError:
                print(Fore.RED + "Invalid input. Please enter a number.")

        volume_name = volumes[index]
        print(Fore.GREEN + f"Removing Docker volume '{volume_name}'...")
        subprocess.run(["docker", "volume", "rm", "-f", volume_name], check=True)
        print(Fore.GREEN + f"Docker volume '{volume_name}' removed.")

    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError removing Docker volume: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Docker command not found. Please ensure Docker is installed and in your system's PATH.")
