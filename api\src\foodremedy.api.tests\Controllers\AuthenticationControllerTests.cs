﻿using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using Microsoft.EntityFrameworkCore;
using foodremedy.api.tests.Factories;

namespace foodremedy.api.tests.Controllers;

/// <summary>
/// Contains integration tests for the AuthenticationController to verify login, token refresh,
/// and API key functionality.
/// </summary>
internal sealed class AuthenticationControllerTests : ControllerTestFixture
{
    private const string _email = "dev@deakin";
    private const string _password = "dev";
    private const string _username = "12345";

    /// <summary>
    /// Tests that a valid login returns a valid JWT access token with expected claims and expiry.
    /// </summary>
    [Test]
    public async Task AttemptLogin_ValidDetails_ReturnsAccessToken()
    {
        await CreateTestUser();

        var response = await _webApiClient.PostAsync("auth/login", JsonContent.Create(authenticationFactory.CreateLogin(_email,_username, _password )));
        var result = await response.Content.ReadFromJsonAsync<AccessTokenCreated>();
        var jwtToken = new JwtSecurityTokenHandler().ReadJwtToken(result?.AccessToken);

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.Should().NotBeNull();
        result!.ExpiresIn.Should().BeGreaterThan(0);
        result.RefreshToken.Should().NotBeNullOrWhiteSpace();
        result.AccessToken.Should().NotBeNullOrWhiteSpace();
        result.TokenType.Should().Be("Bearer");
        jwtToken.Audiences.Should().ContainSingle().Which.Should().Be("FoodRemedy-API");
        jwtToken.Issuer.Should().Be("FoodRemedy-API");
        jwtToken.ValidFrom.Should().BeCloseTo(DateTime.Now.ToUniversalTime(), TimeSpan.FromMinutes(1));
        jwtToken.ValidTo.Should().BeCloseTo(DateTime.Now.AddHours(1).ToUniversalTime(), TimeSpan.FromMinutes(1));
        jwtToken.Subject.Should().NotBeNullOrWhiteSpace();
        jwtToken.Claims.Should().Satisfy(
            claim => claim.Type == "sub" && claim.Value == jwtToken.Subject,
            claim => claim.Type == "jti" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "iat" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "subject" && claim.Value == jwtToken.Subject,
            claim => claim.Type == "nbf" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "exp" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "iss" && claim.Value == "FoodRemedy-API",
            claim => claim.Type == "aud" && claim.Value == "FoodRemedy-API"
        );
    }

    /// <summary>
    /// Creates a test user in the database with predefined credentials.
    /// </summary>
    private async Task CreateTestUser()
    {
        await userFactory.Add(new{
            Email = _email, 
            Password = _password, 
            Username = _username
        });
    }

    /// <summary>
    /// Tests that attempting to login with invalid credentials returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task AttemptLogin_InvalidLogin_ReturnsUnauthorised()
    {
        const string email = "<EMAIL>";
        const string password = "password";
        const string username = "testuser";

        var response = await _webApiClient.PostAsync("auth/login", JsonContent.Create(authenticationFactory.CreateLogin(email, username,password)));

        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests that an unauthenticated request to refresh the access token returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task RefreshAccessToken_Unauthenticated_ReturnsUnauthorised()
    {
        var response = await _webApiClient.PostAsync("auth/refresh", JsonContent.Create(
            authenticationFactory.CreateRefreshAccessToken()
        ));

        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests that an API key authenticated request to refresh a token returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task RefreshAccessToken_AuthenticatedbyApiKey_ReturnsUnauthorised()
    {
        var request = new HttpRequestMessage(HttpMethod.Post, "auth/refresh")
        {
            Content = JsonContent.Create(authenticationFactory.CreateRefreshAccessToken())
        };

        var response = await SendApiKeyAuthenticatedRequestAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }
    
    /// <summary>
    /// Tests that a refresh token request fails when the associated user no longer exists.
    /// </summary>
    [Test]
    public async Task RefreshAccessToken_UserDoesNotExist_ReturnsUnauthorised()
    {
        await CreateTestUser();
        var loginResponse = await _webApiClient.PostAsync("auth/login", JsonContent.Create(authenticationFactory.CreateLogin(_email, _username, _password)));
        var result = await loginResponse.Content.ReadFromJsonAsync<AccessTokenCreated>();
        var jwtToken = new JwtSecurityTokenHandler().ReadJwtToken(result?.AccessToken);
        
        var user = await DbContext.User.SingleOrDefaultAsync(p => p.Id == Guid.Parse(jwtToken.Subject));
        ArgumentNullException.ThrowIfNull(user);
        DbContext.Remove(user);
        await DbContext.SaveChangesAsync();

        var request = new HttpRequestMessage(HttpMethod.Post, "auth/refresh")
        {
            Content = JsonContent.Create(authenticationFactory.CreateRefreshAccessToken(result!.RefreshToken)),
            Headers = { {"Authorization", $"Bearer {result.AccessToken}"} }
        };

        var response = await _webApiClient.SendAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }
    
    /// <summary>
    /// Tests that a refresh token request with an invalid token returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task RefreshAccessToken_RefreshTokenIsInvalid_ReturnsUnauthorised()
    {
        await CreateTestUser();
        var loginResponse = await _webApiClient.PostAsync("auth/login", JsonContent.Create(authenticationFactory.CreateLogin(_email, _username,_password)));
        var result = await loginResponse.Content.ReadFromJsonAsync<AccessTokenCreated>();
        
        var request = new HttpRequestMessage(HttpMethod.Post, "auth/refresh")
        {
            Content = JsonContent.Create(authenticationFactory.CreateRefreshAccessToken("invalid refresh token")),
            Headers = { {"Authorization", $"Bearer {result!.AccessToken}"} }
        };
        
        var response = await _webApiClient.SendAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }
    
    /// <summary>
    /// Tests that a valid refresh token request returns a new access token and refresh token.
    /// </summary>
    [Test]
    public async Task RefreshAccessToken_ValidRequest_ReturnsOkWithAccessTokenCreated()
    {
        await CreateTestUser();
        var loginResponse = await _webApiClient.PostAsync("auth/login", JsonContent.Create(authenticationFactory.CreateLogin(_email, _username, _password)));
        var accessTokenCreated = await loginResponse.Content.ReadFromJsonAsync<AccessTokenCreated>();
        
        var request = new HttpRequestMessage(HttpMethod.Post, "auth/refresh")
        {
            Content = JsonContent.Create(authenticationFactory.CreateRefreshAccessToken(accessTokenCreated!.RefreshToken)),
            Headers = { {"Authorization", $"Bearer {accessTokenCreated.AccessToken}"} }
        };
        
        var response = await _webApiClient.SendAsync(request);
        var result = await response.Content.ReadFromJsonAsync<AccessTokenCreated>();
        var jwtToken = new JwtSecurityTokenHandler().ReadJwtToken(result?.AccessToken);

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.Should().NotBeNull();
        result!.ExpiresIn.Should().BeGreaterThan(0);
        result.RefreshToken.Should().NotBeNullOrWhiteSpace();
        result.AccessToken.Should().NotBeNullOrWhiteSpace();
        result.TokenType.Should().Be("Bearer");
        jwtToken.Audiences.Should().ContainSingle().Which.Should().Be("FoodRemedy-API");
        jwtToken.Issuer.Should().Be("FoodRemedy-API");
        jwtToken.ValidFrom.Should().BeCloseTo(DateTime.Now.ToUniversalTime(), TimeSpan.FromMinutes(1));
        jwtToken.ValidTo.Should().BeCloseTo(DateTime.Now.AddHours(1).ToUniversalTime(), TimeSpan.FromMinutes(1));
        jwtToken.Subject.Should().NotBeNullOrWhiteSpace();
        jwtToken.Claims.Should().Satisfy(
            claim => claim.Type == "sub" && claim.Value == jwtToken.Subject,
            claim => claim.Type == "jti" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "iat" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "subject" && claim.Value == jwtToken.Subject,
            claim => claim.Type == "nbf" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "exp" && !string.IsNullOrWhiteSpace(claim.Value),
            claim => claim.Type == "iss" && claim.Value == "FoodRemedy-API",
            claim => claim.Type == "aud" && claim.Value == "FoodRemedy-API"
        );
    }

    /// <summary>
    /// Tests that a valid API key creation request returns 200 OK and the new API key works.
    /// </summary>
    [Test]
    public async Task CreateApiKey_ValidRequest_ReturnsOkWithApiKeyCreated()
    {
        var user = (database.Models.User) await userFactory.Add();
        var request = new HttpRequestMessage(HttpMethod.Post, $"auth/{user.Id}/api-key"){
                Content = JsonContent.Create(authenticationFactory.CreateApiKey())
            };
        var response = await SendAuthenticatedRequestAsync(request);
        var result = await response.Content.ReadFromJsonAsync<string>();

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.Should().NotBeNull();

        var nutrientRequest = new HttpRequestMessage(HttpMethod.Get, "nutrients");
        var nutrientResponse = await SendApiKeyAuthenticatedRequestAsync(nutrientRequest, result!);

        nutrientResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    }
    
    /// <summary>
    /// Tests that attempting to create an API key for a disabled client returns 400 Bad Request.
    /// </summary>
    [Test]
    public async Task DeleteApiKey_DisabledClient_ReturnsBadRequest()
    {
        var user = (database.Models.User) await userFactory.Add(new{
            Status = false
        });
        var request = new HttpRequestMessage(HttpMethod.Post, $"auth/{user.Id}/api-key"){
                Content = JsonContent.Create(authenticationFactory.CreateApiKey())
            };
        var response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    /// <summary>
    /// Tests that an API key authenticated user cannot create new API keys.
    /// </summary>
    [Test]
    public async Task CreateApiKey_AuthenticatedByApiKey_ReturnsUnauthorised()
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"auth/{Guid.NewGuid()}/api-key"){
                Content = JsonContent.Create(authenticationFactory.CreateApiKey())
            };
        var response = await SendApiKeyAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests that an unauthenticated API key creation request returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task CreateApiKey_UnauthenticatedRequest_ReturnsUnauthorised()
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"auth/{Guid.NewGuid()}/api-key"){
                Content = JsonContent.Create(authenticationFactory.CreateApiKey())
            };
        var response = await _webApiClient.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests that creating an API key for a non-existent client returns 404 Not Found.
    /// </summary>
    [Test]
    public async Task CreateApiKey_ClientDoesNotExist_ReturnsNotFound()
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"auth/{Guid.NewGuid()}/api-key"){
                Content = JsonContent.Create(authenticationFactory.CreateApiKey())
            };
        var response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    /// <summary>
    /// Tests that deleting an API key using API key authentication returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task DeleteApiKey_AuthenticatedByApiKey_ReturnsUnauthorised()
    {
        var request = new HttpRequestMessage(HttpMethod.Delete, $"auth/api-key/{Guid.NewGuid()}");
        var response = await SendApiKeyAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests that deleting an API key without authentication returns 401 Unauthorized.
    /// </summary>
    [Test]
    public async Task DeleteApiKey_UnauthenticatedRequest_ReturnsUnauthorised()
    {
        var request = new HttpRequestMessage(HttpMethod.Delete, $"auth/api-key/{Guid.NewGuid()}");
        var response = await _webApiClient.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests that deleting a non-existent API key returns 404 Not Found.
    /// </summary>
    [Test]
    public async Task DeleteApiKey_ApiKeyDoesNotExist_ReturnsNotFound()
    {
        var request = new HttpRequestMessage(HttpMethod.Delete, $"auth/api-key/{Guid.NewGuid()}");
        var response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    /// <summary>
    /// Tests that a valid API key deletion request returns 204 No Content.
    /// </summary>
    [Test]
    public async Task DeleteApiKey_ValidRequest_ReturnsNoContent()
    {
        var dbuser = await userFactory.Add();
        var dbapikey = await authenticationFactory.AddApiKey(user: dbclient);
        var request = new HttpRequestMessage(HttpMethod.Delete, $"auth/api-key/{dbapikey.Id}");
        var response = await SendAuthenticatedRequestAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
    }
}
