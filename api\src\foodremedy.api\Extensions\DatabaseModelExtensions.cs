﻿using System.IdentityModel.Tokens.Jwt;
using foodremedy.api.Models.Responses;
using foodremedy.database.Models;
using Food = foodremedy.database.Models.Food;
using Tag = foodremedy.api.Models.Responses.Tag;
using TagCategory = foodremedy.api.Models.Responses.TagCategory;
using User = foodremedy.database.Models.User;
using Nutrient = foodremedy.api.Models.Responses.Nutrient;

namespace foodremedy.api.Extensions;

public static class DatabaseModelExtensions
{
    public static Tag ToResponseModel(this database.Models.Tag tag)
    {
        return new Tag(tag.Id, tag.Name, tag.TagCategory.Name, tag.Description);
    }

    public static AccessTokenCreated ToResponseModel(this JwtSecurityToken accessToken, RefreshToken refreshToken)
    {
        return new AccessTokenCreated(
            "Bearer",
            new JwtSecurityTokenHandler().WriteToken(accessToken),
            (int)(accessToken.ValidTo - DateTime.UtcNow).TotalSeconds,
            refreshToken.Token
        );
    }

    public static Models.Responses.ApiKey ToResponseModel(this database.Models.ApiKey apiKey)
    {
        return new Models.Responses.ApiKey(apiKey.Id, apiKey.Name, apiKey.Status, $"{apiKey.User.FirstName} {apiKey.User.LastName}");
    }

    public static Nutrient ToResponseModel(this database.Models.Nutrient nutrient)
    {
        return new Nutrient(nutrient.Id, nutrient.Name, nutrient.Description);
    }

    public static FoodSummary ToSummaryResponseModel(this Food food)
    {
        Dictionary<string, int> Nutrients = new Dictionary<string, int>();
        if (food.Nutrients != "")
        {
            Nutrients = food.Nutrients.Split(',')
                .Select(value => value.Split(':'))
                .ToDictionary(p => p[0], p => Int32.Parse(p[1]));
        }

        return new FoodSummary(food.Id, food.Description, food.Name, food.FoodSeason, food.FoodEnergyWithFibre, food.FoodEnergyWithoutFibre, food.ServingSize, Nutrients);
    }

    public static Models.Responses.Food ToResponseModel(this Food food)
    {
        var tags = food.Tags.GroupBy(p => p.TagCategory);
        var tagDictionary = new Dictionary<string, IEnumerable<string>>();

        foreach (var grouping in tags)
        {
            tagDictionary.Add(grouping.Key.Name, grouping.Select(p => p.Name).ToList());
        }

        Dictionary<string, int> Nutrients = new Dictionary<string, int>();
        if (food.Nutrients != "")
        {
            Nutrients = food.Nutrients.Split(',')
                .Select(value => value.Split(':'))
                .ToDictionary(p => p[0], p => Int32.Parse(p[1]));
        }

        return new Models.Responses.Food(
            food.Id,
            food.Description,
            food.Name,
            food.FoodSeason,
            food.FoodEnergyWithFibre,
            food.FoodEnergyWithoutFibre,
            food.ServingSize,
            Nutrients,
            tagDictionary);
    }

    public static PaginatedResponse<TResponse> ToResponseModel<TResponse, TDbModel>(
        this PaginatedResult<TDbModel> result, Func<TDbModel, TResponse> map)
        where TResponse : class where TDbModel : class
    {
        return new PaginatedResponse<TResponse>(result.Total, result.Count, result.Results.Select(map));
    }

    public static TagCategory ToResponseModel(this database.Models.TagCategory tagCategory)
    {
        return new TagCategory(tagCategory.Id, tagCategory.Name);
    }

    public static Models.Responses.User ToResponseModel(this User user)
    {
        return new Models.Responses.User(
        user.Id,
        user.FirstName,
        user.LastName,
        user.Email,
        user.Username,
        user.DateCreated,
        user.Status,
        user.Role
        );
    }
    public static Models.Responses.Allergy ToResponseModel(this foodremedy.database.Models.Allergy allergy)
    {
        return new Models.Responses.Allergy(allergy.Id, allergy.Name, allergy.Description);
    }

    public static ProfileResponse ToResponseModel(this Profile profile)
    {
        return new ProfileResponse(
            profile.Id,
            profile.UserId,
            profile.FirstName,
            profile.LastName,
            profile.Status,
            profile.Relationship,
            profile.Age,
            profile.AvatarUrl,
            profile.Allergies,
            profile.Intolerances,
            profile.DietaryForm
        );
    }
}
