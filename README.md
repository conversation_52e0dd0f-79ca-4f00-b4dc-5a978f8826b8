# 🥗 Food Remedy API

Food Remedy API is a mobile-first platform designed to help Australian consumers make smarter, health-conscious food choices. Users will be able to scan supermarket products to access nutritional information, allergen warnings, and dietary suitability — all from a personalised mobile experience.  

See our [Project Overview Here](./Documents/project-overview.md)  

<br />

> 💡 This README helps contributors set up the project from scratch, including backend setup, mobile app launch using Expo Go, and local development using Docker.  

> ⚠️ **Heads up**: Backend services are still under development. The mobile app works independently for now.

<br/>


## 📚 Table of Contents
- [📁 Project Structure](#-project-structure)
- [⚙️ Prerequisites](#️-prerequisites)
- [📦 Initialise Repository](#-initialise-repository)
- [🛠️ Set Up Back End](#️-set-up-back-end)
  - [⚡ Option 1: Quick Start](#-option-1-quick-start)
  - [🧰 Option 2: Manual Step-by-Step Setup](#-option-2-manual-step-by-step-setup)
- [🖥️ Run The Backend](#️-run-the-backend)
- [📱 Run the Mobile App](#-run-the-mobile-app)
- [🔑 Credentials](#-credentials)
- [🚀 What's Next?](#-whats-next)


<br />


## 📁 Project Structure
The following outlines the major folder structure of the project. 

```bash
foodremedy-main/
  ├── .venv/              # Python virtual environment
  ├── api/                # Python API for food and nutrition data
  ├── database/           # SQL schema, seed data, JSON files
  ├── Documents/          # Instuctions and guides
  ├── mobile-app/         # React Native app using Expo Go
  ├── README.md
  ├── run.py              # Interactive CLI for backend setup
  └── docker-compose.yml
```


<br/>


## ⚙️ Prerequisites
Install the following tools before starting:

- Git ([Install](https://git-scm.com/))
- Node.js ([Install](https://nodejs.org/en/download))
- Python 3 ([Install](https://www.python.org/downloads/))
- Docker Desktop ([Install](https://www.docker.com/products/docker-desktop))
- (Optional) Expo Go app on your iOS/Android device ([Install](https://expo.dev/go))
- Expo CLI → `npm install -g expo-cli`


<br/>


## 📦 Initialise Repository
### Step 1: Clone the Repository
1. Open your terminal or command prompt.  
2. Navigate to the folder where you want to store the project.  
3. Run the following command:  
   ```bash
   git clone https://github.com/Gopher-Industries/FoodRemedyAPI-Main
   ```

### Step 2: Create a New Branch
1. Navigate inside the repository folder.
   ```bash
   cd FoodRemedyAPI-Main
   ```

2. Now that you’re inside the project folder, create a new branch for your work.   
   This helps keep your changes separate from the main version of the code.  
   ```bash
   git checkout -b your-branch-name
   ```  
   - Replace "your-branch-name" with ticket id and title, such as `FE067-Fix-Filter-Functionality`.
   - When you finish your work later, you’ll push your branch to GitHub and create a pull request.
   - Don’t change the main branch directly.
> See more on  [🧑‍💻 How To Contribute Here](./Documents/Guides/how-to-contribute.md)


<br/>


## 🛠️ Set Up Back End
Set up the database and backend services using the built-in CLI tool.  
Make sure you're terminal is inside the project root folder.  

>💡 Ensure Python and Docker are installed and working.  
> You can test them by running `python --version` and `docker --version`.  

1. Open Docker 
2. Run this command in the terminal:
   ```bash
   python run.py
   # or
   python3 run.py
   ```
3. This will open an interactive menu  
   You’ll use this menu throughout the setup process.  
   It handles running the backend, setting up the database, checking if Docker is working, and more.  

    ```bash
    Console Application Menu:

    Application Options:
    1. Run .NET Application with dotnet watch (assuming database container 'db' is already running)
    2. Trust Dev Certification to the HTTPS development certificate

    Docker Options:
    3. Run Docker container 'db' in daemon mode
    4. Stop Docker container 'db'
    5. Check Docker Container Status
    6. Remove Docker container 'db'
    7. List Docker Containers, Images, and Volumes
    15. Remove a Docker image
    16. Remove a Docker volume

    Database Options:
    8. Print tables in the 'foodremedy' database
    9. Print table data in the 'foodremedy' database
    10. Add data to the 'foodremedy' database
    11. Update current tables structure in database
    12. Remove migrations folder
    13. Create new migration

    Automation Options:
    17. Automate Fix Previous Builds (Clean Docker/Migrations, Setup DB, Run App)

    Exit Options:
    14. Exit
    ```

### ⚡ Option 1: Quick Start
If you just need the backend running with minimal setup:
1. In the CLI menu, choose:
   ```bash
   17. Automate Fix Previous Builds (Clean Docker/Migrations, Setup DB, Run App)
   ```

This will: 
- Start Docker and the database container
- Set up migrations and tables
- Populate the database with sample data
- Launch the backend API

> ⚠️ Use this only for quick setup.
> If something fails, or you’re making backend changes, follow the manual steps below.

<br/>

### 🧰 Option 2: Manual Step-by-Step Setup
Follow these steps if you need more control or are troubleshooting errors.

#### Start the database
1. In the CLI menu, run:
   ```bash 
   3. Run Docker container 'db' in daemon mode
   ```

2. Confirm in Docker Desktop that a container named db-1 is running under the foodremedy-main  
   
   Expected output:
   ```bash
   ✔ Network "foodremedy-main_default" Created  
   ✔ Volume "foodremedy-main_my-db" Created  
   ✔ Container "foodremedy-main-db-1" Started
   ```
    
   See below for reference:  
   ![FoodRemedyMain Runnning with DB in Docker Desktop](Documents/Images/ProjectReadMe/setup-docker-running.png)

3. (*Optional*) **Run option 5** to double-check the container status.  


#### Migrate and Populate the Database
In the CLI menu, run these in order:
1. Create the migration for database tables
   ```bash 
   13. Create new migration
   ```
2. Apply migrations and create necessary tables
   ```bash 
   11. Update current tables structure in database
   ```
3. Populate database with sample data
   ```bash 
   10. Add data to the 'foodremedy' database
   ```

Optional Checks
1. List the tables in the database
   ```bash 
   8. Print tables in the 'foodremedy' database
   ```
2. Preview table data
   ```bash 
   9. Print table data in the 'foodremedy' database
   ```

> If you have authentication issues, run **option 9**  
> Choose the `User` table and confirm that a user called `dev` exists.   
> ⚠️ If there is no user in the `User` table, you won’t be able to log in.    


<br/>


## 🖥️ Run The Backend
Once the backend has been set up (database and migrations done), follow these steps to start it.

### Trust the HTTPS Development Certificate (First Time Only)
1. In the CLI menu, select:
   ```bash 
   2. Trust Dev Certification to the HTTPS development certificate
   ```
   This allows your browser to trust the local HTTPS connection.  
   You may see a pop-up or need to enter your system password to approve the certificate.  
   You only need to do this once per machine.

### Start the Backend API
1. In the CLI menu, select:
   ```bash 
   1. Run .NET Application with dotnet watch (assuming database container 'db' is already running)
   ```
   The backend API will start with hot reload.  
   Any code changes will trigger an automatic rebuild and restart.

### Verify its Running
After a few moments, a browser tab will open automatically with the ASP.NET App Host Dashboard.
See below for reference:    
  ![App Host Image](Documents/Images/ProjectReadMe/setup-apphost-dashboard.png)

To view the **backend API (Swagger docs)**  
Visit either of the below:    
  - `https://localhost:7001/swagger/index.html`
  - `https://localhost:5001/swagger/index.html`  


<br/>  


## 📱 Run the Mobile App
The mobile app runs independently from the backend and uses JavaScript dependencies, so it must be started separately.
> For specific details, see  
> [Mobile App ReadMe](./mobile-app/README.md)


1. Navigate to the mobile-app folder
    ```bash
    cd mobile-app
    ```
2. Install Dependencies (First Time Only)
    ```bash
    npm install
    ```
3. Start the App
    ```bash
    npm start 
    ```
    This launches the Metro bundler and shows a QR code in your terminal (similar to the example below).

### Run the App on Your Device or Simulator
> ⚠️ Make sure your phone is on the same Wi-Fi network if scanning QR  

Choose one of the following:
- 📱 Physical Device:
  - Scan the QR code with the Expo Go app (Android)
  - Or use your iPhone Camera app (iOS)
- 💻 Simulators:
    - Press `i` to open in iOS Simulator (Mac only)
    - Press `a` to open in Android Emulator (if available)



### Example Terminal Output
```bash
▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
█ ▄▄▄▄▄ █▄▄▄▀▀▀█▀ ▀ ▄▄▄▄▄ █
█ █   █ ██▄▀▀█▀▀▄▄█ █   █ █
█ █▄▄▄█ ██▀▄ ▄▄██▀█ █▄▄▄█ █
█▄▄▄▄▄▄▄█ ▀▄█ ▀▄▀ █▄▄▄▄▄▄▄█
█▄ █▄▄█▄██▄▀█▄▀█▀ █▄█▀█▀▀▄█
██ █▀██▄▄▄▄██▄▄▄▄▀▀███▄▀▀ █
█▀▄▄ █▄▄▄▄▄ █▀█▄ █ ▄▀▀█▀ ██
█ ▄█▀ █▄ █▀▄█▀▄▀▀▀▀▀██▄▀▀ █
█▄██▄▄█▄█ ▄  ▄▄ █ ▄▄▄  ▄▀▄█
█ ▄▄▄▄▄ █▀▀▀▀▄  █ █▄█ ███ █
█ █   █ █ ▀█▄ ▀█▄ ▄  ▄ █▀▀█
█ █▄▄▄█ █▀▄▀▀▀▀▄ ▄█▀▀▄█   █
█▄▄▄▄▄▄▄█▄██▄██▄▄▄▄█▄▄███▄█

› Metro waiting on exp://192.165.0.146:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)

› Using Expo Go
› Press s │ switch to development build

› Press a │ open Android
› Press w │ open web

Logs for your project will appear below. Press Ctrl+C to exit.
```


<br/>


## 🔑 Credentials
You are now fully set up and ready to contribute to the project.  
Use the following credentials to log into the site and API for testing:  
> 💡 Developer Login:  
> **Username:** *dev*  
> **Password:** *12345*  


<br/>  


## 🚀 What’s Next?
You’ve finished setting up the project and can now start contributing.  
Whether you’re fixing bugs, building features or just exploring, here are your next steps:
- Learn how to contribute and create pull requests:  
   [🧑‍💻 How To Contribute Here](./Documents/Guides/how-to-contribute.md)  
- Working on a codefree ticket? Learn how to contribute:  
   [🧑‍💻 Completing Codefree Tickets](./Documents/Guides/completing-codefree-tickets.md)
- Need help or ran into errors?  
   [🧑‍💻 Troubleshoot Setup](./Documents/Guides/troubleshoot-setup.md)  
