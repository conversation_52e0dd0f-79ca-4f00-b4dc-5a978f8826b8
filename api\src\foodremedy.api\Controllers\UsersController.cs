using foodremedy.api.Configuration;
using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using foodremedy.api.Utils;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using System.Net;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("[controller]")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status200OK)]
[ProducesResponseType(StatusCodes.Status400BadRequest)]
[ProducesResponseType(StatusCodes.Status409Conflict)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class UsersController : ControllerBase
{
    private readonly IUserRepository _userRepository;
    private readonly IDistributedCache _distributedCache;
    private readonly IConfigurationRepository _configurationRepository;

    public UsersController(IUserRepository userRepository, IDistributedCache distributedCache, IConfigurationRepository configurationRepository)
    {
        _userRepository = userRepository;
        _distributedCache = distributedCache;
        _configurationRepository = configurationRepository;
    }

    /// <summary>
    /// Creates a new user
    /// </summary>
    /// <param name="registerUser">User details to be added</param>
    /// <returns> Successfully created user</returns>
    /// <response code="200">User created</response>
    /// <response code="400">The createUser is null</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="409">A user already exists with this email or username</response>
    [HttpPost("register")]
    [AllowAnonymous] //allowed anonymous for testing purposes
public async Task<IActionResult> RegisterUser([FromBody] RegisterUser registerUser)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(new
            {
                message = "Invalid user registration data.",
                errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)
            });
        }

        try
        {
            // Check for existing user
            var existingUser = await _userRepository.GetByEmailAsync(registerUser.Email)
                                ?? await _userRepository.GetByUsernameAsync(registerUser.Username);
            if (existingUser != null)
            {
                return Conflict(new { message = "A user with this email or username already exists." });
            }

            // Secure password
            string salt = StringHasher.GenerateSalt();
            string hashedPassword = StringHasher.Hash(registerUser.Password, salt);

            var dbUser = new foodremedy.database.Models.User(
                firstName: registerUser.FirstName,
                lastName: registerUser.LastName,
                email: registerUser.Email,
                username: registerUser.Username,
                passwordHash: hashedPassword,
                passwordSalt: salt,
                dateCreated: registerUser.DateCreated,
                age: registerUser.Age,
                role: registerUser.Role ?? "User"
            );

            _userRepository.Add(dbUser);
            await _userRepository.SaveChangesAsync();

            return Ok(dbUser.ToResponseModel());
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }



    /// <summary>
    /// Gets a list of users
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <param name="sortby">The property to sort by</param>
    /// <param name="status">Status to filter by</param>
    /// <returns> List of users</returns>
    /// <response code="201">Returns the list of Users</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet]
    public async Task<ActionResult<Models.Responses.PaginatedResponse<database.Models.User>>> GetUsers([FromQuery] PaginationRequest paginationRequest, [FromQuery] string? status = null, [FromQuery] string? sortby = null)
    {
        var results = await _userRepository.GetAsync(paginationRequest.Skip, paginationRequest.Take, status, sortby);

        return Ok(results.ToResponseModel(p => p.ToResponseModel()));
    }

    /// <summary>
    /// Gets user by ID
    /// </summary>
    /// <param name="userId">ID of the user to search for</param>
    /// <returns> User associated with the ID</returns>
    /// <response code="200">Returns the User with the userId</response>
    /// <response code="404">User does not exist</response>   
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{userId:guid}")]
    public async Task<ActionResult<Models.Responses.User>> GetUser([FromRoute] Guid userId)
    {
        var result = await _userRepository.GetByIdAsync(userId);

        if(result == null)
            return NotFound();

        return Ok(result.ToResponseModel());
    }

    /// <summary>
    /// Updates an exisiting user
    /// </summary>
    /// <param name="userId">ID of the user to update</param>
    /// <param name="updatedUser">The updated user details</param>
    /// <returns>Successfully updated user</returns>
    /// <response code="200">User updated</response>
    /// <response code="409">Password not changed</response>
    /// <response code="409">User already exists with this username</response>
    /// <response code="401">User is not authenticated as the user being updated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPut("{userId:guid}")]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<Models.Responses.User>> UpdateUser([FromRoute] Guid userId, [FromBody] RegisterUser updatedUser)
    {
        string sessionUsername = await _distributedCache.GetStringAsync(UserSessionKeyEnum.SessionKeyUsername.ToString()) ?? "";

        database.Models.User? dbUser = await _userRepository.GetByIdAsync(userId);

        if (dbUser == null)
            return NotFound();

        if (!sessionUsername.Equals(dbUser.Username, StringComparison.OrdinalIgnoreCase))
        {
            return Unauthorized(new ProblemDetails
            {
                Title = "Unauthorized",
                Status = (int)HttpStatusCode.Unauthorized,
                Detail = "You are not authorized to perform this action"
            });
        }
        else
        {
            if (updatedUser.Email != dbUser.Email)
            {
                dbUser.Email = updatedUser.Email;
            }

            if (updatedUser.Username != dbUser.Username)
            {
                // Ensure the new username is unique
                if (await _userRepository.UsernameExistsAsync(updatedUser.Username))
                {
                    return Conflict(new ProblemDetails
                    {
                        Title = "Conflict",
                        Status = (int)HttpStatusCode.Conflict,
                        Detail = "Username already exists."
                    });
                }
                dbUser.Username = updatedUser.Username;
            }

            if (!StringHasher.VerifyPassword(updatedUser.Password, dbUser.PasswordHash, dbUser.PasswordSalt))
            {
                string newSalt = StringHasher.GenerateSalt();
                string newHash = StringHasher.Hash(updatedUser.Password, newSalt);

                dbUser.PasswordSalt = newSalt;
                dbUser.PasswordHash = newHash;
            }
            else
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Bad Request",
                    Status = (int)HttpStatusCode.BadRequest,
                    Detail = "Your new password must be different from your previous password."
                });
            }

            await _userRepository.SaveChangesAsync();
            await _distributedCache.SetStringAsync(UserSessionKeyEnum.SessionKeyUsername.ToString(), dbUser.Username, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60)
            });
            return Ok(dbUser.ToResponseModel());
        }
    }

    /// <summary>
    /// Enables/disables an existing user
    /// </summary>
    /// <param name="userId">ID of the user</param>
    /// <param name="status">Desired status of the user (Disable/Activate)</param>
    /// <response code="204">User enabled/disabled</response>
    /// <response code="401">If the user is not authenticated</response>
    /// <response code="404"> If the user does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPatch("{status}/{userId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Models.Responses.User>> DeleteUser([FromRoute] Guid userId, [FromRoute] string status)
    {
        database.Models.User? dbUser = await _userRepository.GetByIdAsync(userId);

        if (dbUser != null)
        {
            switch(status){
                case "Disable":{
                    dbUser.Status = false;
                    await _userRepository.SaveChangesAsync();
                    return NoContent();
                }
                case "Activate":{
                    dbUser.Status = true;
                    await _userRepository.SaveChangesAsync();
                    return NoContent();
                }
            }      
        }
        return NotFound();
    }

    /// <summary>
    /// Activates a user account.
    /// </summary>
    [Authorize(Policy = "UserOnly")]
    [HttpPost("{userId:guid}/activate")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ActivateUser([FromRoute] Guid userId)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
            return NotFound();

        if (user.Status)
            return BadRequest(new { message = "Account is already active." });

        user.Status = true;
        await _userRepository.SaveChangesAsync();
        return NoContent();
    }

    /// <summary>
    /// Deactivates a user account.
    /// </summary>
    [Authorize(Policy = "UserOnly")]
    [HttpPost("{userId:guid}/deactivate")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeactivateUser([FromRoute] Guid userId)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
            return NotFound();

        if (!user.Status)
            return BadRequest(new { message = "Account is already deactivated." });

        user.Status = false;
        await _userRepository.SaveChangesAsync();
        return NoContent();
    }
}
 
