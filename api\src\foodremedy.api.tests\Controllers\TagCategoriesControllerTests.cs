﻿using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using TagCategory = foodremedy.database.Models.TagCategory;
using foodremedy.api.tests.Factories;

namespace foodremedy.api.tests.Controllers;

    /// <summary>
    /// Contains the test cases for the TagCategoriesController.
    /// Tests include unauthenticated, authenticated by API key, successful creation, and valid retrieval of tag categories.
    /// </summary>
internal sealed class TagCategoriesControllerTests : ControllerTestFixture
{
    /// <summary>
    /// Gets the factory used to create tag category objects.
    /// </summary>
    public override IFactory Factory => tagCategoryFactory;

    /// <summary>
    /// Test cases for unauthenticated requests, expecting Unauthorized status.
    /// </summary>
    public new static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "tags/categories",
            Method= HttpMethod.Get
            }).SetName("GetCategories_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "tags/categories",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateTagCategory_UnauthenticatedRequest_ReturnsUnauthorized"),
    };

    /// <summary>
    /// Test cases for authenticated requests using an API key, expecting Unauthorized status.
    /// </summary>
    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "tags/categories",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateTagCategory_AuthenticatedByApiKey_ReturnsUnauthorized"),
    };

    /// <summary>
    /// Test cases for successfully creating a tag category, expecting Created status.
    /// </summary>
    public new static List<TestCaseData> CreatedTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "tags/categories",
            Method= HttpMethod.Post,
            Request_With_Properties = new{
                    Name = "Name"
                },
            Check_Id_Exists = true,
            Check_Result_Properties = new{
                    Name = "Name"
                }
            }).SetName("CreateTagCategory_ValidRequest_ReturnsCreated"),
    };

    /// <summary>
    /// Test cases for retrieving tag categories, expecting Ok status.
    /// </summary>
    public new static List<TestCaseData> OkTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "tags/categories",
            Method= HttpMethod.Get,
            Add_Before_With_Properties = new[] {
                new{
                    Name = "name1",
                    },
                new{
                    Name = "name2",
                    },
                },
            Check_Id_Exists = true,
            Expect_Result_As_List = true,
            Check_Result_Properties = new[] {
                new{
                    Name = "name1",
                    },
                new{
                    Name = "name2",
                    },
                }
            }).SetName("GetCategories_ValidRequest_ReturnsOk"),
    };

}
