{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=foodremedy;User=root;Password=password;SslMode=none;", "RedisConnection": "localhost:6379"}, "AuthenticationConfiguration": {"SigningKey": "ThisIsATest1ngKey-DoN0tDeployMe!", "Audience": "FoodRemedy-API", "Issuer": "FoodRemedy-API", "TokenTimeToLive": 3600}, "AllowedHosts": "*"}