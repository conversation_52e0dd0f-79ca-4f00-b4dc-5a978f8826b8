﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using foodremedy.api.Utils;

namespace foodremedy.api.Extensions;

public static class SwaggerGenExtensions
{
    public static void AddApiKeySupport(this SwaggerGenOptions setup)
    {
        setup.AddSecurityDefinition(ApiKeyAuthenticationOptions.DefaultScheme, new OpenApiSecurityScheme
        {
            In = ParameterLocation.Header,
            Name = ApiKeyAuthenticationOptions.HeaderName,
            Type = SecuritySchemeType.ApiKey
        });

        setup.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = ApiKeyAuthenticationOptions.DefaultScheme
                    }
                },
                Array.Empty<string>()
            }
        });
    }
}