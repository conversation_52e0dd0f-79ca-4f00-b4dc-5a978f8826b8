(function () {
    function hideSchemasSectionForClient() {

        // Log the current URL
        console.log("Current URL:", window.location.href);

        // Get the current URL's query parameter for primaryName
        const urlParams = new URLSearchParams(window.location.search);
        const primaryName = urlParams.get("urls.primaryName");

        // Log the value of primaryName to see if it matches
        console.log("Primary Name:", primaryName);

        // Select the section containing the schemas (identified by its class)
        const schemaSection = document.querySelector('section.block.col-12.block-desktop.col-12-desktop .models.is-open');
        //const schemaSection = document.querySelector('section.block.col-12.block-desktop.col-12-desktop');

        // Log the schemaSection to ensure it's found
        console.log("Schema Section:", schemaSection);

        // Hide the schemas section only if the Client API page is active
        if (schemaSection && primaryName === "Food Remedy API Client") {
            console.log("Hiding schema section");
            schemaSection.style.display = 'none';
        } else {
            console.log("Not hiding schema section. Either not found or wrong primaryName.");
        }

    }

     // Function to handle URL change
     function handleUrlChange() {
        console.log("URL changed, checking again...");
        hideSchemasSectionForClient(); // Re-run the function when the URL changes
    }

    // Wait until the DOM is fully loaded, then run the hide function
    document.addEventListener('DOMContentLoaded', function () {
        setTimeout(hideSchemasSectionForClient, 1000); // Delay execution to ensure the DOM is fully loaded
    });

     // Automatically check for URL changes without page refresh
     window.addEventListener('popstate', function () {
        console.log('Popstate event triggered'); // Log when popstate is triggered
        handleUrlChange();
    });

     //If you use a hash-based routing system (like some single-page apps), you can use:
    window.addEventListener('hashchange', function () {
        console.log('Hashchange event triggered'); // Log when hashchange is triggered
        handleUrlChange();
    }); 

     // Monitor changes in the location.href
     let lastUrl = location.href;
     setInterval(function () {
         if (location.href !== lastUrl) {
             console.log("URL changed: ", location.href);
             lastUrl = location.href;
             handleUrlChange(); // Trigger the change handler when the URL changes
         }
     }, 300); // Check every 300ms


})();
