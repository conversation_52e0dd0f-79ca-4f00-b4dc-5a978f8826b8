using System.ComponentModel.DataAnnotations;

namespace foodremedy.api.Models.Requests;

public record ProfileRequest(
    [Required(AllowEmptyStrings = false)] Guid UserId,
    [Required(AllowEmptyStrings = false)][StringLength(50)] string FirstName,
    [Required(AllowEmptyStrings = false)][StringLength(50)] string LastName,
    bool Status,
    [StringLength(50)] string? Relationship,
    [Range(0, 150)] int? Age,
    [StringLength(255)] string? AvatarUrl,
    List<string>? Allergies,
    List<string>? Intolerances,
    [StringLength(100)] string? DietaryForm
) : IRequestModel;
