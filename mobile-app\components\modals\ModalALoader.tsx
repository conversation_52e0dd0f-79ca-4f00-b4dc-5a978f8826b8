// Modal Loader

import { View } from "react-native";
import ModalWrapper from "./ModalAWrapper";
import ModalChooseMemberRelationship from "./ModalChooseMemberRelationship";


const ModalLoader = () => {

  return (
    <View>

      <ModalWrapper modalKey="chooseMemberRelationship">
        <ModalChooseMemberRelationship />
      </ModalWrapper>


      {/* EXAMPLES */}
      {/* <ModalWrapper modalKey="responseDiscardWorkout">
        <ModalResponse
          modalKey="responseDiscardWorkout"
          isInput={false}
          message="Discard Current Workout?" acceptLabel="Discard"
          onAccept={() => { startSession(); stopTimer(true); }} />
      </ModalWrapper >

      <ModalWrapper modalKey="exportWorkoutData">
        <ModalResponse
          modalKey="exportWorkoutData"
          isInput={false}
          message="Export Your Data?" acceptLabel="Export"
          onAccept={exportWorkoutData}
        />
      </ModalWrapper> */}


    </View >
  );
}

export default ModalLoader;