body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  font-size: 16px;
  line-height: 1.5;

}

#root {
  overflow-x: hidden; /* Prevent horizontal scrollbar */
}

/* Custom topbar style */
div.topbar {
  background-color: #34a65f !important;
}

/* Logo replacement for Food Remedy */
div.topbar img {
  content: url('/images/Logo.png'); /* Path to Food Remedy logo */
  width: auto;
  height: 40px; /* Adjust as needed */
}

/* Header styles with added text shadow */
h2,
h3 {
  color: #f5624d !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* Adds a subtle black shadow */
}

/* OpBlock summary method colors - swapped POST and GET */
.opblock-get {
  background: rgba(73, 204, 144, .1) !important;
  border-color: #49cc90 !important;
}
.opblock-summary-get {
  border-color: #49cc90 !important;
}
.opblock-summary-get > button > span.opblock-summary-method {
  background-color: #0f8a5f !important; /* GET is now green */
}

.opblock-post {
  background: rgba(97, 175, 254, .1) !important;
  border-color: #61affe !important;
}
.opblock-summary-post {
  border-color: #61affe !important;
}
.opblock-summary-post > button > span.opblock-summary-method {
  background-color: #235e6f !important; /* POST is now blue */
}

.opblock-summary-delete > button > span.opblock-summary-method {
  background-color: #cc231e !important;
}
