// Product Provider

import React, { createContext, useState, useContext, ReactNode, Dispatch, SetStateAction, useEffect } from 'react';

interface ProductContextType {
  barcode: string | null;
  setBarcode: Dispatch<SetStateAction<string | null>>;

  currentProduct: Product | null;
  setCurrentProduct: Dispatch<SetStateAction<Product | null>>;
  fetchProductByBarcode: (barcode: string) => Promise<void>;
  clearProduct: () => void;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const ProductProvider = ({ children }: { children: ReactNode }) => {
  const [barcode, setBarcode] = useState<string | null>(null);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const clearProduct = () => setCurrentProduct(null);

  useEffect(() => {
    if (!barcode) return;

    console.log("Fetching Product with Barcode: ", barcode);
    fetchProductByBarcode(barcode);
  }, [barcode])

  /**
   * Fetch Product By Barcode
   * @param barcode 
   */
  const fetchProductByBarcode = async (barcode: string) => {
    try {
      // TODO: Add API call to backend

      // const resp = await fetch(`/api/products/${encodeURIComponent(barcode)}`, {
      //   method: 'GET',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   }
      // });

      // if (!resp.ok) {
      //   throw new Error(`Server returned ${resp.status}`);
      // }

      // const data: Product = await resp.json();
      // setCurrentProduct(data);
    } catch (err) {
      console.error('fetchProductByBarcode error:', err);
      // TODO: Show notification
    }
  };

  return (
    <ProductContext.Provider value={{
      barcode, setBarcode,
      currentProduct, setCurrentProduct,
      fetchProductByBarcode,
      clearProduct
    }}>
      {children}
    </ProductContext.Provider>
  );
};

export const useProduct = (): ProductContextType => {
  const ctx = useContext(ProductContext);
  if (!ctx) {
    throw new Error('useProduct must be used within a ProductProvider');
  }
  return ctx;
};


/**
 * DELETE TEMP PRODUCT TYPE
 */
type Product = {
  additives_tags?: string[];
  allergens?: string;
  allergens_tags?: string[];
  brands?: string;
  categories_tags?: string[];
  code: string;
  generic_name?: string;
  id?: string;
  ingredients_analysis_tags?: string[];
  ingredients_from_palm_oil_n?: number;
  ingredients_tags?: string[];
  ingredients_text?: string;
  labels_tags?: string[];
  nutriments: Record<string, any>;
  nutriscore_grade?: string;
  product_name?: string;
  product_quantity?: number | string;
  product_quantity_unit?: string;
  quantity?: string;
  serving_quantity?: number | string;
  serving_quantity_unit?: string;
  serving_size?: string;
  traces?: string;
  traces_from_ingredients?: string;
};
