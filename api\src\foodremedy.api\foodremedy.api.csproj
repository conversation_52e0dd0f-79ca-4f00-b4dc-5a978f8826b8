<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <GenerateDocumentationFile>true</GenerateDocumentationFile> 
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <NoWarn>NU1603;CS1574;1591</NoWarn> 
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FastMember" Version="1.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.4.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.2" />
    <PackageReference Include="MySql.Data" Version="9.2.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="9.0.0-preview.2" />
    <PackageReference Include="OpenTelemetry" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.1" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.LocalDevelopment.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.Production.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\foodremedy.database\foodremedy.database.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="foodremedy.api.tests" />
  </ItemGroup>

</Project>
