// Login Page tsx

import { useState } from "react";
import { View, Pressable, ActivityIndicator, KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import { Link, useRouter } from "expo-router";
import Input from "@/components/ui/UIInput";
import IconGeneral from "@/components/icons/IconGeneral";
import Tt from "@/components/ui/UIText";
import { Image } from "react-native";

export default function LoginPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState<boolean>(true);
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  /**
   * Sign In
   * @returns 
   */
  const signIn = async () => {
    setErrorMessage("");
    const validEmail = email.trim().toLowerCase();
    const validPassword = password.trim();

    if (!validEmail || !validPassword) {
      setErrorMessage("Enter a valid Email and Password");
      return;
    }

    setLoading(true);
    try {
      /**
       * TODO:
       * Add sign in functionality using backend api
       */

      router.replace("/(app)/(tabs)");
    } catch (error: any) {
      setErrorMessage(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <ScrollView
        className="flex-1 p-safe"
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
      >
        <View className="justify-center flex-1 w-[90%] self-center">
          {/* Brand Image */}
          <View className="items-center">
            <Image
              source={require("../assets/images/FoodRemedyLogo.png")}
              className="w-[50%] aspect-[3/1] max-w-[300px] h-auto"
              resizeMode="contain"
            />
          </View>

          {!loading && (
            <>
              {errorMessage && (
                <View className="bg-[#FCCACA] border border-primary rounded-md px-4 py-2 mt-8">
                  <Tt className="text-center text-primary font-interSemiBold">{errorMessage}</Tt>
                </View>
              )}

              {/* Email Input */}
              <Input
                className="py-3 mt-8 "
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect={false}
              />

              {/* Password Input */}
              <View className="relative mt-4">
                <Input
                  className="py-3"
                  value={password}
                  onChangeText={setPassword}
                  placeholder="Password"
                  secureTextEntry={showPassword}
                  autoCapitalize="none"
                  autoComplete="off"
                  autoCorrect={false}
                />

                <Pressable onPress={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 -translate-y-1/2"
                  style={({ pressed }) => [{ borderColor: pressed ? '#FF3EB5' : 'hsl(0 0% 13%)' }]}
                  hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}>
                  {({ pressed }) => (
                    <IconGeneral
                      type={showPassword ? 'visibility' : 'visibility-off'}
                      fill={pressed ? '#FF3F3F' : "hsl(0 0% 70%)}"}
                    />
                  )}
                </Pressable>
              </View>

              {/* Login Button */}
              <Pressable onPress={signIn}
                className="bg-primary rounded-lg py-3 mt-4 border border-primary active:bg-transparent"
              >
                {({ pressed }) => (
                  <Tt className={`text-center text-2xl font-interSemiBold 
              ${pressed ? 'text-primary' : 'text-white'}`}>Login</Tt>
                )}
              </Pressable>


              <Tt className="text-sm mt-12 text-center">Don't have an account? <Link href='/register'
                className="text-primary font-interSemiBold active:underline">Create Account</Link>
              </Tt>

              <Tt className="text-sm mt-8 text-center">Forgot Password? <Link href='/forgotPassword'
                className="text-primary font-interSemiBold active:underline">Reset</Link>
              </Tt>
            </>
          )}

          {loading && (
            <View className="mt-40">
              <ActivityIndicator size="large" color="#FF3F3F" />
            </View>
          )}

        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
