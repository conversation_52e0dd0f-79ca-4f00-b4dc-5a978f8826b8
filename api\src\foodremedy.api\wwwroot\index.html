<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoodRemedy API - Documentation</title>
    <link rel="stylesheet" href="/css/styles.css">
</head>
<body>
    <header>
        <h1>FoodRemedy API - Documentation</h1>
        <p>Your guide to understanding the structure and components of the FoodRemedy API project.</p>
    </header>
    <h1 class="api-testing"> Link to api testing -> <a href="http://localhost:5001/swagger/index.html">Swagger</a></h1>
    <section>
        <h2>Project Structure Overview</h2>
        <p>The root directory of the project is <strong>foodremedy-main</strong>, which contains the following key components:</p>
        <ul class="folder-list">
            <li><strong>api/</strong>
                <ul>
                    <li>The main directory for the API project. It contains the source code and related files.</li>
                </ul>
            </li>
            <li><strong>src/</strong>
                <ul>
                    <li>The source directory where all the source code for the FoodRemedy API is located.</li>
                </ul>
            </li>
            <li><strong>foodremedy.api/</strong>
                <ul>
                    <li>The core directory of the API containing various subfolders and files:</li>
                    <li><strong>bin/</strong> - Compiled binaries and executables.</li>
                    <li><strong>Configuration/</strong> - Contains configuration-related classes and settings.</li>
                    <li><strong>Controllers/</strong> - Contains the controller classes, which handle HTTP requests and responses and mainly you have to perform modi on this.</li>
                    <li><strong>Extensions/</strong> - Holds extension methods that add functionality to existing types.</li>
                    <li><strong>Models/</strong> - Includes the model classes for food api that define the structure of the data used within the application.</li>
                    <li><strong>obj/</strong> - Intermediate object files generated during the build process.</li>
                    <li><strong>Properties/</strong> - Contains project-specific properties and settings.</li>
                    <li><strong>Providers/</strong> - Holds classes that provide data or services to the application, such as dependency injection services.</li>
                    <li><strong>Repositories/</strong> - Contains repository classes that handle data access logic, typically interacting with a database.</li>
                    <li><strong>Resources/</strong> - Used for resource files, such as images, text files, etc., that are needed by the application.</li>
                    <li><strong>Services/</strong> - Contains service classes that encapsulatelogic and operations of APi key and Cache.</li>
                    <li><strong>Utils/</strong> - Holds utility classes and helper methods that provide common functionality across the application.</li>
                    <li><strong>wwwroot/</strong> - Contains static files like HTML, CSS, images, etc.</li>
                  <li><strong>Program.cs</strong> - The entry point of the application, where the application's configuration, middleware, and services are set up. </li>
                </ul>
            </li>
            <li><strong>foodremedy.api.tests/</strong>
                <ul>
                    <li>This directory contains the test cases for the FoodRemedy API, ensuring that the functionality works as expected. It is organized into the following subdirectories:</li>
                    <li><strong>bin/</strong> - Compiled binaries and executables specific to the test cases.</li>
                    <li><strong>Controllers/</strong> - Contains test cases that specifically target the controller classes, ensuring that HTTP request handling is correctly implemented.</li>
                    <li><strong>Factories/</strong> - Contains factory classes used in test cases to create instances of models or services with predefined states for testing purposes.</li>
                    <li><strong>Utils/</strong> - Holds utility classes and helper methods that provide common functionality in tests.</li>
                    <li><strong>obj/</strong> - Intermediate object files generated during the build process of the test project.</li>
                    <li><strong>foodremedy.api.tests.csproj</strong> - The project file for the test suite, containing metadata about the test project, dependencies, and build settings.</li>
                    <li><strong>Usings.cs</strong> - A file that typically contains global using directives to be shared across multiple test files, simplifying the import of namespaces.</li>
                </ul>
            </li>
            <li><strong>foodremedy.database/</strong>
                <ul>
                    <li>This directory contains scripts and files related to the database, such as migration scripts and seed data. It is organized into the following subdirectories:</li>
                    <li><strong>bin/</strong> - Compiled binaries and executables related to database operations.</li>
                    <li><strong>Extensions/</strong> - Contains extension methods that add functionality to the database-related classes or contexts.</li>
                    <li><strong>Models/</strong> - Includes the model classes that define the structure of the data used within the database context.</li>
                    <li><strong>obj/</strong> - Intermediate object files generated during the build process of the database project.</li>
                    <li><strong>foodremedy.database.csproj</strong> - The project file for the database project, containing metadata about the project, dependencies, and build settings.</li>
                    <li><strong>FoodRemedyDbContext.cs</strong> - The main database context class, which manages the connection to the database and the interaction with data models.</li>
                    <li><strong>Usings.cs</strong> - A file that typically contains global using directives for the database project, simplifying the import of namespaces.</li>
                </ul>
            </li>
 
        </ul>
    </section>
    
</body>
</html>
