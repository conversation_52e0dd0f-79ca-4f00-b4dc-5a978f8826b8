﻿using foodremedy.database;
using foodremedy.database.Models;
using Microsoft.EntityFrameworkCore;

namespace foodremedy.api.Repositories;

public interface ITagRepository
{
    Task<PaginatedResult<Tag>> GetAsync(int skip = 0, int take = 20);
    Tag? GetByNameAsync(string tagName);
    Tag Update(Tag tag);
    Tag Add(Tag tag);
    void Remove(Tag tag);
    Task<Tag?> GetByIdAsync(Guid id);
    Task SaveChangesAsync();
}

public class TagRepository : ITagRepository
{
    private readonly FoodRemedyDbContext _dbContext;

    public TagRepository(FoodRemedyDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<PaginatedResult<Tag>> GetAsync(int skip = 0, int take = 20)
    {
        var results = await _dbContext
            .Tag
            .Include(p => p.TagCategory)
            .Skip(skip)
            .Take(take)
            .ToListAsync();

        return new PaginatedResult<Tag>(results.Count, _dbContext.Tag.Count(), results);
    }

    public Tag? GetByNameAsync(string name)
    {
        return _dbContext.Tag.Where(p => p.Name == name).SingleOrDefault();
    }

    public async Task<Tag?> GetByIdAsync(Guid id)
    {
        return await _dbContext.Tag.SingleOrDefaultAsync(p => p.Id == id);
    }

    public Tag Add(Tag tag)
    {
        return _dbContext.Add(tag).Entity;
    }

    public void Remove(Tag tag)
    {
        _dbContext.Tag.Remove(tag);
    }
    
    public Tag Update(Tag tag)
    {
        return _dbContext.Tag.Update(tag).Entity;
    }

    public async Task SaveChangesAsync()
    {
        await _dbContext.SaveChangesAsync();
    }
}
