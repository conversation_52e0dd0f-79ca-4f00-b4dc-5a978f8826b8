using System.Net;
using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Nutrient = foodremedy.api.Models.Responses.Nutrient;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("[controller]")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class NutrientsController : ControllerBase
{
    private readonly INutrientRepository _nutrientRepository;

    public NutrientsController(INutrientRepository nutrientRepository)
    {
        _nutrientRepository = nutrientRepository;
    }

    /// <summary>
    /// Gets the list of nutrients
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <param name="sortBy">The property to sort the result by</param>
    /// <returns> Gets a list of nutrients</returns>
    /// <response code="200">The list of Nutrients was found and returned</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedResponse<Nutrient>>> GetNutrients([FromQuery] PaginationRequest paginationRequest, [FromQuery] string? sortBy)
    {
        PaginatedResult<database.Models.Nutrient> results = await _nutrientRepository.GetAsync(paginationRequest.Skip, paginationRequest.Take);

        
        var items = results.Results;

        
        if (!string.IsNullOrEmpty(sortBy))
        {
            items = sortBy.ToLower() switch
            {
                "name" => items.OrderBy(n => n.Name).ToList(),
                "description" => items.OrderBy(n => n.Description).ToList(),
                _ => items 
            };
        }

       
        var paginatedResponse = new PaginatedResponse<Nutrient>(
            results.Total,
            items.Count,
            items.Select(n => n.ToResponseModel()) 
        );

        return Ok(paginatedResponse);
    }

    /// <summary>
    /// Gets a nutrient using it's ID
    /// </summary>
    /// <param name="nutrientId">The ID of the nutrient</param>
    /// <returns> Success message</returns>
    /// <response code="200">Nutrient with that ID was found and returned</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Nutrient does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{nutrientId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Nutrient>> GetNutrient([FromRoute] Guid nutrientId)
    {
        database.Models.Nutrient? result = await _nutrientRepository.GetByIdAsync(nutrientId);

        if (result == null)
            return NotFound();

        return Ok(result.ToResponseModel());
    }

    /// <summary>
    /// Gets a nutrient by name
    /// </summary>
    /// <param name="name">The name of the nutrient</param>
    /// <returns> The nutrient associated with the name</returns>
    /// <response code="200">Nutrient with that name was found and returned</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Nutrient does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("byname/{name}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Nutrient>> GetNutrientByName([FromRoute] string name)
    {
        database.Models.Nutrient? result = await _nutrientRepository.GetByNameAsync(name);

        if (result == null)
            return NotFound();

        return Ok(result.ToResponseModel());
    }

    /// <summary>
    /// Creates a new nutrient
    /// </summary>
    /// <param name="createNutrient">The nutrient to be added</param>
    /// <returns> Successfully created nutrient</returns>
    /// <response code="201">Returns created message</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="400">The createNutrient is null</response>
    /// <response code="409">Nutrient already has that name</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Nutrient>> CreateNutrient([FromBody] CreateNutrient createNutrient)
    {
        database.Models.Nutrient? existingNutrient = await _nutrientRepository.GetByNameAsync(createNutrient.Name);

        if (existingNutrient != null) return Conflict();

        database.Models.Nutrient Nutrient = _nutrientRepository.Add(createNutrient.ToDbModel());
        await _nutrientRepository.SaveChangesAsync();

        return Created($"/nutrients/{Nutrient.ToResponseModel().Id}", Nutrient.ToResponseModel());
    }

    /// <summary>
    /// Updates an existing nutrient
    /// </summary>
    /// <param name="nutrientId">The ID of the nutrient</param>
    /// <param name="updateNutrient">The updated nutrient details</param>
    /// <returns> The successfully updated nutrient</returns>
    /// <response code="201">Nutrient is updated</response>
    /// <response code="404">No nutrient by this name exists</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="400">A nutrient by that name already exists or no changes have been made</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPut("{nutrientId:guid}")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Nutrient>> UpdateNutrient([FromRoute] Guid nutrientId, [FromBody] CreateNutrient updateNutrient)
    {
        database.Models.Nutrient? result;

        database.Models.Nutrient? dbNutrient = await _nutrientRepository.GetByIdAsync(nutrientId);

        // Check if the Nutrient already exist in the database
        if (dbNutrient == null) return NotFound();

        // Check if the Nutrient with the updateNutrient name already exists in the database
        database.Models.Nutrient? existingNutrient = await _nutrientRepository.GetByNameAsync(updateNutrient.Name);
        if (existingNutrient != null && existingNutrient.Id != dbNutrient.Id)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = "Nutrient with this name already exists"
            });
        }

        // Check if update is necessary
        if (dbNutrient.Name == updateNutrient.Name && dbNutrient.Description == updateNutrient.Description)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.Conflict,
                Detail = "Nutrient data is already up to date"
            });
        }

        // Update the Nutrient
        dbNutrient.Name = updateNutrient.Name;
        dbNutrient.Description = updateNutrient.Description;

        result = _nutrientRepository.Update(dbNutrient);
        await _nutrientRepository.SaveChangesAsync();

        return Created($"/nutrients/{result.ToResponseModel().Id}", result.ToResponseModel());
    }

    /// <summary>
    /// Deletes an exisiting nutrient
    /// </summary>
    /// <param name="nutrientId">The ID of the nutrient</param>
    /// <response code="204">Nutrient successfully deleted</response>
    /// <response code="404">Nutrient does not exist</response>
    /// <response code="400">The nutrientId is null</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpDelete("{nutrientId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]    
    public async Task<ActionResult<Nutrient>> DeleteNutrient([FromRoute] Guid nutrientId){
        
        database.Models.Nutrient? dbNutrient = await _nutrientRepository.GetByIdAsync(nutrientId);

        if (dbNutrient != null)
        {
            _nutrientRepository.Remove(dbNutrient);
            await _nutrientRepository.SaveChangesAsync();
            return NoContent();
        }

        return NotFound();
    }
}
