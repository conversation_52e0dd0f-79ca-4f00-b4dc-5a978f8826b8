﻿Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "foodremedy.api", "src\foodremedy.api\foodremedy.api.csproj", "{7E17D027-3D59-441B-AA76-D3F4A404743F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "foodremedy.api.tests", "src\foodremedy.api.tests\foodremedy.api.tests.csproj", "{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "foodremedy.database", "src\foodremedy.database\foodremedy.database.csproj", "{4149A0DC-3F5B-476A-8E87-1144C1495688}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppHost", "AppHost\AppHost.csproj", "{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServiceDefaults", "ServiceDefaults\ServiceDefaults.csproj", "{057C18AC-051D-475A-861A-2FEB21B3FD6B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Debug|x64.Build.0 = Debug|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Debug|x86.Build.0 = Debug|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Release|x64.ActiveCfg = Release|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Release|x64.Build.0 = Release|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Release|x86.ActiveCfg = Release|Any CPU
		{7E17D027-3D59-441B-AA76-D3F4A404743F}.Release|x86.Build.0 = Release|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Debug|x64.Build.0 = Debug|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Debug|x86.Build.0 = Debug|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Release|x64.ActiveCfg = Release|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Release|x64.Build.0 = Release|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Release|x86.ActiveCfg = Release|Any CPU
		{F59EFFE8-2BA6-4504-B4C6-E2E1B23C190A}.Release|x86.Build.0 = Release|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Debug|x64.Build.0 = Debug|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Debug|x86.Build.0 = Debug|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Release|Any CPU.Build.0 = Release|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Release|x64.ActiveCfg = Release|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Release|x64.Build.0 = Release|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Release|x86.ActiveCfg = Release|Any CPU
		{4149A0DC-3F5B-476A-8E87-1144C1495688}.Release|x86.Build.0 = Release|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Debug|x64.Build.0 = Debug|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Debug|x86.Build.0 = Debug|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Release|x64.ActiveCfg = Release|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Release|x64.Build.0 = Release|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Release|x86.ActiveCfg = Release|Any CPU
		{B4D0CBEC-ED0B-4E7D-85DF-0F37699BA27D}.Release|x86.Build.0 = Release|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Debug|x64.Build.0 = Debug|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Debug|x86.Build.0 = Debug|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Release|x64.ActiveCfg = Release|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Release|x64.Build.0 = Release|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Release|x86.ActiveCfg = Release|Any CPU
		{057C18AC-051D-475A-861A-2FEB21B3FD6B}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal