using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace foodremedy.database.Models
{
    public class Profile : IDatabaseModel
    {
        [Key]
        [Column(TypeName = "CHAR(36)")]
        public Guid Id { get; set; }

        [Required]
        [Column(TypeName = "CHAR(36)")]
        public Guid UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        public bool Status { get; set; } = true;

        [StringLength(50)]
        public string? Relationship { get; set; }

        public int? Age { get; set; }

        [StringLength(255)]
        public string? AvatarUrl { get; set; }

        public List<string>? Allergies { get; set; }

        public List<string>? Intolerances { get; set; }

        [StringLength(100)]
        public string? DietaryForm { get; set; }

        // Navigation property
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }
    }
}