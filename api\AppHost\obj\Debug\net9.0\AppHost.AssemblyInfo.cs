//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("5a6eb385-162e-41eb-9633-2a4c9d6b3600")]
[assembly: System.Reflection.AssemblyMetadata("dcpclipath", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.1.0" +
    "\\tools\\dcp.exe"))]
[assembly: System.Reflection.AssemblyMetadata("dcpextensionpaths", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.1.0" +
    "\\tools\\ext\\"))]
[assembly: System.Reflection.AssemblyMetadata("dcpbinpath", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.1.0" +
    "\\tools\\ext\\bin\\"))]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectpath", "C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectname", "AppHost.csproj")]
[assembly: System.Reflection.AssemblyMetadata("aspiredashboardpath", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\9.1.0\\tools\\A" +
    "spire.Dashboard.exe"))]
[assembly: System.Reflection.AssemblyMetadataAttribute("apphostprojectbaseintermediateoutputpath", "C:\\Users\\<USER>\\Desktop\\test\\api\\AppHost\\obj\\")]
[assembly: System.Reflection.AssemblyCompanyAttribute("AppHost")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+84ff70c18b05d57ef6eca0cce15303a06cf4e9f9")]
[assembly: System.Reflection.AssemblyProductAttribute("AppHost")]
[assembly: System.Reflection.AssemblyTitleAttribute("AppHost")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// 由 MSBuild WriteCodeFragment 类生成。

