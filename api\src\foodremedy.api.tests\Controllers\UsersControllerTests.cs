using System.Net;
using System.Net.Http.Json;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Utils;
using Microsoft.AspNetCore.Http;
using foodremedy.api.tests.Factories;
using User = foodremedy.database.Models.User;

namespace foodremedy.api.tests.Controllers;

/// <summary>
/// Contains tests for the UsersController, covering various scenarios related to user management, including authentication, authorization, and CRUD operations.
/// </summary>
internal sealed class UsersControllerTests : ControllerTestFixture
{
    /// <summary>
    /// Factory to create test data for users.
    /// </summary>
    public override IFactory Factory => userFactory;
    
    /// <summary>
    /// List of unauthenticated test cases, verifying that unauthenticated users are denied access to user-related endpoints.
    /// </summary>
    public new static List<TestCaseData> UnauthenticatedTests = new List<TestCaseData>
    {   
        new TestCaseData(new {
            Path=  "users/",
            Method= HttpMethod.Get,
            }).SetName("GetUsers_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetUser_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/Disable/{Guid.NewGuid()}",
            Method= HttpMethod.Patch
            }).SetName("DisableUser_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/Activate/{Guid.NewGuid()}",
            Method= HttpMethod.Patch
            }).SetName("ActivateUser_UnauthenticatedRequest_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateUser_UnauthenticatedRequest_ReturnsUnauthorized")
    };

    /// <summary>
    /// List of authenticated by API key test cases, verifying that authenticated users with an API key are properly authorized to access user-related endpoints.
    /// </summary>
    public new static List<TestCaseData> AuthenticatedByApiKeyTests = new List<TestCaseData>
    {   
        new TestCaseData(new {
            Path=  "users/",
            Method= HttpMethod.Get,
            }).SetName("GetUsers_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetUser_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/Disable/{Guid.NewGuid()}",
            Method= HttpMethod.Patch
            }).SetName("DisableUser_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/Activate/{Guid.NewGuid()}",
            Method= HttpMethod.Patch
            }).SetName("ActivateUser_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  $"users/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateUser_AuthenticatedByApiKey_ReturnsUnauthorized"),

        new TestCaseData(new {
            Path=  "users/register",
            Method= HttpMethod.Post,
            Has_Body= true
            }).SetName("CreateUser_AuthenticatedByApiKey_ReturnsUnauthorized")
    };

    /// <summary>
    /// List of test cases that check for scenarios where the user is not found, ensuring that proper HTTP 404 responses are returned.
    /// </summary>
    public new static List<TestCaseData> NotFoundTests = new List<TestCaseData>
    {   
        new TestCaseData(new {
            Path=  $"users/{Guid.NewGuid()}",
            Method= HttpMethod.Get
            }).SetName("GetUser_UserDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"users/Disable/{Guid.NewGuid()}",
            Method= HttpMethod.Patch
            }).SetName("DisableUser_UserDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"users/Activate/{Guid.NewGuid()}",
            Method= HttpMethod.Patch
            }).SetName("ActivateUser_UserDoesNotExist_ReturnsNotFound"),

        new TestCaseData(new {
            Path=  $"users/{Guid.NewGuid()}",
            Method= HttpMethod.Put,
            Has_Body= true
            }).SetName("UpdateUser_UserDoesNotExist_ReturnsNotFound")
    };

    /// <summary>
    /// List of test cases for scenarios where the response status is HTTP 204 No Content, verifying the success of update or disable actions.
    /// </summary>
    public new static List<TestCaseData> NoContentTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "users/Disable", 
            Use_Real_Id_In_Path = true,
            Method= HttpMethod.Patch
            }).SetName("DisableUser_ValidRequest_ReturnsNoContent"),

        new TestCaseData(new {
            Path= "users/Activate",
            Use_Real_Id_In_Path = true,
            Method= HttpMethod.Patch
            }).SetName("ActivateUser_ValidRequest_ReturnsNoContent")
    };

    /// <summary>
    /// List of test cases for invalid input that should result in an HTTP 400 Bad Request response.
    /// </summary>
    public new static List<TestCaseData> BadRequestTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "users",
            Use_Real_Id_In_Path = true,
            Request_With_Properties = new {
                    Email = "testEmail"
                },
            Method= HttpMethod.Put
            }).SetName("UpdateUser_UsingInvalidEmail_ReturnsBadRequest"),

        new TestCaseData(new {
            Path= "users",
            Use_Real_Id_In_Path = true,
            Request_With_Properties = new {
                    Email = ""
                },
            Method= HttpMethod.Put
            }).SetName("UpdateUser_UsingEmptyEmail_ReturnsBadRequest"),
    };

    /// <summary>
    /// List of test cases for scenarios where duplicate email or username results in an HTTP 409 Conflict response.
    /// </summary>
    public new static List<TestCaseData> ConflictTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path= "users/register",
            Add_Before_With_Properties = new{
                    Email = "Existing@email"
                },
            Request_With_Properties = new{
                    Email = "Existing@email"
                },
            Method= HttpMethod.Post
            }).SetName("CreateUser_DuplicateEmail_ReturnsConflict"),

        new TestCaseData(new {
            Path= "users/register",
            Add_Before_With_Properties = new{
                    Username = "name"
                },
            Request_With_Properties = new{
                    Username = "name"
                },
            Method= HttpMethod.Post
            }).SetName("CreateUser_DuplicateUsername_ReturnsConflict"),
    };

    /// <summary>
    /// List of test cases for valid user creation and retrieval, ensuring that proper HTTP 200 responses are returned when users are created or retrieved.
    /// </summary>
    public new static List<TestCaseData> OkTests = new List<TestCaseData>
    {
        new TestCaseData(new {
            Path=  "users/register",
            Method= HttpMethod.Post,
            Request_With_Properties = new{
                    Email = "Existing@email",
                    Username = "name"
                },
            }).SetName("CreateUser_ValidRequest_ReturnsOk"),

        new TestCaseData(new {
            Path=  "users",
            Method= HttpMethod.Get,
            Use_Real_Id_In_Path = true,
            Add_Before_With_Properties = new{
                    Email = "Existing@email"
                },
            Check_Result_Properties = new{
                    Email = "Existing@email"
                },
            }).SetName("GetUser_ById_ReturnsOk"),
    };

    /// <summary>
    /// Test that verifies that a valid request for retrieving users returns an HTTP 200 status and includes the expected payload.
    /// </summary>
    [Test]
    public async Task GetUsers_ValidRequest_ReturnsOkWithPayload()
    {
        var user1 = (database.Models.User) await userFactory.Add();
        var user2 = (database.Models.User) await userFactory.Add();

        var request = new HttpRequestMessage(HttpMethod.Get, "/users");
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);
        var result = await response.Content.ReadFromJsonAsync<PaginatedResponse<Models.Responses.User>>();

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result!.Total.Should().BeGreaterOrEqualTo(2);
        result.Count.Should().BeGreaterThanOrEqualTo(2);
        result.Results.Should()
            .Contain(p => p.Email == user1.Email && p.Id == user1.Id).
            And.Contain(p => p.Email == user2.Email && p.Id == user2.Id);
    }

    /// <summary>
    /// Tests updating a user with the same password, which should result in a BadRequest response.
    /// </summary>
    [Test]
    public async Task UpdateUser_UsingSamePassword_RespondsBadRequest()
    {
        var dbUser = (database.Models.User) await userFactory.Add(new {
                Password = "password"
            });

        RegisterUser updatedUser = (RegisterUser) userFactory.Create(new {
                Password = "password"
            });

        var request = new HttpRequestMessage(HttpMethod.Put, $"users/{dbUser.Id}")
        {
            Content = JsonContent.Create(updatedUser)
        };
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request, dbUser.Email, "password");

        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    /// <summary>
    /// Tests attempting to update another user's record, which should result in an Unauthorized response.
    /// </summary>
    [Test]
    public async Task UpdateUser_AttemptToUpdateAnotherUserRecord_RespondsUnauthorized()
    {
        var dbUser = (database.Models.User) await userFactory.Add();

        RegisterUser updatedUser = (RegisterUser) userFactory.Create();

        var request = new HttpRequestMessage(HttpMethod.Put, $"users/{dbUser.Id}")
        {
            Content = JsonContent.Create(updatedUser)
        };
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Tests updating a user with valid data, which should return a successful OK response.
    /// </summary>
    [Test]
    public async Task UpdateUser_ValidRequest_ReturnsOk()
    {
        var dbUser = (database.Models.User) await userFactory.Add(new {
                Password = "password"
            });

        RegisterUser updatedUser = (RegisterUser) userFactory.Create(new {
                Password = "newpassword"
            });

        var request = new HttpRequestMessage(HttpMethod.Put, $"users/{dbUser.Id}")
        {
            Content = JsonContent.Create(updatedUser)
        };
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request, dbUser.Email, "password");

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<Models.Responses.User>();

        result.Should().NotBeNull();
        result!.Email.Should().Be(updatedUser.Email);
        result.Id.Should().Be(dbUser.Id);
    }

    /// <summary>
    /// Tests updating the user's email twice with different values, which should return OK for both updates.
    /// </summary>
    [Test]
    public async Task UpdateUser_UpdatingEmailSecondTime_ReturnsOk()
    {
        var dbUser = (database.Models.User) await userFactory.Add(new {
                Password = "password1"
            });
        RegisterUser firstUpdatedUser = (RegisterUser) userFactory.Create(new {
                Password = "password2"
            });
        RegisterUser secondUpdatedUser = (RegisterUser) userFactory.Create(new {
                Password = "password3"
            });

        var request = new HttpRequestMessage(HttpMethod.Put, $"users/{dbUser.Id}")
        {
            Content = JsonContent.Create(firstUpdatedUser)
        };
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request, dbUser.Email, "password1");
        var result = await response.Content.ReadFromJsonAsync<Models.Responses.User>();

        var secondRequest = new HttpRequestMessage(HttpMethod.Put, $"users/{dbUser.Id}")
        {
            Content = JsonContent.Create(secondUpdatedUser)
        };
        HttpResponseMessage secondResponse = await SendAuthenticatedRequestAsync(secondRequest, result!.Email, "password2");

        HttpResponseMessage finalResponse = (response.StatusCode == HttpStatusCode.OK && secondResponse.StatusCode == HttpStatusCode.OK) ?
            new HttpResponseMessage(HttpStatusCode.OK) : 
            new HttpResponseMessage(HttpStatusCode.ExpectationFailed);

        finalResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var secondResult = await secondResponse.Content.ReadFromJsonAsync<Models.Responses.User>();

        result.Should().NotBeNull();
        result!.Email.Should().Be(firstUpdatedUser.Email);
        result.Id.Should().Be(dbUser.Id);

        secondResult.Should().NotBeNull();
        secondResult!.Email.Should().Be(secondUpdatedUser.Email);
        secondResult.Id.Should().Be(dbUser.Id);
    }

    /// <summary>
    /// Tests updating a user's username, which should return a successful OK response with the new username.
    /// </summary>
    [Test]
    public async Task UpdateUser_UsingNewUsername_ReturnsOk()
    {
        var dbUser = (database.Models.User) await userFactory.Add(new { Username = "OldUsername", Password = "password" });

        var updatedUser = (RegisterUser) userFactory.Create(new { Username = "NewUsername" });

        var request = new HttpRequestMessage(HttpMethod.Put, $"users/{dbUser.Id}")
        {
            Content = JsonContent.Create(updatedUser)
        };
        HttpResponseMessage response = await SendAuthenticatedRequestAsync(request, dbUser.Email, "password");

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<Models.Responses.User>();

        result.Should().NotBeNull();
        result!.Username.Should().Be("NewUsername");
        result.Id.Should().Be(dbUser.Id);
    }
}
