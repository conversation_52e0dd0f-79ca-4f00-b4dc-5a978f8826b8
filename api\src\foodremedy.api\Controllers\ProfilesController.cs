using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("[controller]")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status200OK)]
[ProducesResponseType(StatusCodes.Status400BadRequest)]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status404NotFound)]
[ProducesResponseType(StatusCodes.Status409Conflict)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class ProfilesController : ControllerBase
{
    private readonly IProfileRepository _profileRepository;
    private readonly IUserRepository _userRepository;
    private readonly ILogger<ProfilesController> _logger;

    public ProfilesController(IProfileRepository profileRepository, IUserRepository userRepository, ILogger<ProfilesController> logger)
    {
        _profileRepository = profileRepository;
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Creates a new profile
    /// </summary>
    /// <param name="createProfileRequest">Profile details to be added</param>
    /// <returns>Successfully created profile</returns>
    /// <response code="201">Profile created</response>
    /// <response code="400">The request is invalid</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">User not found</response>
    /// <response code="409">A profile already exists for this user</response>
    /// <response code="500">Internal server error</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost]
    public async Task<IActionResult> CreateProfile([FromBody] ProfileRequest createProfileRequest)
    {
        try
        {
            // Basic validation
            if (createProfileRequest.UserId == Guid.Empty)
            {
                return BadRequest(new { message = "UserId cannot be empty." });
            }

            if (string.IsNullOrWhiteSpace(createProfileRequest.FirstName))
            {
                return BadRequest(new { message = "FirstName is required." });
            }

            if (string.IsNullOrWhiteSpace(createProfileRequest.LastName))
            {
                return BadRequest(new { message = "LastName is required." });
            }

            // Validate that the user exists
            var user = await _userRepository.GetByIdAsync(createProfileRequest.UserId);
            if (user == null)
            {
                return NotFound(new { message = "User not found." });
            }

            // Check if a profile already exists for this user (if you want to prevent duplicates)
            var existingProfile = await _profileRepository.GetByUserIdAsync(createProfileRequest.UserId);
            if (existingProfile != null)
            {
                return Conflict(new { message = "A profile already exists for this user." });
            }

            var dbProfile = createProfileRequest.ToDbModel();
            
            // Log the profile data before saving
            Console.WriteLine($"Creating profile: UserId={dbProfile.UserId}, FirstName={dbProfile.FirstName}, LastName={dbProfile.LastName}");
            Console.WriteLine($"Allergies: {(dbProfile.Allergies != null ? string.Join(", ", dbProfile.Allergies) : "null")}");
            Console.WriteLine($"Intolerances: {(dbProfile.Intolerances != null ? string.Join(", ", dbProfile.Intolerances) : "null")}");
            
            var createdProfile = _profileRepository.Add(dbProfile);
            await _profileRepository.SaveChangesAsync();

            return CreatedAtAction(
                nameof(GetProfile), 
                new { profileId = createdProfile.Id }, 
                createdProfile.ToResponseModel()
            );
        }
        catch (Exception ex)
        {
            // Log the full error for debugging (in a real app, use proper logging)
            Console.WriteLine($"Profile creation error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            
            // Get inner exception details
            var innerEx = ex.InnerException;
            var innerMessage = "";
            while (innerEx != null)
            {
                innerMessage += $" Inner: {innerEx.Message}";
                Console.WriteLine($"Inner exception: {innerEx.Message}");
                innerEx = innerEx.InnerException;
            }
            
            return StatusCode(500, new { 
                message = "An error occurred while creating the profile.", 
                error = ex.Message,
                innerError = innerMessage,
                details = ex.ToString() // Full exception details for debugging
            });
        }
    }

    /// <summary>
    /// Gets a profile by ID
    /// </summary>
    /// <param name="profileId">ID of the profile to retrieve</param>
    /// <returns>Profile associated with the ID</returns>
    /// <response code="200">Returns the profile</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Profile not found</response>
    /// <response code="500">Internal server error</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{profileId:guid}")]
    public async Task<ActionResult<ProfileResponse>> GetProfile([FromRoute] Guid profileId)
    {
        try
        {
            var profile = await _profileRepository.GetByIdAsync(profileId);
            if (profile == null)
            {
                return NotFound(new { message = "Profile not found." });
            }

            return Ok(profile.ToResponseModel());
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the profile." });
        }
    }

    /// <summary>
    /// Gets a paginated list of profiles
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <param name="sortby">The property to sort by</param>
    /// <returns>List of profiles</returns>
    /// <response code="200">Returns the list of profiles</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="500">Internal server error</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet]
    public async Task<ActionResult<PaginatedResponse<ProfileResponse>>> GetProfiles(
        [FromQuery] PaginationRequest paginationRequest, 
        [FromQuery] string? sortby = null)
    {
        try
        {
            var results = await _profileRepository.GetAsync(
                paginationRequest.Skip, 
                paginationRequest.Take, 
                sortby
            );

            return Ok(results.ToResponseModel(p => p.ToResponseModel()));
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the profiles." });
        }
    }

    /// <summary>
    /// Updates a profile
    /// </summary>
    /// <param name="profileId">ID of the profile to update</param>
    /// <param name="updateProfileRequest">Profile details to update</param>
    /// <returns>Updated profile</returns>
    /// <response code="200">Profile updated successfully</response>
    /// <response code="400">The request is invalid</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Profile not found</response>
    /// <response code="500">Internal server error</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPut("{profileId:guid}")]
    public async Task<ActionResult<ProfileResponse>> UpdateProfile(
        [FromRoute] Guid profileId, 
        [FromBody] ProfileRequest updateProfileRequest)
    {
        try
        {
            var profile = await _profileRepository.GetByIdAsync(profileId);
            if (profile == null)
            {
                return NotFound(new { message = "Profile not found." });
            }

            profile.UpdateFromRequest(updateProfileRequest);
            _profileRepository.Update(profile);
            await _profileRepository.SaveChangesAsync();

            return Ok(profile.ToResponseModel());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating profile {ProfileId}", profileId);
            
            var innerExceptionMessage = ex.InnerException?.Message ?? "";
            _logger.LogError("Inner exception: {InnerException}", innerExceptionMessage);
            
            return StatusCode(500, new { 
                message = "An error occurred while updating the profile.",
                details = $"Error: {ex.Message}. Inner: {innerExceptionMessage}"
            });
        }
    }

    /// <summary>
    /// Deletes a profile
    /// </summary>
    /// <param name="profileId">ID of the profile to delete</param>
    /// <returns>Success message</returns>
    /// <response code="200">Profile deleted successfully</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">Profile not found</response>
    /// <response code="500">Internal server error</response>
    [Authorize(Policy = "UserOnly")]
    [HttpDelete("{profileId:guid}")]
    public async Task<IActionResult> DeleteProfile([FromRoute] Guid profileId)
    {
        try
        {
            var profile = await _profileRepository.GetByIdAsync(profileId);
            if (profile == null)
            {
                return NotFound(new { message = "Profile not found." });
            }

            _profileRepository.Remove(profile);
            await _profileRepository.SaveChangesAsync();

            return Ok(new { message = "Profile deleted successfully." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting profile {ProfileId}", profileId);
            
            var innerExceptionMessage = ex.InnerException?.Message ?? "";
            _logger.LogError("Inner exception: {InnerException}", innerExceptionMessage);
            
            return StatusCode(500, new { 
                message = "An error occurred while deleting the profile.",
                details = $"Error: {ex.Message}. Inner: {innerExceptionMessage}"
            });
        }
    }
}
