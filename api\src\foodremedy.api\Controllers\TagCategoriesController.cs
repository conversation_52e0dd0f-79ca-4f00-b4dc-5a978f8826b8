﻿using System.Net;
using foodremedy.api.Extensions;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.api.Repositories;
using foodremedy.database.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TagCategory = foodremedy.api.Models.Responses.TagCategory;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("tags/categories")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class TagCategoriesController : ControllerBase
{
    private readonly ITagCategoryRepository _tagCategoryRepository;

    public TagCategoriesController(ITagCategoryRepository tagCategoryRepository)
    {
        _tagCategoryRepository = tagCategoryRepository;
    }

    /// <summary>
    /// Gets a list of tag categories
    /// </summary>
    /// <param name="paginationRequest">Used to divide results into pages</param>
    /// <returns> List of tag categories</returns>
    /// <response code="200">Returns the list of Categories</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedResponse<TagCategory>>> GetCategories([FromQuery] PaginationRequest paginationRequest)
    {
        PaginatedResult<database.Models.TagCategory> results = await _tagCategoryRepository.GetAsync(paginationRequest.Skip, paginationRequest.Take);

        return Ok(results.ToResponseModel(p => p.ToResponseModel()));
    }

    /// <summary>
    /// Gets a category by ID
    /// </summary>
    /// <param name="categoryId">The ID of the category</param>
    /// <returns> Category associated with inputted ID</returns>
    /// <response code="200">Returns the category</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="404">category does not exist</response>
    [Authorize(Policy = "UserOnly")]
    [HttpGet("{categoryId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TagCategory>> GetCategory([FromRoute] Guid categoryId)
    {
        database.Models.TagCategory? result = await _tagCategoryRepository.GetByIdAsync(categoryId);

        if (result == null)
            return NotFound();
        
        return Ok(result.ToResponseModel());
    }

    /// <summary>
    /// Creates a new tag category
    /// </summary>
    /// <param name="createTagCategory">Tag Category to be added</param>
    /// <returns> Successfullt created tag category</returns>
    /// <response code="201">Returns created message</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="400">The createTagCategory is null</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TagCategory>> CreateTagCategory(CreateTagCategory createTagCategory)
    {
        database.Models.TagCategory tagCategory = _tagCategoryRepository.Add(createTagCategory.ToDbModel());
        await _tagCategoryRepository.SaveChangesAsync();

        return Created($"tags/categories/{tagCategory.ToResponseModel().Id}", tagCategory.ToResponseModel());
    }

    /// <summary>
    /// Updates an existing tag category
    /// </summary>
    /// <param name="id">The ID of the tag category</param>
    /// <param name="updateTagCategory">The updated tag category details</param>
    /// <returns> The successfully updated tag category</returns>
    /// <response code="201">Tag category is updated</response>
    /// <response code="404">No tag category by this ID exists</response>
    /// <response code="401">User is not authenticated</response>
    /// <response code="400">A tag category by that name already exists or no changes have been made</response>
    [Authorize(Policy = "UserOnly")]
    [HttpPut("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TagCategory>> UpdateTagCategory([FromRoute] Guid id, [FromBody] UpdateTagCategory updateTagCategory)
    {

        database.Models.TagCategory? dbCategory = await _tagCategoryRepository.GetByIdAsync(id);

        // Check if the Tag Category already exists in the database
        if (dbCategory == null) return NotFound();

        // Check if a Tag Category with the updated name already exists
        database.Models.TagCategory? existingCategory = await _tagCategoryRepository.GetByName(updateTagCategory.Name);
        if (existingCategory != null && existingCategory.Id != dbCategory.Id)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = "Tag category with this name already exists"
            });
        }

        // Check if update is necessary
        if (dbCategory.Name == updateTagCategory.Name)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Status = (int)HttpStatusCode.Conflict,
                Detail = "Tag category data is already up to date"
            });
        }

        // Update the Tag Category
        dbCategory.Name = updateTagCategory.Name;

        database.Models.TagCategory result = _tagCategoryRepository.Update(dbCategory);
        await _tagCategoryRepository.SaveChangesAsync();

        return Created($"/tags/categories/{result.ToResponseModel().Id}", result.ToResponseModel());
    }
    /// <summary>
    /// Deletes an existing tag category
    /// </summary>
    /// <param name="id">The ID of the tag category</param>
    /// <response code="204">Tag category successfully deleted</response>
    /// <response code="404">Tag category does not exist</response>
    /// <response code="400">The id is null</response>
    /// <response code="401">User is not authenticated</response>
    [Authorize(Policy = "UserOnly")]
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteTagCategory([FromRoute] Guid id)
    {
        // Fetch the Tag Category by ID
        database.Models.TagCategory? dbCategory = await _tagCategoryRepository.GetByIdAsync(id);

        if (dbCategory != null)
        {
            // Remove the Tag Category
            _tagCategoryRepository.Remove(dbCategory);
            await _tagCategoryRepository.SaveChangesAsync();
            return NoContent(); // Successfully deleted
        }

        // If not found, return 404
        return NotFound();
    }


}
