﻿using Microsoft.Extensions.Caching.Memory;
using foodremedy.api.Models.Responses;

namespace foodremedy.api.Services;

/// <summary>
/// Service used to resolve and invalidate API keys.
/// </summary>
public interface IApiKeysCacheService
{
    /// <summary>
    /// Gets a API Key Owner ID (owner of the API key) from its API Key.
    /// </summary>
    /// <param name="apiKey">The API Key received on the HTTP request.</param>
    /// <returns>The ID of the owner of the API Key if the key was found, null otherwise.</returns>
    ValueTask<string?> GetOwnerIdFromApiKey(string apiKey);

    /// <summary>
    /// Invalidates (removes from cache and/or permanent storage) an API key.
    /// </summary>
    /// <param name="apiKey">The API Key to invalidate</param>
    /// <returns>A task representing the operation.</returns>
    Task InvalidateApiKey(database.Models.ApiKey apiKey);
}

public class CacheService : IApiKeysCacheService
{
    private static readonly TimeSpan _cacheKeysTimeToLive = new(1, 0, 0);

    private readonly IMemoryCache _memoryCache;
    private readonly IApiKeyService _apiKeysService;

    public CacheService(IMemoryCache memoryCache, IApiKeyService apiKeysService)
    {
        _memoryCache = memoryCache;
        _apiKeysService = apiKeysService;
    }

    public async ValueTask<string?> GetOwnerIdFromApiKey(string apiKey)
    {
        if (!_memoryCache.TryGetValue<Dictionary<string, Guid>>("Authentication_ApiKeys", out var internalKeys))
        {
            internalKeys = await _apiKeysService.GetActiveApiKeys();

            _memoryCache.Set("Authentication_ApiKeys", internalKeys, _cacheKeysTimeToLive);
        }

        if (!internalKeys!.TryGetValue(apiKey, out var userId))
        {
            return null;
        }

        return userId.ToString();
    }

    public async Task InvalidateApiKey(database.Models.ApiKey apiKey)
    {
        if (_memoryCache.TryGetValue<Dictionary<string, Guid>>("Authentication_ApiKeys", out var internalKeys))
        {
            internalKeys!.Remove(apiKey.Token);
            _memoryCache.Set("Authentication_ApiKeys", internalKeys);
        }

        await _apiKeysService.InvalidateApiKey(apiKey);
    }
}