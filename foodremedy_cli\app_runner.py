import subprocess
import os
import time
import sys
from colorama import Fore, Style # Import necessary colorama components

def log(filename):
    """Tails a log file and prints new lines, looking for a specific pattern."""
    try:
        # Wait for the log file to exist
        print(Style.DIM + f"Waiting for log file '{filename}'...")
        while not os.path.isfile(filename):
            time.sleep(0.1)
        print(Style.DIM + f"Log file '{filename}' found.")

        with open(filename, 'r') as log_file:
            # Go to the end of the file
            log_file.seek(0, 2)
            print(Style.DIM + "Tailing log file...")
            while True:
                line = log_file.readline()
                if not line:
                    time.sleep(0.1) # Wait a bit if no new lines
                    continue

                print(Style.DIM + line.strip()) # Print line with dim style

                # Check for specific patterns
                if "Now listening on:" in line:
                    try:
                        local_address = line.split("Now listening on: ")[1].strip()
                        print(Fore.GREEN + f"\nApplication running at: {local_address}")
                    except IndexError:
                         print(Fore.YELLOW + "Could not parse 'Now listening on:' line.")
                # Add other patterns to look for if needed

    except FileNotFoundError:
        print(Fore.RED + f"Error: Log file not found after waiting: {filename}")
    except Exception as e:
        print(Fore.RED + f"An error occurred while tailing the log file: {e}")


def run_dotnet_watch():
    """Starts the Redis container and the .NET API with dotnet watch."""
    print(Fore.CYAN + "Attempting to start Redis container and .NET API...")
    try:
        # Start Redis container (only if not running)
        print(Fore.CYAN + "Checking Redis container status...")
        # Use docker inspect for a more direct check of container existence and state
        try:
            inspect_result = subprocess.run(["docker", "inspect", "-f", "{{.State.Running}}", "redis"], capture_output=True, text=True, check=True)
            is_redis_running = inspect_result.stdout.strip().lower() == "true"
        except subprocess.CalledProcessError:
            is_redis_running = False # Container doesn't exist or inspection failed

        if not is_redis_running:
            print(Fore.CYAN + "Redis container not running. Starting...")
            try:
                subprocess.run(["docker", "run", "--name", "redis", "-p", "6379:6379", "-d", "redis"], check=True)
                print(Fore.GREEN + "Redis container started successfully.")
                # Add a small delay to allow Redis to start up
                time.sleep(2)
            except subprocess.CalledProcessError as e:
                 print(Fore.RED + f"Error starting Redis container: {e}")
                 # Attempt to remove a potentially exited container with the same name
                 print(Fore.YELLOW + "Attempting to remove potentially existing stopped 'redis' container...")
                 subprocess.run(["docker", "rm", "redis"], stderr=subprocess.DEVNULL) # Ignore errors if rm fails
                 # Try running again
                 try:
                      subprocess.run(["docker", "run", "--name", "redis", "-p", "6379:6379", "-d", "redis"], check=True)
                      print(Fore.GREEN + "Redis container started successfully after removal attempt.")
                 except subprocess.CalledProcessError as e2:
                      print(Fore.RED + f"Error starting Redis container after removal attempt: {e2}")
                      print(Fore.RED + "Cannot proceed without Redis. Exiting .NET startup.")
                      return # Exit this function

        else:
            print(Fore.YELLOW + "Redis container is already running.")

        # Start .NET API with dotnet watch
        print(Fore.GREEN + "Starting .NET API with dotnet watch...")
        # Assuming api/AppHost is relative to the script runner
        app_host_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "api", "AppHost") # Adjust path
        if not os.path.exists(app_host_path):
             print(Fore.RED + f"Error: .NET AppHost project not found at {app_host_path}")
             return

        # Create logs directory if it doesn't exist
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs") # Adjust path
        os.makedirs(logs_dir, exist_ok=True)
        log_file_path = os.path.join(logs_dir, "foodremedy_api.log")

        # Open the log file for writing the process output
        # Use a different file handle for subprocess stdout to avoid tailing its own writes
        with open(log_file_path, "w") as log_output_file:
             dotnet_process = subprocess.Popen(
                 ["dotnet", "watch", "run", "--project", app_host_path],
                 stdout=log_output_file, # Redirect stdout to the log file
                 stderr=subprocess.STDOUT, # Redirect stderr to the same log file
                 text=True # Use text mode for file writing
             )

        print(Fore.GREEN + f"dotnet watch process started. Output redirected to {log_file_path}")
        print(Fore.YELLOW + "Press Ctrl+C to terminate the .NET process and Redis container.")

        # Start tailing the log file in the current process
        # This is blocking until Ctrl+C
        log(log_file_path) # Call the log tailing function

    except FileNotFoundError:
        print(Fore.RED + "\nError: dotnet command not found. Please ensure .NET SDK is installed and in your system's PATH.")
    except Exception as e:
        print(Fore.RED + f"\nAn unexpected error occurred: {e}")
    finally:
        # This block executes when run_dotnet_watch exits normally (e.g., after Ctrl+C is caught by log)
        # Need to ensure cleanup happens even if the log function doesn't catch Ctrl+C itself,
        # or if another error occurs before the log function is fully entered.
        # A signal handler for SIGINT might be more robust in the main run_script.py
        # For now, let's assume the main loop or log function handles Ctrl+C and exits this function.
        # If the process was started, attempt to terminate it here.
        # Clean up logic is complex with Popen; the original script used pkill, which is system-dependent.
        # A better way is to manage the process object.

        # --- Cleanup Logic (more robust than pkill) ---
        print(Fore.YELLOW + "\nAttempting to stop .NET process and Redis container...")
        try:
            # Try to stop the .NET process gracefully
            if 'dotnet_process' in locals() and dotnet_process.poll() is None: # Check if process exists and is still running
                 print(Fore.CYAN + "Terminating dotnet watch process...")
                 dotnet_process.terminate()
                 try:
                      dotnet_process.wait(timeout=5) # Wait for a few seconds for graceful exit
                 except subprocess.TimeoutExpired:
                      print(Fore.YELLOW + "dotnet watch process did not terminate gracefully, killing...")
                      dotnet_process.kill() # Force kill if not terminated

            # Stop and remove Redis container
            print(Fore.CYAN + "Stopping and removing Redis container...")
            # docker stop can take a name or ID
            subprocess.run(["docker", "stop", "redis"], check=False, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL) # Don't check=True as it fails if not running
            subprocess.run(["docker", "rm", "redis"], check=False, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL) # Don't check=True as it fails if not exists
            print(Fore.GREEN + "Cleanup complete.")

        except FileNotFoundError:
             print(Fore.RED + "Docker command not found during cleanup. Ensure Docker is installed.")
        except Exception as e:
             print(Fore.RED + f"An error occurred during cleanup: {e}")


def trust_dotnet_dev_cert():
    """Runs dotnet dev-certs https --trust."""
    try:
        print(Fore.GREEN + "Running dotnet command to trust the HTTPS development certificate...")
        subprocess.run(["dotnet", "dev-certs", "https", "--trust"], check=True)
        print(Fore.GREEN + "dotnet development certificate trusted.")
    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError running dotnet command: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: dotnet command not found. Please ensure .NET SDK is installed and in your system's PATH.")
