import csv
import json
import os
from decimal import Decimal
from backend.database import SessionLocal
from backend.models import Food


CSV_FILE = os.getenv("CSV_FILE", "/data/foodSample.csv")


def clean_code(raw: str) -> str:
    if not raw:
        return ""
    s = raw.strip().lstrip("'")
    try:
        return str(int(Decimal(s)))
    except:
        return s


def parse_array(text: str) -> list[str]:
    if text and text.startswith("[") and text.endswith("]"):
        return json.loads(text.replace("'", '"'))
    return []


def parse_json(text: str) -> dict:
    try:
        return json.loads(text)
    except:
        return {}


def main():
    db = SessionLocal()
    # start fresh every time
    db.query(Food).delete()
    db.commit()

    with open(CSV_FILE, newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        if reader.fieldnames is None:
            raise RuntimeError("No headers in " + CSV_FILE)
        # strip BOM
        reader.fieldnames = [h.lstrip("\ufeff") for h in reader.fieldnames]

        for row in reader:
            code = clean_code(row["code"])
            if not code:
                continue

            food = Food(
                code=code,
                brands=row.get("brands", ""),
                product_name=row.get("product_name", ""),
                additives_tags=parse_array(row.get("additives_tags", "[]")),
                allergens_tags=parse_array(row.get("allergens_tags", "[]")),
                generic_name=row.get("generic_name", ""),
                ingredients_text=row.get("ingredients_text", ""),
                nutriments=parse_json(row.get("nutriments", "{}")),
                quantity=row.get("quantity", ""),
                serving_size=row.get("serving_size", ""),
            )
            db.add(food)

        db.commit()
        print("Imported simplified data.")


if __name__ == "__main__":
    main()
