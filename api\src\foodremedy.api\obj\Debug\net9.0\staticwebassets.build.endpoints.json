{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/custom-swagger.6rc0bqxwf5.css", "AssetFile": "css/custom-swagger.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001470588235"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "ETag", "Value": "W/\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rc0bqxwf5"}, {"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}, {"Name": "label", "Value": "css/custom-swagger.css"}]}, {"Route": "css/custom-swagger.6rc0bqxwf5.css", "AssetFile": "css/custom-swagger.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1578"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rc0bqxwf5"}, {"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}, {"Name": "label", "Value": "css/custom-swagger.css"}]}, {"Route": "css/custom-swagger.6rc0bqxwf5.css.gz", "AssetFile": "css/custom-swagger.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6rc0bqxwf5"}, {"Name": "integrity", "Value": "sha256-AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M="}, {"Name": "label", "Value": "css/custom-swagger.css.gz"}]}, {"Route": "css/custom-swagger.css", "AssetFile": "css/custom-swagger.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001470588235"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "ETag", "Value": "W/\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}]}, {"Route": "css/custom-swagger.css", "AssetFile": "css/custom-swagger.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1578"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kuNKoplzpjpdTUsU1VxmYIe5W3wDaJ4T2TA2Saa79GM="}]}, {"Route": "css/custom-swagger.css.gz", "AssetFile": "css/custom-swagger.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AVRBIv8xW+x2ITa9KYNy3zsQRZUB1TsTu0HF/3xSz2M="}]}, {"Route": "css/styles.css", "AssetFile": "css/styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002032520325"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "ETag", "Value": "W/\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}]}, {"Route": "css/styles.css", "AssetFile": "css/styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1426"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}]}, {"Route": "css/styles.css.gz", "AssetFile": "css/styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo="}]}, {"Route": "css/styles.rptzmrj8w9.css", "AssetFile": "css/styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002032520325"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "ETag", "Value": "W/\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rptzmrj8w9"}, {"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}, {"Name": "label", "Value": "css/styles.css"}]}, {"Route": "css/styles.rptzmrj8w9.css", "AssetFile": "css/styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1426"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rptzmrj8w9"}, {"Name": "integrity", "Value": "sha256-0SzfYtLvLpqzJwZvYaMKSIKYgvGdaQOlp8fMGr+PKHU="}, {"Name": "label", "Value": "css/styles.css"}]}, {"Route": "css/styles.rptzmrj8w9.css.gz", "AssetFile": "css/styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "491"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rptzmrj8w9"}, {"Name": "integrity", "Value": "sha256-+XW9NLn24ZnemgASj91OEIIXRs2/S0TiISJh1U2pKTo="}, {"Name": "label", "Value": "css/styles.css.gz"}]}, {"Route": "images/Logo.b6gw5pqj59.png", "AssetFile": "images/Logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7437"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b6gw5pqj59"}, {"Name": "integrity", "Value": "sha256-HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8="}, {"Name": "label", "Value": "images/Logo.png"}]}, {"Route": "images/Logo.png", "AssetFile": "images/Logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7437"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HV0/b/RuQiu2xYC67R3MVQU1vaKVdWOmuAq/dAa97H8="}]}, {"Route": "index.3wvalnw33v.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000601684717"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "ETag", "Value": "W/\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3wvalnw33v"}, {"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.3wvalnw33v.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6382"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3wvalnw33v"}, {"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.3wvalnw33v.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3wvalnw33v"}, {"Name": "integrity", "Value": "sha256-tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000601684717"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "ETag", "Value": "W/\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6382"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DY+dhNFEUJrCJ86WPHgCkdAz4BB0Tj0HHTLTihNrsIQ="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1661"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tLi+Q/Y7TvJ+1AAwaLp63jZf8ump5LE1BiB5pJH7KYA="}]}, {"Route": "swagger-custom.8yjfewal8n.js", "AssetFile": "swagger-custom.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001051524711"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "ETag", "Value": "W/\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yjfewal8n"}, {"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}, {"Name": "label", "Value": "swagger-custom.js"}]}, {"Route": "swagger-custom.8yjfewal8n.js", "AssetFile": "swagger-custom.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2689"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yjfewal8n"}, {"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}, {"Name": "label", "Value": "swagger-custom.js"}]}, {"Route": "swagger-custom.8yjfewal8n.js.gz", "AssetFile": "swagger-custom.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8yjfewal8n"}, {"Name": "integrity", "Value": "sha256-dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs="}, {"Name": "label", "Value": "swagger-custom.js.gz"}]}, {"Route": "swagger-custom.js", "AssetFile": "swagger-custom.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001051524711"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "ETag", "Value": "W/\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}]}, {"Route": "swagger-custom.js", "AssetFile": "swagger-custom.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2689"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:13:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wEAsO8DN4E5aP1A9/5BtRpmyaMJmLdstcMfSSBacws0="}]}, {"Route": "swagger-custom.js.gz", "AssetFile": "swagger-custom.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "950"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 09:26:48 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dBUWbdmeAc+q6zu750MQ89KROKXMaFRGF3Ifj6evwVs="}]}]}