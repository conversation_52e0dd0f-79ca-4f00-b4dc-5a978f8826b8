using Microsoft.AspNetCore.Mvc;
using foodremedy.api.Models.Requests;
using foodremedy.api.Models.Responses;
using foodremedy.database.Models;
using foodremedy.api.Extensions;
using foodremedy.api.Repositories;
using System.Net;
using Microsoft.AspNetCore.Authorization;

using ResponseAllergy = foodremedy.api.Models.Responses.Allergy;

namespace foodremedy.api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status500InternalServerError)]
public class AllergyController : ControllerBase
{
    private readonly IAllergyRepository _allergyRepository;

    public AllergyController(IAllergyRepository allergyRepository)
    {
        _allergyRepository = allergyRepository;
    }

    [HttpGet]
    [Authorize(Policy = "UserOnly")]
    public async Task<ActionResult<IEnumerable<ResponseAllergy>>> GetAllAllergies()
    {
        var results = await _allergyRepository.GetAsync();
        return Ok(results.ToResponseModel(p => p.ToResponseModel()));
    }


    [HttpGet("{id:guid}")]
    [Authorize(Policy = "UserOnly")]
    public async Task<ActionResult<ResponseAllergy>> GetAllergy(Guid id)
    {
        var allergy = await _allergyRepository.GetByIdAsync(id);

        if (allergy == null)
            return NotFound();

        return Ok(allergy.ToResponseModel());
    }

    [HttpPost]
    [Authorize(Policy = "UserOnly")]
    [ProducesResponseType(typeof(ResponseAllergy), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<ActionResult<ResponseAllergy>> CreateAllergy([FromBody] CreateAllergy request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var existing = await _allergyRepository.GetByNameAsync(request.Name);
        if (existing != null)
            return Conflict(new { message = $"An allergy with the name '{request.Name}' already exists." });

        foodremedy.database.Models.Allergy allergy = new(request.Name, request.Symptoms)
        {
            Id = Guid.NewGuid()
        };

        _allergyRepository.Add(allergy);
        await _allergyRepository.SaveChangesAsync();

        return CreatedAtAction(nameof(GetAllergy), new { id = allergy.Id }, allergy.ToResponseModel());
    }

    [HttpPut("{id:guid}")]
    [Authorize(Policy = "UserOnly")]
    public async Task<IActionResult> UpdateAllergy(Guid id, [FromBody] UpdateAllergy updateAllergy)
    {
        if (updateAllergy == null || id != updateAllergy.Id)
            return BadRequest(new { message = "Invalid request or ID mismatch" });

        var existing = await _allergyRepository.GetByIdAsync(id);
        if (existing == null)
            return NotFound();

        if (existing.Name == updateAllergy.Name && existing.Description == updateAllergy.Symptoms)
            return BadRequest(new { message = "No changes detected to update." });

        existing.Name = updateAllergy.Name;
        existing.Description = updateAllergy.Symptoms;

        _allergyRepository.Update(existing);
        await _allergyRepository.SaveChangesAsync();

        return Ok(new { message = $"Allergy {updateAllergy.Name} updated successfully." });

    }

    [HttpDelete("{id:guid}")]
    [Authorize(Policy = "UserOnly")]
    public async Task<IActionResult> DeleteAllergy(Guid id)
    {
        var allergy = await _allergyRepository.GetByIdAsync(id);
        if (allergy == null)
            return NotFound(new { message = $"Allergy with ID {id} not found." });

        _allergyRepository.Remove(allergy);
        await _allergyRepository.SaveChangesAsync();

        return Ok(new { message = $"Allergy {allergy.Name} deleted successfully." });
    }
}
