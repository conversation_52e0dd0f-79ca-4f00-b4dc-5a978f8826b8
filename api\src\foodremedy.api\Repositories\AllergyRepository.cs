using foodremedy.database.Models;
using foodremedy.database;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Threading.Tasks;

namespace foodremedy.api.Repositories
{
    public interface IAllergyRepository
    {
        Task<PaginatedResult<Allergy>> GetAsync(int skip = 0, int take = 20);
        Allergy Add(Allergy allergy);
        Allergy Update(Allergy allergy);
        void Remove(Allergy allergy);
        Task SaveChangesAsync();
        Task<Allergy?> GetByIdAsync(Guid id);
        Task<Allergy?> GetByNameAsync(string name);
    }
}

namespace foodremedy.api.Repositories
{
    public class AllergyRepository : IAllergyRepository
    {
        private readonly FoodRemedyDbContext _dbContext;

        public AllergyRepository(FoodRemedyDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<PaginatedResult<Allergy>> GetAsync(int skip = 0, int take = 20)
        {
            List<Allergy> result = await _dbContext.Allergies
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            return new PaginatedResult<Allergy>(result.Count, _dbContext.Allergies.Count(), result);
        }

        public Allergy Add(Allergy allergy)
        {
            return _dbContext.Allergies.Add(allergy).Entity;
        }

        public Allergy Update(Allergy allergy)
        {
            return _dbContext.Allergies.Update(allergy).Entity;
        }

        public void Remove(Allergy allergy)
        {
            _dbContext.Allergies.Remove(allergy);
        }

        public async Task<Allergy?> GetByIdAsync(Guid id)
        {
            return await _dbContext.Allergies.SingleOrDefaultAsync(a => a.Id == id);
        }

        public async Task<Allergy?> GetByNameAsync(string name)
        {
            return await _dbContext.Allergies.SingleOrDefaultAsync(a => a.Name == name);
        }

        public async Task SaveChangesAsync()
        {
            await _dbContext.SaveChangesAsync();
        }
    }
}
