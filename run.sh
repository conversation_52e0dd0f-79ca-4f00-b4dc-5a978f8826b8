#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Change directory to the script's location if necessary
# cd /path/to/your/project

# Activate the virtual environment
echo "Activating virtual environment..."
source .venv/bin/activate

# Check if activation was successful (optional but good practice)
if [ -z "$VIRTUAL_ENV" ]; then
    echo "Failed to activate virtual environment!"
    exit 1
fi
echo "Virtual environment activated."

# Run your Python script using the activated environment's python interpreter
echo "Running run_script.py..."
python run_script.py

# Deactivate the environment when done (optional)
# deactivate

echo "<PERSON><PERSON><PERSON> finished."
