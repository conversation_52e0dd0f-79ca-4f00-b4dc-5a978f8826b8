// TabLayout.tsx (app/(tabs)/_layout.tsx)

import { Tabs } from 'expo-router';
import IconNavigation from '@/components/icons/IconNavigation';
import { PlatformPressable } from '@react-navigation/elements';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#FF3F3F',
        tabBarInactiveTintColor: 'hsl(0 0% 30%)',
        tabBarLabelStyle: {
          fontSize: 10,
        },
        tabBarStyle: {
          borderTopWidth: 0,
          backgroundColor: 'hsl(0 0% 95%)',
        },
        // Disable OnPress Ripple Effect (Android) by Passing Specific Pressable
        tabBarButton: (props) => (
          <PlatformPressable   {...props} android_ripple={{ color: 'transparent' }} pressOpacity={1} />
        ),
      }}
    >
      <Tabs.Screen
        name="scan"
        options={{
          title: 'Scan',
          tabBarIcon: ({ color }) => (
            <IconNavigation type="scan" size={30} fill={color} />
          ),
        }}
      />

      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => (
            <IconNavigation type="home" size={30} fill={color} />
          ),
        }}
      />

      <Tabs.Screen
        name="history"
        options={{
          title: 'History',
          tabBarIcon: ({ color }) => (
            <IconNavigation type="history" size={30} fill={color} />
          ),
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => (
            <IconNavigation type="profile" size={30} fill={color} />
          ),
        }}
      />
    </Tabs>
  );
}
