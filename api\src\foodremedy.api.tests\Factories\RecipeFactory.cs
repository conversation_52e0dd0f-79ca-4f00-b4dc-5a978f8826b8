using foodremedy.api.Models.Requests;
using foodremedy.database;
using Recipy = foodremedy.database.Models.Recipy;

namespace foodremedy.api.tests.Factories;

public class RecipeFactory : IFactory
{
    const string DefaultName = "DefaultRecipe";
    const string DefaultDetails = "A default recipe description";
    const decimal DefaultCalories = 100.00m;
    const decimal DefaultFat = 5.00m;
    const decimal DefaultProtein = 10.00m;
    const decimal DefaultCarbohydrates = 15.00m;
    const string DefaultReference = "https://example.com/default-recipe";
    static int num = 0;
    public FoodRemedyDbContext Db { get; set; }

    public RecipeFactory(FoodRemedyDbContext db)
    {
        Db = db;
    }

    public async Task<IDatabaseModel> Add(dynamic? args = null)
    {
        num++;
        args = args ?? new
        {
            Name = DefaultName + num,
            Details = DefaultDetails,
            Calories = DefaultCalories,
            Fat = DefaultFat,
            Protein = DefaultProtein,
            Carbohydrates = DefaultCarbohydrates,
            Reference = DefaultReference
        };

        Recipy recipy = Db.Recipy.Add(new Recipy(
            id: Guid.NewGuid(),
            name: args.GetType().GetProperty("Name") != null ? args.Name : DefaultName + num,
            details: args.GetType().GetProperty("Details") != null ? args.Details : DefaultDetails,
            calories: args.GetType().GetProperty("Calories") != null ? (decimal?)args.Calories : DefaultCalories,
            fat: args.GetType().GetProperty("Fat") != null ? (decimal?)args.Fat : DefaultFat,
            protein: args.GetType().GetProperty("Protein") != null ? (decimal?)args.Protein : DefaultProtein,
            carbohydrates: args.GetType().GetProperty("Carbohydrates") != null ? (decimal?)args.Carbohydrates : DefaultCarbohydrates,
            reference: args.GetType().GetProperty("Reference") != null ? args.Reference : DefaultReference
        )).Entity;

        await Db.SaveChangesAsync();
        return recipy;
    }

    public IRequestModel Create(dynamic? args = null)
    {
        num++;
        args = args ?? new
        {
            Name = DefaultName + num,
            Details = DefaultDetails,
            Calories = DefaultCalories,
            Fat = DefaultFat,
            Protein = DefaultProtein,
            Carbohydrates = DefaultCarbohydrates,
            Reference = DefaultReference
        };

        return new CreateRecipy(
            Name: args.GetType().GetProperty("Name") != null ? args.Name : DefaultName + num,
            Details: args.GetType().GetProperty("Details") != null ? args.Details : DefaultDetails,
            Calories: args.GetType().GetProperty("Calories") != null ? (decimal)args.Calories : DefaultCalories,
            Fat: args.GetType().GetProperty("Fat") != null ? (decimal)args.Fat : DefaultFat,
            Protein: args.GetType().GetProperty("Protein") != null ? (decimal)args.Protein : DefaultProtein,
            Carbohydrates: args.GetType().GetProperty("Carbohydrates") != null ? (decimal)args.Carbohydrates : DefaultCarbohydrates,
            Reference: args.GetType().GetProperty("Reference") != null ? args.Reference : DefaultReference
        );
    }
}