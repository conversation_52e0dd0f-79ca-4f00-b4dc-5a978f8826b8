import subprocess
import os
import time
import shutil
import sys
from colorama import Fore, Style # Import necessary colorama components
from .utils import menu_with_arrows_and_numbers, center_text # Import menu helpers from utils

def print_tables_in_database():
    """Fetches and prints tables in the 'foodremedy' database."""
    try:
        print(Fore.GREEN + "Fetching tables in the 'foodremedy' database...")
        # Assuming docker-compose.yml is in the parent directory or project root
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", "SHOW TABLES;", "foodremedy"],
            capture_output=True, text=True, check=True
        )
        print(Fore.GREEN + "\nTables in the 'foodremedy' database:")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        if b"service \"db\" is not running" in e.stderr:
            print(Fore.RED + "\nError: The 'db' database service is not running. Please start it (e.g., via the Docker menu).")
        elif b"Unknown database 'foodremedy'" in e.stderr:
            print(Fore.RED + "\nError: Database 'foodremedy' not found. Ensure it's created in your setup.")
        else:
            print(Fore.RED + f"\nError fetching tables: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")


def print_table_data_in_database():
    """Fetches tables, prompts user to select one, and prints its data."""
    try:
        print(Fore.GREEN + "Fetching tables in the 'foodremedy' database...")
        # Assuming docker-compose.yml is in the parent directory or project root
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", "SHOW TABLES;", "foodremedy"],
            capture_output=True, text=True, check=True
        )
        tables = []
        # Split lines and skip the header
        for line in result.stdout.strip().splitlines():
            if line != "Tables_in_foodremedy":
                tables.append(line)

        if not tables:
             print(Fore.YELLOW + "\nNo tables found in the 'foodremedy' database.")
             return

        print(Fore.GREEN + "\nTables in the 'foodremedy' database:")
        # Use the menu utility for table selection
        table_options = tables + ["Back"]
        choice_index = menu_with_arrows_and_numbers(table_options)

        if choice_index == len(tables): # "Back" option selected
            return

        selected_table = tables[choice_index]

        print(Fore.GREEN + f"\nFetching data from table '{selected_table}'...")
        result = subprocess.run(
            ["docker-compose", "exec", "db", "mysql", "-uroot", "-ppassword", "-e", f"SELECT * FROM `{selected_table}`;", "foodremedy"], # Quote table name for safety
            capture_output=True, text=True, check=True
        )

        if not result.stdout.strip():
            print(Fore.YELLOW + f"\nNo data in the table '{selected_table}'.")
        else:
            print(Fore.GREEN + "\nData in the table:")
            print(result.stdout)

    except subprocess.CalledProcessError as e:
        if b"service \"db\" is not running" in e.stderr:
            print(Fore.RED + "\nError: The 'db' database service is not running. Please start it (e.g., via the Docker menu).")
        elif b"Unknown database 'foodremedy'" in e.stderr:
            print(Fore.RED + "\nError: Database 'foodremedy' not found. Ensure it's created in your setup.")
        elif b"Table 'foodremedy." in e.stderr and b"doesn't exist" in e.stderr:
             print(Fore.RED + f"\nError: The selected table does not exist.") # Should not happen if tables list is accurate, but good practice
        else:
            print(Fore.RED + f"\nError fetching tables or data: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")


def populate_database():
    """Populates the database using a specified SQL file."""
    try:
        print(Fore.GREEN + "Populating the database with data from the sql file...")
        # Assuming sql file is in a 'database/code' directory relative to the script runner
        sql_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "database", "code", "db_sampledata_Active.sql") # Adjust path
        if not os.path.exists(sql_file):
            print(Fore.YELLOW + f"\nSql file not found: {sql_file}")
            return

        print(Fore.GREEN + f"Using sql file: {sql_file}")

        # Execute the SQL file content directly via mysql command
        # This is generally more robust than splitting by ';' in Python
        # Assuming docker-compose.yml is in the parent directory or project root
        with open(sql_file, "r") as f:
            subprocess.run(
                ["docker-compose", "exec", "-T", "db", "mysql", "-uroot", "-ppassword", "foodremedy"], # -T disables pseudo-tty allocation
                stdin=f, # Pipe the file content to stdin of the mysql command
                check=True
            )

        print(Fore.GREEN + "\nDatabase has been populated with data.")

    except subprocess.CalledProcessError as e:
        if b"service \"db\" is not running" in e.stderr:
            print(Fore.RED + "\nError: The 'db' database service is not running. Please start it (e.g., via the Docker menu).")
        elif b"Unknown database 'foodremedy'" in e.stderr:
             print(Fore.RED + "\nError: Database 'foodremedy' not found. Ensure it's created in your setup.")
        else:
            print(Fore.RED + f"\nError populating the database: {e}")
            print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: docker-compose command not found. Please ensure Docker Compose is installed and in your system's PATH.")
    except IOError as e:
        print(Fore.RED + f"\nError reading SQL file: {e}")


def run_migrations():
    """Runs database migrations using a separate script."""
    try:
        print(Fore.GREEN + "Running migrations for the database...")
        # Assuming db_migrate.py is in the parent directory or project root
        migrate_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "db_migrate.py") # Adjust path
        if not os.path.exists(migrate_script):
            print(Fore.RED + f"Error: Migration script not found at {migrate_script}")
            return

        # Ensure we use the same Python environment (likely the activated venv)
        subprocess.run([sys.executable, migrate_script, "update"], check=True)
        print(Fore.GREEN + "Database migrations completed.")

    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError running migration: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Python interpreter or migration script not found. Ensure Python is installed and the script exists.")


def create_new_migration():
    """Creates a new migration using a separate script."""
    try:
        print(Fore.GREEN + "Creating a new migration...")
        name = input(Fore.GREEN + "Enter the name of the migration: ")
        if not name:
             print(Fore.YELLOW + "Migration name cannot be empty.")
             return

        # Assuming db_migrate.py is in the parent directory or project root
        migrate_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "db_migrate.py") # Adjust path
        if not os.path.exists(migrate_script):
             print(Fore.RED + f"Error: Migration script not found at {migrate_script}")
             return

        # Ensure we use the same Python environment (likely the activated venv)
        subprocess.run([sys.executable, migrate_script, "add", name], check=True)
        print(Fore.GREEN + f"New migration '{name}' created (check your migration files).")

    except subprocess.CalledProcessError as e:
        print(Fore.RED + f"\nError creating migration: {e}")
        print(Fore.RED + f"Stderr: {e.stderr}")
    except FileNotFoundError:
        print(Fore.RED + "\nError: Python interpreter or migration script not found. Ensure Python is installed and the script exists.")


def remove_migrations_folder():
    """Removes the 'Migrations' folder if it exists."""
    try:
        print(Fore.GREEN + "Removing Migrations folder if it exists...")
        # Search for 'Migrations' folder starting from the directory containing the script runner
        # This assumes Migrations is typically relative to the project root where run_script.py is
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Go up two levels from db_ops.py
        found = False
        for root, dirs, files in os.walk(project_root):
            if "Migrations" in dirs:
                migrations_folder = os.path.join(root, "Migrations")
                print(Fore.GREEN + f"Found and removing folder: {migrations_folder}")
                shutil.rmtree(migrations_folder)
                print(Fore.GREEN + f"Folder removed: {migrations_folder}")
                found = True
                # We could continue searching or stop after the first one depending on project structure
                # For typical .NET EF Core, there's usually only one in the project root or a specific project dir
                # If there could be multiple, remove the 'break' below.
                # break # Assuming only one primary Migrations folder

        if not found:
            print(Fore.YELLOW + "No 'Migrations' folder found.")

    except OSError as e:
        print(Fore.RED + f"\nError removing migrations folder: {e}")
