#!/usr/bin/env python3

import sys
import os
import subprocess
import time
import shutil # Import shutil to check for executable

# --- Bootstrap Logic: Ensure Environment and Essential Packages ---

# Function to check if running in a virtual environment (basic check)
def is_virtual_env_bootstrap():
    """Basic check if the script is running inside a virtual environment."""
    return (hasattr(sys, 'real_prefix') or
            (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))

# Function to create a virtual environment
def create_virtual_env_bootstrap():
    """Creates a virtual environment if not already in one."""
    if not is_virtual_env_bootstrap():
        project_root = os.path.dirname(os.path.abspath(__file__))
        venv_dir = os.path.join(project_root, ".venv")

        print("Not in a virtual environment.")
        print(f"Checking for virtual environment at: {venv_dir}")

        if not os.path.exists(venv_dir):
            print("Virtual environment not found. Creating...")
            try:
                subprocess.check_call([sys.executable, "-m", "venv", venv_dir])
                print("Virtual environment created.")
                print(f"Please activate it by running: source {venv_dir}/bin/activate")
                return False # Venv created, needs activation
            except subprocess.CalledProcessError as e:
                 print(f"Error creating virtual environment: {e}", file=sys.stderr)
                 sys.exit(1) # Exit on venv creation failure
        else:
            # Venv exists but not active
            print("Virtual environment already exists.")
            print(f"It is not active. Please activate it by running: source {venv_dir}/bin/activate")
            return False # Exists but not active
    else:
        # Already in a virtual environment
        pass # Already active, continue
    return True # Venv is active

# Function to install a package using pip in the current environment
def install_package_bootstrap(package):
    """Installs a Python package using pip in the current environment."""
    print(f"Installing package: {package}")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"Installation command for {package} finished.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing package {package}: {e}", file=sys.stderr)
        return False


# --- Main Bootstrap Steps ---

# 1. Check and ensure the virtual environment is active.
if not create_virtual_env_bootstrap():
    sys.exit(0) # Exit here, user must reactivate and re-run

# We are now confirmed (or the user re-ran after activating) to be in the venv.

# 2. Check if essential Python packages are installed. Install if necessary.
# REMOVED 'pygithub' from this list
essential_packages = ["colorama", "psutil"]
packages_needed_re_run = False

print("Checking for essential Python packages...")

for package in essential_packages:
    try:
        __import__(package)
    except ImportError:
        print(f"Essential package '{package}' not found in virtual environment.")
        print(f"Attempting to install '{package}'...")
        if not install_package_bootstrap(package):
            print(f"CRITICAL ERROR: Failed to install {package}. Cannot proceed. Exiting.")
            sys.exit(1)

        try:
            __import__(package)
            print(f"Successfully imported '{package}' after installation attempt.")
            packages_needed_re_run = True
        except ImportError:
            print(f"CRITICAL ERROR: Package '{package}' installed but still cannot be imported.", file=sys.stderr)
            print("Please check your Python/venv setup and package integrity.", file=sys.stderr)
            sys.exit(1)


if packages_needed_re_run:
    print("\nInstalled missing packages. Please re-run the script to start the application.")
    sys.exit(0)
else:
    print("All essential Python packages found.")

# 3. Check if essential external tools are installed (like git).
print("Checking for external tools...")
required_tools = ["git"]
tools_missing = False
for tool in required_tools:
    if shutil.which(tool) is None:
        print(f"Required tool '{tool}' not found in system PATH.", file=sys.stderr)
        print(f"Please install '{tool}' and ensure it's accessible from your terminal.", file=sys.stderr)
        tools_missing = True

if tools_missing:
    sys.exit(1)
else:
    print("All required external tools found.")


# --- If we reach here, venv is active, Python packages & external tools are found ---

# 4. Initialize colorama now that it's confirmed installed
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
except ImportError as e:
    print(f"CRITICAL ERROR: Colorama installed but cannot be imported for initialization: {e}", file=sys.stderr)
    sys.exit(1)


# 5. Add the application package directory to sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 6. Import and run the main application logic
try:
    from foodremedy_cli.menus import main_menu

    # Start the main application menu
    main_menu()

except ImportError as e:
    print(Fore.RED + f"Failed to import core application modules from foodremedy_cli: {e}")
    print(Fore.YELLOW + "Please ensure the 'foodremedy_cli' directory exists and contains the necessary files.")
    print(Fore.YELLOW + "Also verify your virtual environment is correctly activated.")
    sys.exit(1)
except Exception as e:
    error_message = f"An unexpected error occurred during application execution: {e}"
    if 'Fore' in locals():
        print(Fore.RED + error_message)
    else:
        print(error_message, file=sys.stderr)
    sys.exit(1)
