# foodremedy_cli/menus.py
import os
import sys
import time
from colorama import Fore, Style # Keep colorama imports

from .utils import menu_with_arrows_and_numbers, center_text

from .app_runner import run_dotnet_watch, trust_dotnet_dev_cert
from .docker_ops import (
    run_docker_db_daemon, stop_docker_db, check_docker_status,
    remove_docker_container, list_docker, remove_docker_image,
    remove_docker_volume
)
from .db_ops import (
    print_tables_in_database, print_table_data_in_database,
    populate_database, run_migrations, create_new_migration,
    remove_migrations_folder
)

# REMOVE THE IMPORT FROM . import github_ops

# IMPORT THE NEW GIT OPS MODULE
from . import git_ops


def main_menu():
    """Displays the main menu, centered."""
    while True:
        os.system("clear")
        width = os.get_terminal_size().columns
        print(Fore.GREEN + center_text("\nConsole Application Menu:", width))
        main_options = [
            ".NET Application Menu",
            "Docker Menu",
            "Database Menu",
            "Git Tools Menu", # RENAME/CHANGE THIS OPTION
            "Exit"
        ]

        main_choice = menu_with_arrows_and_numbers(main_options)

        if main_choice == 0:
            net_app_menu()
        elif main_choice == 1:
            docker_menu()
        elif main_choice == 2:
            database_menu()
        elif main_choice == 3: # ADJUST INDEX FOR THE NEW MENU
            git_tools_menu() # CALL THE NEW GIT MENU
        elif main_choice == 4: # ADJUST INDEX FOR EXIT
            print(Fore.GREEN + center_text("Exiting the application.", width))
            break


# --- REMOVE THE github_menu FUNCTION ENTIRELY ---


# --- Add the new git_tools_menu function ---

def git_tools_menu():
    """Displays the Git Tools menu, centered."""
    while True:
        os.system("clear")
        width = os.get_terminal_size().columns
        print(Fore.GREEN + center_text("\nGit Tools Menu:", width))
        git_options = [
            "View Git Log",
            "Check Git Status",
            "Back"
        ]
        git_choice = menu_with_arrows_and_numbers(git_options)

        if git_choice == 0: # Index for View Log
            print(Fore.CYAN + center_text("Viewing Git Log...", width))
            git_ops.get_git_log() # Call function from git_ops
            input(Fore.GREEN + center_text("\nPress Enter to return to Git Tools Menu...", width))
        elif git_choice == 1: # Index for Check Status
            print(Fore.CYAN + center_text("Checking Git Status...", width))
            git_ops.get_git_status() # Call function from git_ops
            input(Fore.GREEN + center_text("\nPress Enter to return to Git Tools Menu...", width))
        elif git_choice == 2: # Index for Back
            print(Fore.GREEN + center_text("Returning to Main Menu...", width))
            break # Exit the git tools menu loop


# --- Existing menu functions (net_app_menu, docker_menu, database_menu) follow below ---
# ... (copy/paste the existing net_app_menu, docker_menu, database_menu functions here) ...

def net_app_menu():
    """Displays the .NET Application menu, centered."""
    while True:
        os.system("clear")
        width = os.get_terminal_size().columns
        print(Fore.GREEN + center_text("\n.NET Application Menu:", width))
        dotnet_options = [
            "Run .NET Application with dotnet watch (assuming database container 'db' is already running)",
            "Trust Dev Certification to the HTTPS development certificate",
            "Back"
        ]
        dotnet_choice = menu_with_arrows_and_numbers(dotnet_options)
        if dotnet_choice == 0:
            print(Fore.CYAN + center_text("Running .NET Application...", width))
            run_dotnet_watch()
            time.sleep(1)
        elif dotnet_choice == 1:
            print(Fore.CYAN + center_text("Trusting Dev Certification...", width))
            trust_dotnet_dev_cert()
            input(Fore.GREEN + center_text("\nPress Enter to return to .NET Application Menu...", width))
        elif dotnet_choice == 2:
            print(Fore.GREEN + center_text("Returning to Main Menu...", width))
            break

def docker_menu():
    """Displays the Docker menu, centered."""
    while True:
        os.system("clear")
        width = os.get_terminal_size().columns
        print(Fore.GREEN + center_text("\nDocker Menu:", width))
        docker_options = [
            "Run Docker container 'db' in daemon mode",
            "Stop Docker container 'db'",
            "Check Docker Container Status",
            "Remove Docker container 'db'",
            "List Docker Containers, Images, and Volumes",
            "Remove a Docker image",
            "Remove a Docker volume",
            "Back"
        ]
        docker_choice = menu_with_arrows_and_numbers(docker_options)
        if docker_choice == 0:
            print(Fore.CYAN + center_text("Running Docker container 'db' in daemon mode...", width))
            run_docker_db_daemon()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 1:
            print(Fore.CYAN + center_text("Stopping Docker container 'db'...", width))
            stop_docker_db()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 2:
            print(Fore.CYAN + center_text("Checking Docker Container Status...", width))
            check_docker_status()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 3:
            print(Fore.CYAN + center_text("Removing Docker container 'db'...", width))
            remove_docker_container()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 4:
            print(Fore.CYAN + center_text("Listing Docker Containers, Images, and Volumes...", width))
            list_docker()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 5:
            print(Fore.CYAN + center_text("Removing a Docker image...", width))
            remove_docker_image()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 6:
            print(Fore.CYAN + center_text("Removing a Docker volume...", width))
            remove_docker_volume()
            input(Fore.GREEN + center_text("\nPress Enter to return to Docker Menu...", width))
        elif docker_choice == 7:
            print(Fore.GREEN + center_text("Returning to Main Menu...", width))
            break

def database_menu():
    """Displays the Database menu, centered."""
    while True:
        os.system("clear")
        width = os.get_terminal_size().columns
        print(Fore.GREEN + center_text("\nDatabase Menu:", width))
        database_options = [
            "Print tables in the 'foodremedy' database",
            "Print table data in the 'foodremedy' database",
            "Add data to the 'foodremedy' database", # Populates from SQL file
            "Update current tables structure in database", # Runs migrations update
            "Remove migrations folder",
            "Create new migration",
            "Back"
        ]
        database_choice = menu_with_arrows_and_numbers(database_options)
        if database_choice == 0:
            print(Fore.CYAN + center_text("Printing tables in the 'foodremedy' database...", width))
            print_tables_in_database()
            input(Fore.GREEN + center_text("\nPress Enter to return to Database Menu...", width))
        elif database_choice == 1:
            print(Fore.CYAN + center_text("Printing table data in the 'foodremedy' database...", width))
            print_table_data_in_database()
            input(Fore.GREEN + center_text("\nPress Enter to return to Database Menu...", width))
        elif database_choice == 2:
            print(Fore.CYAN + center_text("Adding data to the 'foodremedy' database...", width))
            populate_database()
            input(Fore.GREEN + center_text("\nPress Enter to return to Database Menu...", width))
        elif database_choice == 3:
            print(Fore.CYAN + center_text("Updating current tables structure in database...", width))
            run_migrations()
            input(Fore.GREEN + center_text("\nPress Enter to return to Database Menu...", width))
        elif database_choice == 4:
            print(Fore.CYAN + center_text("Removing migrations folder...", width))
            remove_migrations_folder()
            input(Fore.GREEN + center_text("\nPress Enter to return to Database Menu...", width))
        elif database_choice == 5:
            print(Fore.CYAN + center_text("Creating new migration...", width))
            create_new_migration()
            input(Fore.GREEN + center_text("\nPress Enter to return to Database Menu...", width))
        elif database_choice == 6:
            print(Fore.GREEN + center_text("Returning to Main Menu...", width))
            break
