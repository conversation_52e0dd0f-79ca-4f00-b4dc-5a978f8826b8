﻿using System.ComponentModel.DataAnnotations;

namespace foodremedy.api.Models.Requests;

public record RegisterUser(
    [Required(AllowEmptyStrings = false)] string FirstName,
    [Required(AllowEmptyStrings = false)] string LastName,
    [Required(AllowEmptyStrings = false)][EmailAddress] string Email,
    [Required(AllowEmptyStrings = false)][StringLength(50)] string Username,
    [Required(AllowEmptyStrings = false)] string Password,
    [Required] DateTime DateCreated,
    int? Age,
    string Role = "User" //optional with default value
) : IRequestModel;
